kind: pipeline
type: docker
name: default

platform:
  os: linux
  arch: amd64

clone:
  depth: 1

steps:
  - name: restore-cache-with-filesystem
    image: meltwater/drone-cache:v1.1.0
    pull: if-not-exists
    settings:
      backend: "filesystem"
      restore: true
      cache_key: "{{ .Commit.Branch }}"
      archive_format: "gzip"
      filesystem_cache_root: "/cache"
      mount:
        - './.m2'
    volumes:
      - name: cache
        path: /cache
    when:
      ref:
        - refs/heads/**

  - name: restore-cache-with-filesystem-tags
    image: meltwater/drone-cache:v1.1.0
    pull: if-not-exists
    settings:
      backend: "filesystem"
      restore: true
      cache_key: "master"
      archive_format: "gzip"
      filesystem_cache_root: "/cache"
      mount:
        - './.m2'
    volumes:
      - name: cache
        path: /cache
    when:
      ref:
        - refs/tags/**

  - name: snapshots deploy
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
    environment:
      MAVEN_CONFIG: /drone/src/.m2
    commands:
      - mvn clean install org.apache.maven.plugins:maven-deploy-plugin:2.8:deploy -U -s /root/.m2/settings.xml -DskipTests -Dmaven.repo.local=./.m2/repository 
    when:
      branch:
        - develop

  - name: release deploy
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
    commands:
      - mvn clean install org.apache.maven.plugins:maven-deploy-plugin:2.8:deploy -U -s /root/.m2/settings.xml -Dmaven.repo.local=./.m2/repository
    when:
      ref:
        - refs/tags/**

  - name: rebuild-cache-with-filesystem
    image: meltwater/drone-cache:v1.1.0
    pull: if-not-exists
    settings:
      backend: "filesystem"
      rebuild: true
      cache_key: "{{ .Commit.Branch }}"
      archive_format: "gzip"
      filesystem_cache_root: "/cache"
      mount:
        - './.m2'
    volumes:
      - name: cache
        path: /cache
    when:
      ref:
        - refs/heads/**

  - name: notify
    image: fifsky/drone-wechat-work
    pull: if-not-exists
    settings:
      url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0accac60-9d20-446f-8e8a-4f758da26b57
      msgtype: markdown
      content: |
        {{if eq .Status "success" }}
        #### 🎉 ${DRONE_REPO} 构建成功
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > [点击查看](${DRONE_BUILD_LINK})
        {{else}}
        #### ❌ ${DRONE_REPO} 构建失败
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > 请立即修复!!!
        > [点击查看](${DRONE_BUILD_LINK})
        {{end}}
    when:
      status:
        - failure
        - success

volumes:
  - name: cache
    host:
      path: /data/drone/cache
  - name: docker-auths
    host:
      path: /root/.docker/config.json
  - name: m2-global-settings
    host:
      path: /data/drone/settings.xml
  - name: ssh_rsa
    host:
      path: /root/.ssh/id_rsa

trigger:
  ref:
    - refs/heads/**
    - refs/tags/**

image_pull_secrets:
  - dockerconfig