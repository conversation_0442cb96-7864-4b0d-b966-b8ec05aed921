## 0.1.10
* fix: criteria entity.id
* update: support in operator

## 0.1.11
* fix: test case Autowried with EnableJpaRepositories

## 0.1.12
* update: add EnterpriseSharableEntity

## 0.1.13
* update: add limiter revertConsume

## 0.1.14
* update: snowflake id

## 0.1.15
* fix: multi roleId

## 0.1.16
* update: RootAware
* update: add ResourceRepository.deleteSoft
* update: add auto config, and server property
* update: add OpenApiFilter

## 0.1.17
* fix: global filter add EnterpriseSharableEntity

## 0.1.18
* update: TenantContext add currentDepartmentId,currentSubDepartmentId,currentIsAdmin
* update: add jpa departmentFilter
* update: add StringTreeListConverter
* update: disable session
* update: controller copy @PreAuthorize annotation

## 0.1.19
* update: support application event for entity change [EntityChangeAware]

## 0.1.20
* update: support count _by

## 0.1.21
* update: support ResourceCollectionType.SINGLE_TYPE [example->ProfileController]

## 0.1.22
* fix: global filter apply to count function as well

## 0.1.24
* update: add changes to EntityChangeEvent

## 0.1.25
* update: support _q text search

## 0.1.26
* update: support batch action

## 0.1.27
* update: BaseEntity.[createTime|modifyTime] add api comment
* update: LongListConverter check string to long

## 0.1.28
* update: BaseEmbeddedController.findAll support sort 

## 0.1.29
* fix: tenant interceptor clean within request

## 0.1.30
* update: add valid exception handler [MethodArgumentNotValidException|ConstraintViolationException]

## 0.1.31
* update: add List converter

## 0.1.32
* update: findAll support _or and join 
* 
## 0.1.33
* update: EnterpriseDepartmentEntity add isnull
* fix: ParamsHelper parse subfield 

## 0.1.34
* fix: specification join cast value error
* update: support join 2 level

## 0.1.35
* update: support inner join & left join & right join
* update: updateOne support update simple list and update ManyToOne entity

## 0.1.36
* update: support proxy resource exclude methods

## 0.1.38
* update SnowflakeGenerator support setId

## 0.1.39
* update support doc tag

## 0.1.40
* update HashMapConverter support single quotes

## 0.1.41
* update searchOperator support isnull

## 0.1.42
* update entityUtility support map convert to dto

## 0.1.44
* support tree view
* support single view
* entityUtility autofill OneToOne as well

## 0.1.45
* BaseRepository override findById to check orgFilter

## 0.1.46
* class add @Validated 
* copy method params annotation

## 0.1.47
* support cacheable

## 0.1.48
* add httpResponseBody when httpStatus is 401/403

## 0.1.49
* cache evict after change Embedded 

## 0.1.50
* based on 0.1.42 fix ListConverter can't convert '\xa0'

## 0.1.51
* merge 0.1.50

## 0.1.52
* 增加枚举类型参数校验注解

## 0.1.53
* TenantContext 增加角色id

## 0.1.54
* 自定义进制转换
* TenantContext 增加permissions
* 搬迁JsonHelper到core

## 0.1.55
* 增加LimitCheckConsumer

## 0.1.56
* 注释flush Listener

## 0.1.57
* 在afterCompletion中清理TenantContext
