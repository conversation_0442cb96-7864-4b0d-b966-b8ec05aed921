# 架构
befun-task是基于Redis构建的简单分布式任务调度系统，主要支持如下功能
 - 延时任务
 - 任务队列
 - 定时任务（暂未支持）

## 延时任务
延时任务使用 redis zset 有序集合，以timestamp作为序列key，定期搬运到相对应的任务队列中. 延时任务有如下功能点
 - TaskSchedulerService->carryDelayTask 定时从 [zset] delay 搬运到 [stream] queue

## 任务队列
任务队列使用 redis stream 实现完整功能的队列任务，通过 xadd 添加 队列，xpending 读取阻塞消息，xtrim 控制任务总数量
 - TaskWorkerService 启动时候检查pending task
 - TaskWorkerService 每一次完成一个任务之后手动ACK确认
 - TaskWorkerService 会检查当下bean所有标注Task，针对每一个Task都启动一个线程消费

## 如何使用
### 配置
pom添加befun-task依赖
application.yml 添加如下配置
```
befun:
  task:
    prefix: befun.task           // 所有redis key的前缀
    trim:                        // trim功能：针对TaskTrimService
      enabled: true              // 是否开启trim功能     
      cron: "*/10 * * * * *"     // trim cron
    scheduler:                   // scheduler功能：针对TaskSchedulerService
      enabled: true              // 是否开启调度服务
      cron: "*/10 * * * * *"     // carry cron
    worker:                      // worker功能：针对TaskWorkerService
      enabled: true              // 是否开启worker
      interval-seconds: 5        // 间隔
```

### 定义任务
```
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class FooTaskDetailDto extends BaseTaskDetailDto {
    private String name;
}


@Slf4j
@Task("foo")
@Component
public class FooTaskExecutor extends BaseTaskExecutor<FooTaskDetailDto> {
    public void run(FooTaskDetailDto detailDto, TaskContextDto context) {
        log.info("foo task started {}", detailDto.getName());
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.info("foo task finished {}", detailDto.getName());
    }
}
```

### 触发任务
```
@Autowired
private FooTaskExecutor barTask;

FooTaskDetailDto detail = BarTaskDetailDto.builder()
    .name("hello")
    .build();

// 异步触发，立即返回，立即消费
fooTask.performAsync(detail);

// 延时2秒触发
fooTask.performAsyncDelay(taskDetailDto, Duration.ofSeconds(2));

// 延时两分钟触发(string)
fooTask.performAsyncDelay(taskDetailDto, "2m");
```

### 延时格式
    *  - 6s: six second
    *  - 6m: six minute
    *  - 2h: two hour
    *  - 3d: three day


### 定时任务
```
// 天定时任务
TimedTaskDto taskDto = TimedTaskDto.builder()
                .timedType(TaskExecutionTimedType.DAILY)
                .hour(11)
                .minute(0)
                .build();

// 周定时任务
TimedTaskDto taskDto = TimedTaskDto.builder()
        .timedType(TaskExecutionTimedType.WEEKLY)
        .dayOfWeeks(new String[]{"MONDAY"})
        .hour(11)
        .minute(0)
        .build();
        
long timestamp = fooTask.performAt(detail, taskDto)
```

## 节假日勿扰 暂未启用
节假日采用了https://github.com/NateScarlet/holiday-cn
数据，该库会动态抓取国务院网站。基本每年更新，考虑到我们存在私有部署的可能性，数据包提供离线安装，数据包采用sql的方式动态配置。
具体更新数据包的脚本 ./scripts/update_holiday.py


## TODO
- Auto Retryable
- Task Restful API
- Monitoring
- Error Handling
