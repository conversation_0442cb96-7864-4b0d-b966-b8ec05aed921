import sys
import argparse
import requests

parser = argparse.ArgumentParser(description='build holiday data pack (.sql)')
parser.add_argument('--year', type=int, default=2021, help='specified year to fetch')
parser.add_argument('--outfile', default='data.sql', help='default data sql')

args = parser.parse_args()
address = 'https://natescarlet.coding.net/p/github/d/holiday-cn/git/raw/master/{year}.json'.format(year = args.year)
print('fetch address from ' + address)

r = requests.get(address)
response = r.json()

count = len(response['days'])
if count == 0:
    print('no holiday found for ' + str(args.year))
    exit(-1)

print('holiday detected ' + str(count))

with open(args.outfile, 'w') as text_file:
    text_file.write('INSERT INTO holiday \n (name, date, is_off, create_time, modify_time)\n VALUES \n')
    days = map(lambda day: '(' + ','.join(['"' + day['name'] + '"',  '"' + day['date'] + '"', '1' if day['isOffDay'] else '0', 'NOW()', 'NOW()']) + ')', response['days'])
    text_file.write(',\n'.join(days))
    text_file.write(';\n')



