package org.befun.task.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.Duration;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TimeUtilsTest {

    @Test
    public void testParseDuration() {
        Duration duration = TimeUtils.parseDuration("6m");
        Assertions.assertEquals(Duration.ofMinutes(6), duration);

        duration = TimeUtils.parseDuration("6d");
        Assertions.assertEquals(Duration.ofDays(6), duration);

        duration = TimeUtils.parseDuration("6h");
        Assertions.assertEquals(Duration.ofHours(6), duration);

        duration = TimeUtils.parseDuration("6s");
        Assertions.assertEquals(Duration.ofSeconds(6), duration);
    }
}
