package org.befun.task.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Configuration
@ConfigurationProperties("befun.task.metrics")
public class TaskMetricsProperties {
    private boolean enabled;
    private List<String> supportQueues = new ArrayList<>();
}
