package org.befun.task.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "befun.task")
public class TaskProperties {

    private boolean registerAllConsumers = false;
    private String type = "redisStream";
    private TaskDelayProperty delay = new TaskDelayProperty();
    private RedisStreamWorkerProperty redisStream = new RedisStreamWorkerProperty();
    private KafkaWorkerProperty kafka = new KafkaWorkerProperty();
    private RabbitmqWorkerProperty rabbitmq = new RabbitmqWorkerProperty();

    @Getter
    @Setter
    public static class CommonProperty {
        private String prefix = "befun.task";
        private String separator = ".";
        /**
         * 最终的queue路径为 ${prefix}${queuePath}${separator}${queueName}
         */
        private String queuePath = ".queue";
        private String group = "default";
        private String name = "default";
    }

    @Getter
    @Setter
    public static class RedisStreamWorkerProperty extends CommonProperty {
        private boolean manualAck = true;
        private int pullIntervalSeconds = 5;
        private QueueTrimProperty trim = new QueueTrimProperty();
    }

    @Getter
    @Setter
    public static class KafkaWorkerProperty extends CommonProperty {
        private boolean manualAck = false;
    }

    @Getter
    @Setter
    public static class RabbitmqWorkerProperty extends CommonProperty {
        private boolean manualAck = false;
        private String exchange;
    }

    @Getter
    @Setter
    public static class TaskDelayProperty {
        /**
         * 是否启用迁移延时队列的消息的定时任务
         * 最终的delay路径为 ${prefix}${separator}${delayQueueName}
         */
        private boolean enabled = false;
        private String queue = "delay"; // delayQueueName
        private String cron = "*/10 * * * * *";
    }

    @Getter
    @Setter
    public static class QueueTrimProperty {
        /**
         * 是否启用裁剪消息队列的消息的定时任务
         */
        private boolean enabled = false;
        private int maxLen = 10000;
        private String cron = "*/10 * * * * *";
    }
}
