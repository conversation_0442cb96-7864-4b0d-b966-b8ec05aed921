package org.befun.task.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import org.befun.task.configuration.TaskMetricsProperties;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class TaskMetrics {

    @Autowired(required = false)
    private MeterRegistry meterRegistry;
    @Autowired
    private TaskMetricsProperties properties;

    private static final String METRIC_NAME_PREFIX = "befun.task";
    private static final String METRIC_NAME_APP_TIMER = METRIC_NAME_PREFIX + ".app.timer";  // tags: queue, type(producer), namespace, deployment, pod
    private static final String METRIC_NAME_APP_COUNT = METRIC_NAME_PREFIX + ".app.count";  // tags: queue, type(producer), namespace, deployment, pod

    private static final String TYPE_PRODUCER = "producer";
    private static final String TYPE_CONSUMER = "consumer";

    private final Map<String/*{queue}/{type}*/, Timer> timerMap = new HashMap<>();
    private final Map<String/*{queue}/{type}*/, Counter> counterMap = new HashMap<>();


    public void producerIncrement(String queue) {
        increment(TYPE_PRODUCER, queue);
    }

    public void consumerIncrement(String queue) {
        increment(TYPE_CONSUMER, queue);
    }

    public void tagTimer(String type, String queue, long startMs) {
        timer(type.toLowerCase(), queue, startMs);
    }

    private void increment(String type, String queue) {
        if (meterRegistry == null || properties.getSupportQueues().stream().noneMatch(q -> queue.toLowerCase().startsWith(q.toLowerCase()))) {
            return;
        }
        String key = queue + "/" + type;
        Counter counter = counterMap.get(key);
        if (counter == null) {
            synchronized (counterMap) {
                counter = counterMap.get(key);
                if (counter == null) {
                    counter = createAppCounter(queue, type);
                    counterMap.put(key, counter);
                }
            }
        }
        counter.increment();
    }

    private void timer(String type, String queue, long startMs) {
        if (meterRegistry == null || properties.getSupportQueues().stream().noneMatch(q -> queue.toLowerCase().startsWith(q.toLowerCase()))) {
            return;
        }
        String key = queue + "/" + type;
        Timer timer = timerMap.get(key);
        if (timer == null) {
            synchronized (timerMap) {
                timer = timerMap.get(key);
                if (timer == null) {
                    timer = createAppTimer(queue, type);
                    timerMap.put(key, timer);
                }
            }
        }
        long ms = System.currentTimeMillis() - startMs;
        if (ms > 0) {
            timer.record(ms, TimeUnit.MILLISECONDS);
        }
    }

    private Counter createAppCounter(String queue, String type) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("type", type));
        tags.add(Tag.of("queue", queue));
        return meterRegistry.counter(METRIC_NAME_APP_COUNT, tags);
    }


    private Timer createAppTimer(String queue, String type) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("type", type));
        tags.add(Tag.of("queue", queue));
        return meterRegistry.timer(METRIC_NAME_APP_TIMER, tags);
    }
}
