package org.befun.task;

import org.befun.task.annotation.Task;
import org.befun.task.dto.TaskContextDto;

public interface ITaskExecutor<T extends BaseTaskDetailDto> {
    /**
     * 根据task的注解判断是否开启消费者模式
     */
    default boolean isRegisterConsumer() {
        Task annotation = getClass().getAnnotation(Task.class);
        if (annotation == null) {
            throw new RuntimeException("missing task annotation for " + getClass().getName());
        }
        return annotation.enableConsumer();
    }

    T parseDetailDto(TaskContextDto context);

    void run(T detailDto, TaskContextDto context);
}
