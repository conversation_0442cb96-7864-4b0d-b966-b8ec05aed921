package org.befun.task.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(value = ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TaskLock {
    String value() default "";
    String key() default "";
    boolean isSpEl = false;
    int seconds() default 60;
}
