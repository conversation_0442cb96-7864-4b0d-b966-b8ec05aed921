package org.befun.task.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 默认重试2次，每次都会立即重试，失败2次之后，会加入 dead queue
 * 如果 intervals().length 小于 maxAttempts(), 则以后重试的间隔数，都是数组的最后一位
 */
@Target(value = ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface TaskRetryable {
    int maxAttempts() default 2;
    int[] intervals() default {0};
    TimeUnit intervalUnit() default TimeUnit.MINUTES;
    Class<?> value() default Exception.class;
    boolean disabled() default true;
}