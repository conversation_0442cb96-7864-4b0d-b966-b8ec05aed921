package org.befun.task;

import org.befun.task.configuration.TaskMetricsProperties;
import org.befun.task.configuration.TaskProperties;
import org.befun.task.metrics.TaskMetrics;
import org.befun.task.mq.ITaskService;
import org.befun.task.mq.kafka.KafkaService;
import org.befun.task.mq.rabbitmq.RabbitmqService;
import org.befun.task.mq.redisstream.RedisStreamService;
import org.befun.task.mq.redisstream.RedisStreamTrimScheduled;
import org.befun.task.service.*;
import org.springframework.amqp.rabbit.annotation.RabbitListenerConfigurer;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListenerConfigurer;
import org.springframework.kafka.config.KafkaListenerEndpointRegistrar;

@Configuration
@EnableConfigurationProperties({TaskProperties.class, TaskMetricsProperties.class})
public class TaskAutoConfiguration {

    public static final String PACKAGE_ENTITY = "org.befun.task.entity";
    public static final String PACKAGE_REPOSITORY = "org.befun.task.repository";

    /**
     *  spring:
     *      redis:
     *          host: 127.0.0.1
     *          port: 6379
     *          password: 123456
     *  befun:
     *      task:
     *          type: redisStream
     *          delay:
     *              enabled: true
     *              queue: delay
     *              cron: "1/10 * * * * *"
     *          redis-stream:
     *              prefix: befun.task.example
     *              separator: .
     *              group: example
     *              name: example
     *              manual-ack: true
     *              pull-interval-seconds: 5
     *              trim:
     *                  enabled: false
     *                  max-len: 10000
     *                  cron: "1/10 * * * * *"
     */
    @Configuration
    @ConditionalOnMissingBean(ITaskService.class)
    @ConditionalOnProperty(prefix = "befun.task", name = "type", havingValue = "redisStream", matchIfMissing = true)
    public static class TaskRedisStreamConfiguration implements SmartInitializingSingleton {

        @Bean
        public ITaskService taskService() {
            return new RedisStreamService();
        }

        @Bean
//        @ConditionalOnBean(RedisStreamService.class)
        @ConditionalOnProperty(prefix = "befun.task.redis-stream.trim", value = "enabled", havingValue = "true")
        public RedisStreamTrimScheduled redisStreamTrimScheduled() {
            return new RedisStreamTrimScheduled();
        }

        @Override
        public void afterSingletonsInstantiated() {
            taskService().registerAllTasks(null);
        }
    }

    /**
     *  spring:
     *      kafka:
     *          bootstrap-servers: 127.0.0.1:9092
     *  befun:
     *      task:
     *          type: kafka
     *          delay:
     *              enabled: true
     *              queue: delay
     *              cron: "1/10 * * * * *"
     *          kafka:
     *              prefix: befun.task.example
     *              separator: .
     *              group: example
     *              manual-ack: false
     */
    @Configuration
    @ConditionalOnMissingBean(ITaskService.class)
    @ConditionalOnProperty(prefix = "befun.task", name = "type", havingValue = "kafka")
    public static class TaskKafkaConfiguration implements KafkaListenerConfigurer {

        @Bean
        public ITaskService taskService() {
            return new KafkaService();
        }

        @Override
        public void configureKafkaListeners(KafkaListenerEndpointRegistrar registrar) {
            taskService().registerAllTasks(registrar);
        }
    }

    /**
     *  spring:
     *      rabbitmq:
     *          host: 127.0.0.1
     *          port: 5672
     *          username: guest
     *          password: guest
     *  befun:
     *      task:
     *          type: rabbitmq
     *          delay:
     *              enabled: true
     *              queue: delay
     *              cron: "1/10 * * * * *"
     *          rabbitmq:
     *              prefix: befun.task.example
     *              separator: .
     *              exchange: example
     *              manual-ack: false
     */
    @Configuration
    @ConditionalOnMissingBean(ITaskService.class)
    @ConditionalOnProperty(prefix = "befun.task", name = "type", havingValue = "rabbitmq")
    public static class TaskRabbitmqConfiguration implements RabbitListenerConfigurer {

        @Bean
        public ITaskService taskService() {
            return new RabbitmqService();
        }

        @Override
        public void configureRabbitListeners(RabbitListenerEndpointRegistrar registrar) {
            taskService().registerAllTasks(registrar);
        }
    }

    @Bean
    public CalendarService calendarService() {
        return new CalendarService();
    }

    @Bean
    @ConditionalOnBean(ITaskService.class)
    public TaskDelayService taskDelayService() {
        return new TaskDelayService();
    }

    @Bean
    public TaskDelayHelper taskDelayHelper() {
        return new TaskDelayHelper();
    }

    @Bean
    @ConditionalOnBean({ITaskService.class, TaskDelayService.class})
    @ConditionalOnProperty(prefix = "befun.task.delay", value = "enabled", havingValue = "true")
    public TaskDelayScheduled taskDelayScheduled() {
        return new TaskDelayScheduled();
    }

    @Bean
    @ConditionalOnBean(ITaskService.class)
    public TaskLockAspect taskLockAspect() {
        return new TaskLockAspect();
    }

    @Bean
    @ConditionalOnBean(ITaskService.class)
    public TaskRetryService taskRetryService() {
        return new TaskRetryService();
    }

    @Bean
    @ConditionalOnProperty(prefix = "befun.task.metrics", value = "enabled", havingValue = "true")
    public TaskMetrics taskMetrics() {
        return new TaskMetrics();
    }
}
