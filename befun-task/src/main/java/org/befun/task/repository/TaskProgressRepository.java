package org.befun.task.repository;

import org.befun.core.repository.ResourceRepository;
import org.befun.task.entity.TaskProgress;

import java.sql.Timestamp;
import java.util.Optional;

public interface TaskProgressRepository extends ResourceRepository<TaskProgress, Long> {
    Optional<TaskProgress> findFirstByOrgIdAndTypeOrderByCreateTimeDesc(Long orgId, String type);

    Optional<TaskProgress> findFirstByOrgIdAndUserIdAndTypeOrderByCreateTimeDesc(Long orgId, Long userId, String type);

    void deleteByUserIdAndCreateTimeLessThan(Long UserId, Timestamp data);

    void deleteByUserIdAndIdLessThan(Long UserId, Long id);
}
