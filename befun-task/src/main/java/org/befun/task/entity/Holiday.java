package org.befun.task.entity;

import lombok.*;
import org.befun.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "holiday")
@AllArgsConstructor
@NoArgsConstructor
public class Holiday extends BaseEntity {
    private String name;

    @Temporal(TemporalType.DATE)
    Date date;

    @Column(name = "is_off")
    private Boolean isOff;
}
