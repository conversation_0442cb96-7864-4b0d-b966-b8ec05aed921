package org.befun.task.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.befun.task.constant.TaskStatus;
import org.befun.task.constant.TaskTypeBelong;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EntityScopeStrategy
@Table(name = "task_progress")
public class TaskProgress extends EnterpriseEntity {

    @DtoProperty(ignore = true)
    @Column(name = "user_id")
    private Long userId;

    @DtoProperty(description = "任务类型", jsonView = ResourceViews.Basic.class)
    @Column(name = "type")
    private String type;

    @DtoProperty(description = "任务所属", jsonView = ResourceViews.Basic.class)
    @Column(name = "type_belong")
    @Enumerated(EnumType.STRING)
    private TaskTypeBelong typeBelong;

    @DtoProperty(description = "任务状态", jsonView = ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private TaskStatus status;

    @DtoProperty(description = "总数量", jsonView = ResourceViews.Basic.class)
    @Column(name = "total_size")
    private Integer totalSize;

    @DtoProperty(description = "成功数量", jsonView = ResourceViews.Basic.class)
    @Column(name = "success_size")
    private Integer successSize = 0;

    @DtoProperty(description = "失败数量", jsonView = ResourceViews.Basic.class)
    @Column(name = "failed_size")
    private Integer failedSize = 0;

    @DtoProperty(ignore = true)
    @Column(name = "params")
    private String params;

    @DtoProperty(ignore = true)
    @Column(name = "result")
    private String result;

    @DtoProperty(description = "过期时间", jsonView = ResourceViews.Basic.class)
    @Column(name = "expire_time")
    private Date expireTime;

    @DtoProperty(description = "关联Id", jsonView = ResourceViews.Basic.class)
    @Column(name = "relation_id")
    private Long relationId;
}
