package org.befun.task.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.befun.task.constant.TaskExecutionTimedType;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
public class TimedTaskDto {
    private TaskExecutionTimedType timedType;
    private String[] dayOfWeeks;
    private int hour;
    private int minute;
    private boolean skipHoliday;

    public String formatTimed(boolean throwable) {
        validate(throwable);
        List<String> items = new ArrayList<>();
        items.add(timedType.name());
        items.add(String.join(",", getDayOfWeeks()));
        items.add(String.valueOf(hour));
        items.add(String.valueOf(minute));
        items.add(String.valueOf(skipHoliday ? 1 : 0));
        return String.join("/", items);
    }

    public static TimedTaskDto parseTimed(String timedInfo) {
        String[] items = timedInfo.split("/");
        if (items.length == 5) {
            TimedTaskDto timed = TimedTaskDto.builder()
                    .timedType(TaskExecutionTimedType.valueOf(items[0]))
                    .dayOfWeeks(items[1].split(","))
                    .hour(Integer.parseInt(items[2]))
                    .minute(Integer.parseInt(items[3]))
                    .skipHoliday(Integer.parseInt(items[4]) == 1)
                    .build();
            if (timed.validate(false)) {
                return timed;
            }
        }
        return null;
    }

    public String[] getDayOfWeeks() {
        return dayOfWeeks == null ? new String[0] : dayOfWeeks;
    }

    private boolean validate(boolean throwable) {
        List<String> message = new ArrayList<>();
        if (timedType == null) {
            message.add("定时任务周期类型不能为(null), 且必须是(DAILY|WEEKLY)");
        }
        if (timedType == TaskExecutionTimedType.WEEKLY) {
            if (dayOfWeeks == null) {
                message.add("定时任务周期为(WEEKLY)时，必须指定星期几");
            }
        }
        if (hour < 0 || hour > 23) {
            message.add("小时必须在(0-23)之间");
        }
        if (minute < 0 || minute > 59) {
            message.add("分钟必须在(0-59)之间");
        }
        if (!message.isEmpty()) {
            if (throwable) {
                throw new RuntimeException(String.join(",", message));
            }
            return false;
        }
        return true;
    }
}