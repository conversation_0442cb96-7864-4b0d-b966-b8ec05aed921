package org.befun.task.dto;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.constant.TaskExecutionType;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskContextDto implements Serializable {
    private String id = UUID.randomUUID().toString();
    private String name = "";
    private String group = "";
    private String description = "";
    private String queue = "";
    private int retryCount = 0;
    private Map<String, Object> data = new HashMap<>();
    private String dataClass = "";
    private String dataJson = "";
    private int delay = 0;
    private TaskExecutionType executionType = TaskExecutionType.REGULAR;

}