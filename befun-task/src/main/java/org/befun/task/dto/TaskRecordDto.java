package org.befun.task.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.RecordId;

import java.io.Serializable;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class TaskRecordDto implements Serializable {
    private RecordId id;
    private TaskContextDto contextDto;
}