package org.befun.task.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.task.constant.TaskStatus;

@Getter
@Setter
@NoArgsConstructor
public class TaskProgressDto {

    @Schema(description = "完成状态")
    private TaskStatus status;
    @Schema(description = "总数量")
    private long totalSize;
    @Schema(description = "成功数量")
    private long successSize;
    @Schema(description = "失败数量")
    private long failedSize;
    @Schema(description = "完成数量")
    private long completedSize;
    @Schema(description = "最后活跃时间")
    private String lastActiveTime;

    public TaskProgressDto(TaskStatus status, long totalSize, long successSize, long failedSize) {
        this.status = status;
        this.totalSize = totalSize;
        this.successSize = successSize;
        this.failedSize = failedSize;
    }

    public TaskProgressDto(TaskStatus status, long totalSize, long successSize, long failedSize, String lastActiveTime) {
        this.status = status;
        this.totalSize = totalSize;
        this.successSize = successSize;
        this.failedSize = failedSize;
        this.lastActiveTime = lastActiveTime;
    }

    public long getCompletedSize() {
        return NumberUtils.min((successSize + failedSize), totalSize);
    }
}