package org.befun.task;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.dto.TimedTaskDto;
import org.befun.task.mq.ITaskService;
import org.befun.task.service.TaskDelayService;
import org.befun.task.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.Assert;

import java.lang.reflect.ParameterizedType;
import java.time.Duration;

@Slf4j
@SuppressWarnings("unchecked")
public abstract class BaseTaskExecutor<T extends BaseTaskDetailDto> implements ITaskExecutor<T> {

    @Lazy
    @Autowired
    private ITaskService taskService;
    @Lazy
    @Autowired
    private TaskDelayService taskDelayService;
    private final Class<T> dtoClass;

    public BaseTaskExecutor() {
        dtoClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    @Override
    public T parseDetailDto(TaskContextDto context) {
        return JsonHelper.toObject(context.getDataJson(), dtoClass);
    }

    /**
     * performAsync: 放到对应的queue，延后处理
     */
    public void performAsync(T data) {
        Assert.notNull(taskService, "missing task service");
        taskService.addTask(this.getClass(), data);
    }

    /**
     * performAsyncDelay: 放到延迟队列，延后处理
     */
    public void performAsyncDelay(T data, Duration duration) {
        Assert.notNull(taskDelayService, "missing task service");
        taskDelayService.addTaskDelay(this.getClass(), data, duration);
    }

    /**
     * performAsyncDelay: 放到延迟队列，延后处理
     */
    public void performAsyncDelay(T data, String durationStr) throws RuntimeException {
        Assert.notNull(taskDelayService, "missing task service");
        Duration duration = TimeUtils.parseDuration(durationStr);
        taskDelayService.addTaskDelay(this.getClass(), data, duration);
    }

    /**
     * performAt: 定时执行任务
     */
    public long performAt(T data, TimedTaskDto timedTaskDto) {
        Assert.notNull(taskDelayService, "missing task service");
        Duration duration = TimeUtils.parseAtDuration(timedTaskDto);
        return taskDelayService.addTaskDelay(this.getClass(), data, duration);
    }

}
