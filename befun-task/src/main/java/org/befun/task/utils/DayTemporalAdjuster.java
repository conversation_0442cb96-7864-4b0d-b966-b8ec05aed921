package org.befun.task.utils;

import org.befun.core.service.SpringContextHolder;
import org.befun.task.service.CalendarService;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAdjuster;

/**
 * 定时任务日调整器
 * - 跳过节假日
 *
 * <AUTHOR>
 */
public class DayTemporalAdjuster implements TemporalAdjuster {

    private Boolean skipHoliday = false;
    private CalendarService calendarService;
    private int scheduleMinuteOfDay = 0;

    /**
     * disable default ctor
     */
    private DayTemporalAdjuster() {
    }

    /**
     * init
     */
    public DayTemporalAdjuster(int hour, int minute, boolean skipHoliday) {
        this.scheduleMinuteOfDay = hour * 60 + minute;
        this.skipHoliday = skipHoliday;
        this.calendarService = SpringContextHolder.getBean(CalendarService.class);
    }

    @Override
    public Temporal adjustInto(Temporal temporal) {
        LocalDateTime temporalAdjusterTime = LocalDateTime.from(temporal);
        int currentHour = temporalAdjusterTime.getHour();
        int currentMinute = temporalAdjusterTime.getMinute();
        int minuteOfDay = currentHour * 60 + currentMinute;

        int daysToAdd = minuteOfDay < this.scheduleMinuteOfDay ? 0 : 1;
        while (true) {
            LocalDateTime time = (LocalDateTime)temporal.plus(daysToAdd * 24 * 60 + this.scheduleMinuteOfDay - minuteOfDay, ChronoUnit.MINUTES);
            if (this.skipHoliday == false || !this.calendarService.isHoliday(time.toLocalDate())) {
                // found one and return
                return time;
            }
            daysToAdd += 1;
        }
    }
}

