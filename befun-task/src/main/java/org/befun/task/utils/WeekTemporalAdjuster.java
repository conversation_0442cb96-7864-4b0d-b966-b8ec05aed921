package org.befun.task.utils;

import org.befun.core.service.SpringContextHolder;
import org.befun.task.service.CalendarService;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAdjuster;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时任务周调整器
 * - 根据配置的周1，2，3等白名单寻找下一个有效时间
 * - 跳过节假日
 *
 * <AUTHOR>
 */
public class WeekTemporalAdjuster implements TemporalAdjuster {

    private List<DayOfWeek> dayOfWeeks = new ArrayList<>();
    private Boolean skipHoliday = false;
    private CalendarService calendarService;
    private int scheduleMinuteOfDay = 0;

    /**
     * disable default ctor
     */
    private WeekTemporalAdjuster() {
    }

    /**
     * init from DayOfWeeks String
     * @param dayOfWeeks
     */
    public WeekTemporalAdjuster(String[] dayOfWeeks, int hour, int minute, boolean skipHoliday) {
        List<DayOfWeek> values = Arrays.stream(dayOfWeeks)
                .map(n -> DayOfWeek.valueOf(n))
                .collect(Collectors.toList());
        this.dayOfWeeks = values;
        this.scheduleMinuteOfDay = hour * 60 + minute;
        this.skipHoliday = skipHoliday;
        this.calendarService = SpringContextHolder.getBean(CalendarService.class);
    }

    @Override
    public Temporal adjustInto(Temporal temporal) {
        if (this.dayOfWeeks.isEmpty()) {
            // 默认+1天执行
            return temporal.plus(1, ChronoUnit.DAYS);
        }

        LocalTime temporalAdjusterTime = LocalTime.from(temporal);
        int currentHour = temporalAdjusterTime.getHour();
        int currentMinute = temporalAdjusterTime.getMinute();
        int minuteOfDay = currentHour * 60 + currentMinute;

        int daysToAdd = minuteOfDay < this.scheduleMinuteOfDay ? 0 : 1;
        while (true) {
            LocalDateTime time = (LocalDateTime)temporal.plus(daysToAdd * 24 * 60 + this.scheduleMinuteOfDay - minuteOfDay, ChronoUnit.MINUTES);
            DayOfWeek dayOfWeek = DayOfWeek.of(time.get(ChronoField.DAY_OF_WEEK));
            if (this.dayOfWeeks.contains(dayOfWeek)) {
                if (this.skipHoliday == false || !this.calendarService.isHoliday(time.toLocalDate())) {
                    // found one and return
                    return time;
                }
            }
            daysToAdd += 1;
        }
    }
}

