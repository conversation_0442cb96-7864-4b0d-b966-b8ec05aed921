package org.befun.task.utils;


import org.befun.core.service.SpringContextHolder;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.Map;

public class SpELUtils {

	private static final ExpressionParser PARSER = new SpelExpressionParser();


	public static Object parseExpression(Map<String, Object> variables, String expression) {
		return parseExpression(null, variables, expression);
	}

	public static Object parseExpression(Object rootObject, String expression) {
		return parseExpression(rootObject,null, expression);
	}

	public static Object parseExpression(Object rootObject, Map<String, Object> variables, String expression) {
		return parseExpression(rootObject, variables, expression, Object.class);
	}

	public static <T> T parseExpression(Object rootObject, Map<String, Object> variables, String expression, Class<T> clazzType) {
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setRootObject(rootObject);
		if(variables != null){
			context.setVariables(variables);
		}
		context.setBeanResolver((ctx, beanName) -> SpringContextHolder.getApplicationContext().getBean(beanName));
		Expression exp = PARSER.parseExpression(expression);
		return exp.getValue(context, clazzType);
	}

	public static void main(String[] args) {
		String expression = "hasRole()";
		try {
			String result = (String) parseExpression(new User(), expression);
			System.out.println(result);
		} catch (SpelEvaluationException e) {
			System.out.println(expression);
		}
	}

	static class User {
		public String hasRole(String str){
			System.out.println("......."+str);
			return "admin";
		}
	}
}









