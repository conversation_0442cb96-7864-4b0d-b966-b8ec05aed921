package org.befun.task.utils;

import org.befun.task.dto.TimedTaskDto;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjuster;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TimeUtils {

    private static final Pattern PERIOD_PATTERN = Pattern.compile("(\\d+)([smhdyw])");

    /**
     * parse duration from string
     * - 6s: six seconds
     * - 6m: six minutes
     * - 2h: two hour
     * - 3d: three day
     * - 1w: one week
     */
    public static Duration parseDuration(String text) throws RuntimeException {
        if (text == null) {
            return null;
        }
        Matcher matcher = PERIOD_PATTERN.matcher(text);
        while (matcher.find()) {
            int number = Integer.parseInt(matcher.group(1));
            String unit = matcher.group(2);
            switch (unit) {
                case "s":
                    return Duration.ofSeconds(number);
                case "m":
                    return Duration.ofMinutes(number);
                case "h":
                    return Duration.ofHours(number);
                case "d":
                    return Duration.ofDays(number);
                case "w":
                    return Duration.ofDays(number * 7L);
            }
        }
        throw new RuntimeException("invalid duration format");
    }

    public static Duration parseAtDuration(TimedTaskDto timedTaskDto) {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime triggerTime = null;
        TemporalAdjuster adjuster = null;

        switch (timedTaskDto.getTimedType()) {
            case DAILY:
                adjuster = new DayTemporalAdjuster(timedTaskDto.getHour(), timedTaskDto.getMinute(), timedTaskDto.isSkipHoliday());
                break;
            case WEEKLY:
                adjuster = new WeekTemporalAdjuster(timedTaskDto.getDayOfWeeks(), timedTaskDto.getHour(), timedTaskDto.getMinute(), timedTaskDto.isSkipHoliday());
                break;
            default:
                throw new RuntimeException("Unknown timed type");
        }
        triggerTime = currentTime.with(adjuster);
        long minutesBetween = ChronoUnit.MINUTES.between(currentTime, triggerTime);
        return Duration.ofMinutes(minutesBetween);
    }
}
