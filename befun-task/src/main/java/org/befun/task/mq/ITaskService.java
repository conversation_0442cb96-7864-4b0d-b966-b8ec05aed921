package org.befun.task.mq;

import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskDetailDto;
import org.befun.task.ITaskExecutor;
import org.befun.task.configuration.TaskProperties;
import org.befun.task.constant.TaskExecutionType;
import org.befun.task.dto.TaskContextDto;

import java.util.Optional;
import java.util.function.Supplier;

public interface ITaskService {

    void registerAllTasks(Object registrar);

    TaskProperties.CommonProperty getWorkerProperty();

    String getName();

    String getGroup();

    String getDelayQueue();

    String getFullDelayQueue();

    default String getFullQueue(String queue) {
        return String.format("%s%s%s%s", getWorkerProperty().getPrefix(), getWorkerProperty().getQueuePath(), getWorkerProperty().getSeparator(), queue);
    }

    default String getFullQueue(TaskContextDto contextDto) {
        return String.format("%s%s%s%s", getWorkerProperty().getPrefix(), getWorkerProperty().getQueuePath(), getWorkerProperty().getSeparator(), contextDto.getQueue());
    }

    default String getFullQueue(ITaskExecutor<?> executor) {
        return String.format("%s%s%s%s", getWorkerProperty().getPrefix(), getWorkerProperty().getQueuePath(), getWorkerProperty().getSeparator(), getQueue(executor.getClass()));
    }

    String getQueue(Class<?> executorClass);

    default String getQueue(TaskContextDto contextDto) {
        return contextDto.getQueue();
    }

    String addTask(TaskContextDto contextDto);

    default String addTask(Class<?> executorClass, BaseTaskDetailDto detailDto) {
        return addTask(buildContext(getQueue(executorClass), detailDto));
    }

    default String addTask(String queue, BaseTaskDetailDto detailDto) {
        return addTask(buildContext(queue, detailDto));
    }

    default TaskContextDto buildContext(String queue, TaskExecutionType executionType, BaseTaskDetailDto detailDto) {
        TaskContextDto contextDto = new TaskContextDto();
        contextDto.setExecutionType(executionType);
        contextDto.setQueue(queue);
        contextDto.setGroup(getGroup());
        contextDto.setDataClass(detailDto.getClass().getName());
        contextDto.setDataJson(Optional.ofNullable(JsonHelper.toJson(detailDto)).orElse(""));
        return contextDto;
    }

    default TaskContextDto buildContext(String queue, BaseTaskDetailDto detailDto) {
        return buildContext(queue, TaskExecutionType.REGULAR, detailDto);
    }


}
