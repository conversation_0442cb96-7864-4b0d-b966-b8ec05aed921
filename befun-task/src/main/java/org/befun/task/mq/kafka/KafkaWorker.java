package org.befun.task.mq.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskDetailDto;
import org.befun.task.ITaskExecutor;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.mq.ITaskWorker;
import org.befun.task.mq.TaskRetryWorker;
import org.springframework.kafka.config.KafkaListenerEndpoint;
import org.springframework.kafka.listener.AcknowledgingMessageListener;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.TopicPartitionOffset;
import org.springframework.kafka.support.converter.MessageConverter;

import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;

@Slf4j
public class KafkaWorker<T extends BaseTaskDetailDto> extends TaskRetryWorker implements ITaskWorker, KafkaListenerEndpoint, AcknowledgingMessageListener<String, String> {

    private final ITaskExecutor<T> executor;
    private final String topic;
    private final String groupId;
    private final KafkaService kafkaService;

    public KafkaWorker(String groupId, String topic, ITaskExecutor<T> executor, KafkaService kafkaService) {
        super(executor);
        this.topic = topic;
        this.groupId = groupId;
        this.executor = executor;
        this.kafkaService = kafkaService;
    }

    // from AcknowledgingMessageListener
    @Override
    public void onMessage(ConsumerRecord<String, String> data, Acknowledgment acknowledgment) {
        boolean retry = false;
        Throwable throwable = null;
        TaskContextDto contextDto = null;
        try {
            log.info("接受到kafka消息：queue={}, data={}", data.topic(), data);
            contextDto = JsonHelper.toObject(data.value(), TaskContextDto.class);
            T detail = executor.parseDetailDto(contextDto);
            executor.run(detail, contextDto);
        } catch (Throwable ex) {
            log.warn("执行kafka消息失败：queue={}, data={}", data.topic(), data, ex);
            retry = true;
            throwable = ex;
        } finally {
            if (kafkaService.getWorkerProperty().isManualAck()) {
                acknowledgment.acknowledge();
            }
            if (enableRetry && retry) {
                boolean tryRetry = kafkaService.retry(this, contextDto, throwable);
                if (tryRetry) {
                    log.warn("kafka消息已重试：queue={}, data={}", data.topic(), data);
                }
            }
        }
    }

    @Override
    public String getFullQueue() {
        return topic;
    }

    // from KafkaListenerEndpoint
    @Override
    public String getId() {
        return String.format("kafka-%s-%s", getGroupId(), topic);
    }

    // from KafkaListenerEndpoint
    @Override
    public String getGroupId() {
        return groupId;
    }

    // from KafkaListenerEndpoint
    @Override
    public String getGroup() {
        return null;
    }

    // from KafkaListenerEndpoint
    @Override
    public Collection<String> getTopics() {
        return List.of(topic);
    }

    // from KafkaListenerEndpoint
    @Override
    public TopicPartitionOffset[] getTopicPartitionsToAssign() {
        return new TopicPartitionOffset[0];
    }

    // from KafkaListenerEndpoint
    @Override
    public Pattern getTopicPattern() {
        return null;
    }

    // from KafkaListenerEndpoint
    @Override
    public String getClientIdPrefix() {
        return null;
    }

    // from KafkaListenerEndpoint
    @Override
    public Integer getConcurrency() {
        return 1;
    }

    // from KafkaListenerEndpoint
    @Override
    public Boolean getAutoStartup() {
        return true;
    }

    // from KafkaListenerEndpoint
    @Override
    public void setupListenerContainer(MessageListenerContainer listenerContainer, MessageConverter messageConverter) {
        listenerContainer.setupMessageListener(this);
    }

    // from KafkaListenerEndpoint
    @Override
    public boolean isSplitIterables() {
        return true;
    }

    // from ITaskWorker
    @Override
    public String name() {
        return String.format("KafkaWorker(queue=%s,group=%s)", topic, groupId);
    }

}
