package org.befun.task.mq.redisstream;

import lombok.extern.slf4j.Slf4j;
import org.befun.task.ITaskExecutor;
import org.befun.task.configuration.TaskProperties;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.metrics.TaskMetrics;
import org.befun.task.mq.AbstractMqService;
import org.befun.task.mq.ITaskService;
import org.befun.task.mq.ITaskWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;

@Slf4j
public class RedisStreamService extends AbstractMqService<TaskProperties.RedisStreamWorkerProperty> implements ITaskService {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired(required = false)
    private TaskMetrics taskMetrics;
    private ExecutorService executorService;

    @PreDestroy
    public void destroy() {
        foreachTaskWorker((ignore, worker) -> ((RedisStreamWorker<?>) worker).stop());
    }

    @Override
    protected void beforeRegisterAllTasks() {
        executorService = Executors.newFixedThreadPool(workerMap.size());
    }

    @Override
    protected void registerTask(Object registrar, ITaskWorker worker) {
        RedisStreamWorker<?> redisStreamWorker = (RedisStreamWorker<?>) worker;
        registerConsumerGroup(redisStreamWorker.getFullQueue());
        executorService.execute(redisStreamWorker);
        log.info("已成功注册RedisStream消费者：{}", worker.name());
    }

    @Override
    public TaskProperties.RedisStreamWorkerProperty getWorkerProperty() {
        return taskProperties.getRedisStream();
    }

    @Override
    protected ITaskWorker createWorker(String group, String queue, String fullQueue, ITaskExecutor<?> executor) {
        return new RedisStreamWorker<>(group, fullQueue, executor, this, taskMetrics);
    }

    @Override
    public String addTask(TaskContextDto contextDto) {
        String fullQueue = getFullQueue(contextDto);
        ObjectRecord<String, TaskContextDto> record = StreamRecords.newRecord().in(fullQueue).ofObject(contextDto);
        redisTemplate.opsForStream().add(record);
        if (taskMetrics != null) {
            taskMetrics.producerIncrement(contextDto.getQueue());
        }
        return contextDto.getId();
    }

    private void registerConsumerGroup(String fullQueue) {
        try {
            log.info("register consumer group {} for queue {}", getGroup(), fullQueue);
            redisTemplate.opsForStream().createGroup(fullQueue, getGroup());
        } catch (RedisSystemException ex) {
            log.warn("consumer group {} for queue {} already exist, skip", getGroup(), fullQueue);
        }
    }

    void foreachTaskWorker(BiConsumer<String, ITaskWorker> consumer) {
        workerMap.forEach(consumer);
    }

    void trimStream(String fullQueue) {
        redisTemplate.opsForStream().trim(fullQueue, getWorkerProperty().getTrim().getMaxLen());
    }

    Map<String, TaskContextDto> getPendingTasks(String fullQueue) {
        log.info("try get pending tasks for queue {}", fullQueue);
        Consumer consumer = Consumer.from(getGroup(), getName());
        PendingMessages pendingMessages = redisTemplate.opsForStream().pending(fullQueue, consumer);
        Map<String, TaskContextDto> result = new HashMap<>();
        pendingMessages.stream().forEach(x -> {
            List<ObjectRecord<String, TaskContextDto>> pendingTasks = redisTemplate.opsForStream().range(TaskContextDto.class, fullQueue, Range.rightOpen(x.getIdAsString(), x.getIdAsString()));
            pendingTasks.forEach(task -> {
                result.put(task.getId().toString(), task.getValue());
            });
        });
        return result;
    }

    @SuppressWarnings("unchecked")
    Map<String, TaskContextDto> getTask(String fullQueue, Duration duration) {
        log.debug("try get task for queue {}", fullQueue);
        try {
            Consumer consumer = Consumer.from(getGroup(), getName());
            StreamReadOptions options = StreamReadOptions.empty().block(duration).count(1);
            StreamOffset<String> offset = StreamOffset.create(fullQueue, ReadOffset.lastConsumed());
            List<ObjectRecord<String, TaskContextDto>> result = redisTemplate.opsForStream().read(TaskContextDto.class, consumer, options, offset);
            if (result != null && result.size() == 1) {
                return Map.of(result.get(0).getId().toString(), result.get(0).getValue());
            }
        } catch (RedisSystemException ex) {
            if (ex.getCause() != null && ex.getCause().getMessage().startsWith("NOGROUP")) {
                // group deleted
                log.warn("group deleted unexpected, try recovery");
                registerConsumerGroup(fullQueue);
            } else {
                log.warn("failed to fetch task {} {}", fullQueue, ex.getMessage());
            }
        }
        return null;
    }

    @SuppressWarnings("UnusedReturnValue")
    Long acknowledge(String fullQueue, String recordId) {
        log.debug("try acknowledge task for queue {}", fullQueue);
        return redisTemplate.opsForStream().acknowledge(fullQueue, getGroup(), recordId);
    }
}
