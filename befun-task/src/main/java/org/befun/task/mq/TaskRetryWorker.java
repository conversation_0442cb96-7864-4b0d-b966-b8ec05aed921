package org.befun.task.mq;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.ITaskExecutor;
import org.befun.task.annotation.TaskRetryable;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Getter
public abstract class TaskRetryWorker {

    protected boolean enableRetry;
    protected int maxAttempts;
    protected int[] intervals;
    protected TimeUnit intervalUnit;
    protected Class<?> retryException;

    public abstract String getFullQueue();

    public TaskRetryWorker(ITaskExecutor<?> taskExecutor) {
        TaskRetryable retry = taskExecutor.getClass().getAnnotation(TaskRetryable.class);
        if (retry != null && !retry.disabled()) {
            enableRetry = true;
            maxAttempts = retry.maxAttempts();
            intervals = retry.intervals();
            intervalUnit = retry.intervalUnit();
            retryException = retry.value();
            log.info("task queue {} retry strategy：maxAttempts={}，intervals={}，intervalUnit={}，retryException={}", getFullQueue(), maxAttempts, Arrays.stream(intervals).mapToObj(Objects::toString).collect(Collectors.joining(",")), intervalUnit.name(), retryException.getSimpleName());
        }
    }
}
