package org.befun.task.mq;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.task.ITaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.configuration.TaskProperties;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.service.TaskRetryService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractMqService<P extends TaskProperties.CommonProperty> implements ITaskService {

    @Autowired
    protected TaskProperties taskProperties;
    @Autowired
    protected TaskRetryService retryService;
    @Autowired(required = false)
    protected List<ITaskExecutor<?>> executors;
    protected final Map<String, ITaskWorker> workerMap = new HashMap<>();
    protected final Map<Class<?>, String> taskQueueMap = new HashMap<>();

    protected abstract ITaskWorker createWorker(String group, String queue, String fullQueue, ITaskExecutor<?> executor);

    public abstract P getWorkerProperty();

    protected void beforeRegisterAllTasks() {
    }

    protected void registerTask(Object registrar, ITaskWorker worker) {
    }

    public void registerAllTasks(Object registrar) {
        if (!taskProperties.isRegisterAllConsumers()) {
            log.info("已关闭Task消息消费者模式");
            return;
        }
        if (CollectionUtils.isEmpty(executors)) {
            log.info("未找到Task消息消费者");
            return;
        }
        executors.forEach(executor -> {
            if (executor.isRegisterConsumer()) {
                String queue = getQueue(executor.getClass());
                String fullQueue = getFullQueue(queue);
                String group = getGroup();
                ITaskWorker worker = createWorker(group, queue, fullQueue, executor);
                workerMap.put(fullQueue, worker);
            }
        });
        beforeRegisterAllTasks();
        workerMap.values().forEach(worker -> registerTask(registrar, worker));
        log.info("已成功注册Task所有消费者，[{}]", workerMap.values().stream().map(ITaskWorker::name).collect(Collectors.joining(",")));
    }

    public boolean retry(TaskRetryWorker worker, TaskContextDto contextDto, Throwable e) {
        return retryService.tryRetry(worker, contextDto, e);
    }

    @Override
    public String getName() {
        return getWorkerProperty().getName();
    }

    @Override
    public String getGroup() {
        return getWorkerProperty().getGroup();
    }

    @Override
    public String getDelayQueue() {
        return taskProperties.getDelay().getQueue();
    }

    @Override
    public String getFullDelayQueue() {
        return String.format("%s%s%s", getWorkerProperty().getPrefix(), getWorkerProperty().getSeparator(), getDelayQueue());
    }

    @Override
    public String getQueue(Class<?> executorClass) {
        if (taskQueueMap.containsKey(executorClass)) {
            return taskQueueMap.get(executorClass);
        }
        Task annotation = executorClass.getAnnotation(Task.class);
        if (annotation == null) {
            throw new RuntimeException("missing task annotation for " + executorClass.getName());
        }
        String queue = annotation.value();
        taskQueueMap.put(executorClass, queue);
        return queue;
    }
}
