package org.befun.task.mq.redisstream;

import lombok.extern.slf4j.Slf4j;
import org.befun.task.annotation.TaskLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

@Slf4j
public class RedisStreamTrimScheduled {

    @Autowired
    private RedisStreamService redisStreamService;

    @TaskLock(key = "trim", seconds = 60)
    @Scheduled(cron = "${befun.task.redis-stream.trim.cron}")
    public void trimTasks() {
        log.info("scheduler service start to trim");
        try {
            redisStreamService.foreachTaskWorker((fullQueue, ignore) -> {
                redisStreamService.trimStream(fullQueue);
            });
        } catch (Exception ex) {
            log.error("failed to trim due to exception", ex);
        }
        log.info("scheduler service finished trim");
    }
}
