package org.befun.task.mq.rabbitmq;

import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskDetailDto;
import org.befun.task.ITaskExecutor;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.mq.ITaskWorker;
import org.befun.task.mq.TaskRetryWorker;
import org.befun.task.mq.kafka.KafkaService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.MessageListenerContainer;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpoint;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.kafka.config.KafkaListenerEndpoint;
import org.springframework.kafka.listener.AcknowledgingMessageListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.TopicPartitionOffset;
import org.springframework.kafka.support.converter.MessageConverter;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Slf4j
public class RabbitmqWorker<T extends BaseTaskDetailDto> extends TaskRetryWorker implements ITaskWorker, RabbitListenerEndpoint, ChannelAwareMessageListener {

    private final ITaskExecutor<T> executor;
    private final String exchange;
    private final String routingKey;
    private final RabbitmqService rabbitmqService;

    public RabbitmqWorker(String exchange, String routingKey, ITaskExecutor<T> executor, RabbitmqService rabbitmqService) {
        super(executor);
        this.exchange = exchange;
        this.routingKey = routingKey;
        this.executor = executor;
        this.rabbitmqService = rabbitmqService;
    }

    @Override
    public void onMessage(Message message, Channel channel) {
        boolean retry = false;
        Throwable throwable = null;
        TaskContextDto contextDto = null;
        String data = null;
        try {
            data = new String(message.getBody());
            log.info("接受到rabbit消息：exchange={}, routingKey={}, data={}", exchange, routingKey, data);
            contextDto = JsonHelper.toObject(data, TaskContextDto.class);
            T detail = executor.parseDetailDto(contextDto);
            executor.run(detail, contextDto);
        } catch (Throwable ex) {
            log.warn("执行rabbit消息失败：exchange={}, routingKey={}, data={}", exchange, routingKey, data);
            retry = true;
            throwable = ex;
        } finally {
            try {
                if (rabbitmqService.getWorkerProperty().isManualAck()) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
            } catch (IOException e) {
                log.warn("rabbit消息确认失败：exchange={}, routingKey={}, deliveryTag={}", exchange, routingKey, message.getMessageProperties().getDeliveryTag());
            }
            if (enableRetry && retry) {
                boolean tryRetry = rabbitmqService.retry(this, contextDto, throwable);
                if (tryRetry) {
                    log.warn("rabbit消息已重试：exchange={}, routingKey={}, data={}", exchange, routingKey, data);
                }
            }
        }
    }

    @Override
    public String getFullQueue() {
        return routingKey;
    }

    // from ITaskWorker
    @Override
    public String name() {
        return String.format("RabbitmqWorker(exchange=%s,queue=%s)", exchange, getFullQueue());
    }

    @Override
    public String getId() {
        return String.format("rabbit-%s-%s", exchange, getFullQueue());
    }

    @Override
    public String getGroup() {
        return null;
    }

    @Override
    public String getConcurrency() {
        return null;
    }

    @Override
    public Boolean getAutoStartup() {
        return true;
    }

    @Override
    public void setupListenerContainer(MessageListenerContainer listenerContainer) {
        AbstractMessageListenerContainer container = (AbstractMessageListenerContainer) listenerContainer;
        container.setQueueNames(routingKey);
        listenerContainer.setupMessageListener(this);
    }
}
