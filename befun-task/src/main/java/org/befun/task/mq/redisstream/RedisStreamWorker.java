package org.befun.task.mq.redisstream;

import lombok.extern.slf4j.Slf4j;
import org.befun.task.BaseTaskDetailDto;
import org.befun.task.ITaskExecutor;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.metrics.TaskMetrics;
import org.befun.task.mq.ITaskWorker;
import org.befun.task.mq.TaskRetryWorker;
import org.springframework.context.Lifecycle;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

@Slf4j
public class RedisStreamWorker<T extends BaseTaskDetailDto> extends TaskRetryWorker implements ITaskWorker, Runnable, Lifecycle {

    private final String group;
    private final String fullQueue;
    private final ITaskExecutor<T> executor;
    private final RedisStreamService redisStreamService;
    private final TaskMetrics taskMetrics;
    private final AtomicBoolean running;
    private final int interval;

    public RedisStreamWorker(String group, String fullQueue, ITaskExecutor<T> executor, RedisStreamService redisStreamService, TaskMetrics taskMetrics) {
        super(executor);
        this.group = group;
        this.fullQueue = fullQueue;
        this.executor = executor;
        this.redisStreamService = redisStreamService;
        this.taskMetrics = taskMetrics;
        this.running = new AtomicBoolean(false);
        this.interval = redisStreamService.getWorkerProperty().getPullIntervalSeconds();
    }

    @Override
    public void run() {
        start();
        // consume all pending tasks
        run(true, redisStreamService.getPendingTasks(fullQueue), size -> {
            log.info("开始处理({})队列已消费但没有提交确认的{}条消息", fullQueue, size);
        });

        // consumer task
        while (isRunning()) {
            try {
                run(false, redisStreamService.getTask(fullQueue, Duration.ofSeconds(interval)), size -> {
                    log.info("成功拉取({})队列的{}条新消息，开始处理", fullQueue, size);
                });
            } catch (Exception ex) {
                log.error("({})队列的新消息处理失败", fullQueue, ex);
            }
        }
        log.info("RedisStream消费者已关闭：{}", name());
    }

    void run(boolean pending, Map<String, TaskContextDto> tasks, Consumer<Integer> consumer) {
        if (tasks != null && !tasks.isEmpty()) {
            consumer.accept(tasks.size());
            tasks.forEach((recordId, message) -> run(pending, recordId, message));
        }
    }

    void run(boolean pending, String recordId, TaskContextDto contextDto) {
        boolean retry = false;
        Throwable throwable = null;
        try {
            log.info("开始处理({})队列的{}消息({})", fullQueue, pending ? "未确认" : "新", recordId);
            T detailDto = executor.parseDetailDto(contextDto);
            executor.run(detailDto, contextDto);
        } catch (Throwable ex) {
            log.error("({})队列的{}消息({})处理失败", fullQueue, pending ? "未确认" : "新", recordId, ex);
            retry = true;
            throwable = ex;
        } finally {
            if (taskMetrics != null) {
                taskMetrics.consumerIncrement(contextDto.getQueue());
            }
            if (redisStreamService.getWorkerProperty().isManualAck()) {
                this.redisStreamService.acknowledge(fullQueue, recordId);
            }
            if (enableRetry && retry) {
                boolean tryRetry = redisStreamService.retry(this, contextDto, throwable);
                if (tryRetry) {
                    log.info("({})队列的{}消息({})已添加重试", fullQueue, pending ? "未确认" : "新", recordId);
                }
            }
        }
    }

    @Override
    public void start() {
        running.set(true);
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public void stop() {
        running.set(false);
    }

    @Override
    public String getFullQueue() {
        return fullQueue;
    }

    @Override
    public String name() {
        return String.format("RedisStreamWorker(queue=%s,group=%s)", fullQueue, group);
    }
}
