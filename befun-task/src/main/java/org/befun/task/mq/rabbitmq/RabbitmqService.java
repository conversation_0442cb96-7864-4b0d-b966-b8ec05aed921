package org.befun.task.mq.rabbitmq;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.task.ITaskExecutor;
import org.befun.task.configuration.TaskProperties;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.mq.AbstractMqService;
import org.befun.task.mq.ITaskService;
import org.befun.task.mq.ITaskWorker;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpoint;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class RabbitmqService extends AbstractMqService<TaskProperties.RabbitmqWorkerProperty> implements ITaskService {

    @Autowired
    private AmqpTemplate amqpTemplate;


    @Override
    protected void registerTask(Object registrar, ITaskWorker worker) {
        ((RabbitListenerEndpointRegistrar) registrar).registerEndpoint((RabbitListenerEndpoint) worker);
        log.info("已成功注册rabbitmq消费者：{}", worker.name());
    }

    @Override
    protected ITaskWorker createWorker(String group, String queue, String fullQueue, ITaskExecutor<?> executor) {
        return new RabbitmqWorker<>(getWorkerProperty().getExchange(), getFullQueue(executor), executor, this);
    }

    @Override
    public TaskProperties.RabbitmqWorkerProperty getWorkerProperty() {
        return taskProperties.getRabbitmq();
    }

    @Override
    public String addTask(TaskContextDto contextDto) {
        amqpTemplate.convertAndSend(getWorkerProperty().getExchange(), getFullQueue(contextDto), JsonHelper.toJson(contextDto));
        return contextDto.getId();
    }
}
