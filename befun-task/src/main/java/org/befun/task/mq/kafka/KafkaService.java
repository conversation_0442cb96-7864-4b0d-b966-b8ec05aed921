package org.befun.task.mq.kafka;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.task.ITaskExecutor;
import org.befun.task.configuration.TaskProperties;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.mq.AbstractMqService;
import org.befun.task.mq.ITaskService;
import org.befun.task.mq.ITaskWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.kafka.config.KafkaListenerEndpoint;
import org.springframework.kafka.config.KafkaListenerEndpointRegistrar;
import org.springframework.kafka.core.KafkaTemplate;

@Slf4j
public class KafkaService extends AbstractMqService<TaskProperties.KafkaWorkerProperty> implements ITaskService {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    protected void registerTask(Object registrar, ITaskWorker worker) {
        ((KafkaListenerEndpointRegistrar) registrar).registerEndpoint((KafkaListenerEndpoint) worker);
        log.info("已成功注册Kafka消费者：{}", worker.name());
    }

    @Override
    protected ITaskWorker createWorker(String group, String queue, String fullQueue, ITaskExecutor<?> executor) {
        return new KafkaWorker<>(group, fullQueue, executor, this);
    }

    @Override
    public TaskProperties.KafkaWorkerProperty getWorkerProperty() {
        return taskProperties.getKafka();
    }

    @Override
    public String addTask(TaskContextDto contextDto) {
        kafkaTemplate.send(getFullQueue(contextDto), JsonHelper.toJson(contextDto));
        return contextDto.getId();
    }

}
