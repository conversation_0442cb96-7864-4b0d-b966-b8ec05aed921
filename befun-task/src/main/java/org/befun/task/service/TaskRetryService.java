package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.mq.ITaskService;
import org.befun.task.mq.TaskRetryWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

@Slf4j
public class TaskRetryService {

    @Autowired
    private ITaskService taskService;
    @Autowired
    private TaskDelayService taskDelayService;

    /**
     * 尝试重试，如果没有重试，则会将此消息加入 dead queue
     *
     * @return false 没有重试，会将此消息加入 dead queue; true 已重新加入 task queue
     */
    public boolean tryRetry(TaskRetryWorker worker, TaskContextDto contextDto, Throwable e) {
        if (contextDto == null || worker == null || e == null) {
            log.warn("重试失败, worker({}) 不能为空, contextDto({}) 不能为空, exception({}) 不能为空", worker == null, contextDto == null, e == null);
            return false;
        }
        if (worker.isEnableRetry() && worker.getRetryException().isAssignableFrom(e.getClass())) {
            int maxAttempts = worker.getMaxAttempts();
            int retryCount = contextDto.getRetryCount();
            if (retryCount < maxAttempts) {
                int nextInterval = getNextInterval(worker.getIntervals(), retryCount);
                TaskContextDto retryRecord = contextDto.toBuilder().build();
                retryRecord.setRetryCount(retryRecord.getRetryCount() + 1);
                if (nextInterval <= 0) {
                    // 立即重试
                    log.warn("retry immediate, queue={}, id={}, retryCount={}, detail={}", worker.getFullQueue(), contextDto.getId(), retryRecord.getRetryCount(), contextDto.getDataJson());
                    taskService.addTask(retryRecord);
                } else {
                    // 延迟重试
                    long millis = worker.getIntervalUnit().toMillis(nextInterval);
                    log.warn("retry delay {} ms, queue={}, id={}, retryCount={}, detail={}", millis, worker.getFullQueue(), contextDto.getId(), retryRecord.getRetryCount(), contextDto.getDataJson());
                    taskDelayService.addTaskDelay(retryRecord, millis);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 获得下次重试的间隔
     */
    private int getNextInterval(int[] intervals, int retryCount) {
        int intervalLength = intervals.length;
        if (intervalLength == 0) {
            return 0;
        }
        int lastIndex = intervalLength - 1;
        if (retryCount > lastIndex) {
            return intervals[lastIndex];
        } else {
            return intervals[retryCount];
        }
    }
}
