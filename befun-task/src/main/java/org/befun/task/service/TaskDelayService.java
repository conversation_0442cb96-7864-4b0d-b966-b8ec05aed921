package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskDetailDto;
import org.befun.task.constant.TaskExecutionType;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.dto.TaskScoreDto;
import org.befun.task.metrics.TaskMetrics;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;

import java.time.Duration;
import java.time.temporal.TemporalAmount;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class TaskDelayService {

    @Autowired
    private ITaskService taskService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired(required = false)
    private TaskMetrics taskMetrics;

    /**
     * add delay task by context & temporalAmount
     */
    public long addTaskDelay(TaskContextDto contextDto, TemporalAmount temporalAmount) {
        String fullQueue = taskService.getFullQueue(contextDto);
        String fullDelayQueue = taskService.getFullDelayQueue();
        long timestamp = new Date().toInstant().plus(temporalAmount).toEpochMilli();
        log.info("add delay task key:{} to queue {}", timestamp, fullQueue);
        getZSetOpt().add(fullDelayQueue, JsonHelper.toJson(contextDto), timestamp);
        if (taskMetrics != null) {
            taskMetrics.producerIncrement(taskService.getDelayQueue());
        }
        return timestamp;
    }

    /**
     * add delay task by context & milli
     */
    public long addTaskDelay(TaskContextDto taskDto, long milli) {
        return addTaskDelay(taskDto, Duration.ofMillis(milli));
    }

    /**
     * add delay task by queue & detail & temporalAmount
     */
    public long addTaskDelay(String queue, BaseTaskDetailDto data, TemporalAmount temporalAmount) {
        return addTaskDelay(taskService.buildContext(queue, TaskExecutionType.DELAY, data), temporalAmount);
    }

    /**
     * add delay task by class & detail & temporalAmount
     */
    public long addTaskDelay(Class<?> taskClass, BaseTaskDetailDto data, TemporalAmount temporalAmount) {
        return addTaskDelay(taskService.getQueue(taskClass), data, temporalAmount);
    }

    /**
     * rangeByScore until now
     */
    public Set<TaskScoreDto> pullDelayedTasks() {
        long currentTimestamp = new Date().toInstant().toEpochMilli();
        Set<ZSetOperations.TypedTuple<String>> result = getZSetOpt().rangeByScoreWithScores(taskService.getFullDelayQueue(), 0, currentTimestamp);
        if (CollectionUtils.isEmpty(result)) {
            return new HashSet<>();
        }
        return result.stream().map(x -> {
            TaskScoreDto recordDto = new TaskScoreDto();
            recordDto.setScore(x.getScore());
            recordDto.setContextDto(JsonHelper.toObject(x.getValue(), TaskContextDto.class));
            return recordDto;
        }).collect(Collectors.toSet());
    }

    /**
     * remove specified task
     */
    public void removeDelayedTask(Double score) {
        getZSetOpt().removeRangeByScore(taskService.getFullDelayQueue(), score, score);
    }

    public ZSetOperations<String, String> getZSetOpt() {
        return redisTemplate.opsForZSet();
    }
}
