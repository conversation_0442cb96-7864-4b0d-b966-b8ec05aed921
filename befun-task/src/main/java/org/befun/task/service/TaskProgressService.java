package org.befun.task.service;


import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.NumberHelper;
import org.befun.task.constant.TaskStatus;
import org.befun.task.constant.TaskTypeBelong;
import org.befun.task.dto.TaskProgressDto;
import org.befun.task.dto.TaskType;
import org.befun.task.entity.TaskProgress;
import org.befun.task.repository.TaskProgressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

public class TaskProgressService {

    @Autowired
    protected TaskProgressRepository repository;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;
    private static final String HK_TOTAL_SIZE = "totalSize";
    private static final String HK_SUCCESS_SIZE = "successSize";
    private static final String HK_FAILED_SIZE = "failedSize";
    private static final String HK_STATUS = "status";
    private static final String HK_LAST_ACTIVE_TIME = "lastActiveTime";

    public TaskProgress get(Long id) {
        return id != null && id != 0L ? repository.findById(id).orElse(null) : null;
    }

    public TaskProgress require(Long id) {
        TaskProgress entity = this.get(id);
        if (entity == null) {
            throw new EntityNotFoundException(TaskProgress.class);
        } else {
            return entity;
        }
    }

    protected String getProgressKey(Long id) {
        return "progress:task:" + id;
    }

    /**
     * 修改任务总数
     */
    public TaskStatus updateTaskTotalSize(Long id, Integer totalSize) {
        return updateTaskTotalSize(id, totalSize, true);
    }

    /**
     * 修改任务总数
     */
    public TaskStatus updateTaskTotalSize(Long id, Integer totalSize, boolean flushToDb) {
        return updateTaskSize(id, HK_TOTAL_SIZE, totalSize, flushToDb);
    }

    /**
     * 添加任务总数
     */
    public TaskStatus appendTaskTotalSize(Long id, Integer totalSize) {
        return appendTaskTotalSize(id, totalSize, true);
    }

    /**
     * 添加任务总数
     */
    public TaskStatus appendTaskTotalSize(Long id, Integer totalSize, boolean flushToDb) {
        return appendTaskSize(id, HK_TOTAL_SIZE, totalSize, flushToDb);
    }

    /**
     * 修改任务成功数
     */
    public TaskStatus updateTaskSuccessSize(Long id, Integer successSize) {
        return updateTaskSuccessSize(id, successSize, true);
    }

    /**
     * 修改任务成功数
     */
    public TaskStatus updateTaskSuccessSize(Long id, Integer successSize, boolean flushToDb) {
        return updateTaskSize(id, HK_SUCCESS_SIZE, successSize, flushToDb);
    }

    /**
     * 添加任务成功数
     */
    public TaskStatus appendTaskSuccessSize(Long id, Integer successSize) {
        return appendTaskSuccessSize(id, successSize, true);
    }

    /**
     * 添加任务成功数
     */
    public TaskStatus appendTaskSuccessSize(Long id, Integer successSize, boolean flushToDb) {
        return appendTaskSize(id, HK_SUCCESS_SIZE, successSize, flushToDb);
    }

    /**
     * 修改任务失败数
     */
    public TaskStatus updateTaskFailedSize(Long id, Integer failedSize) {
        return updateTaskFailedSize(id, failedSize, true);
    }

    /**
     * 修改任务失败数
     */
    public TaskStatus updateTaskFailedSize(Long id, Integer failedSize, boolean flushToDb) {
        return updateTaskSize(id, HK_FAILED_SIZE, failedSize, flushToDb);
    }

    /**
     * 添加任务失败数
     */
    public TaskStatus appendTaskFailedSize(Long id, Integer failedSize) {
        return appendTaskFailedSize(id, failedSize, true);
    }

    /**
     * 添加任务失败数
     */
    public TaskStatus appendTaskFailedSize(Long id, Integer failedSize, boolean flushToDb) {
        return appendTaskSize(id, HK_FAILED_SIZE, failedSize, flushToDb);
    }

    /**
     * 修改任务状态
     */
    public TaskStatus updateTaskStatus(Long id, TaskStatus status) {
        return updateTaskStatus(id, status, true);
    }

    /**
     * 确认存在缓存，如果不存在，则从数据库同步过去
     */
    private boolean confirmHasCache(Long id, String key) {
        Boolean existsKey = stringRedisTemplate.hasKey(key);
        if (existsKey == null || !existsKey) {
            return syncTaskToCache(id) != null;
        }
        return true;
    }

    /**
     * 修改任务最后活跃时间
     */
    public void updateTaskActiveTime(Long id) {
        if (id == null) {
            return;
        }
        String progressKey = getProgressKey(id);
        if (!confirmHasCache(id, progressKey)) {
            return;
        }
        updateTaskActiveTime(progressKey);
    }

    /**
     * 修改任务最后活跃时间
     */
    private void updateTaskActiveTime(String progressKey) {
        stringRedisTemplate.opsForHash().put(progressKey, HK_LAST_ACTIVE_TIME, DateHelper.formatDateTime(new Date()));
    }

    /**
     * 修改任务状态
     */
    public TaskStatus updateTaskStatus(Long id, TaskStatus status, boolean flushToDb) {
        if (id == null || status == null) {
            return TaskStatus.NONE;
        }
        String progressKey = getProgressKey(id);
        if (!confirmHasCache(id, progressKey)) {
            return TaskStatus.NONE;
        }
        updateTaskActiveTime(progressKey);
        stringRedisTemplate.opsForHash().put(progressKey, HK_STATUS, status.name());
        if (flushToDb) {
            syncTaskToDb(id);
        }
        return status;
    }

    protected TaskStatus updateTaskSize(Long id, String hkKey, Integer size, boolean flushToDb) {
        if (id == null || size == null) {
            return TaskStatus.NONE;
        }
        String progressKey = getProgressKey(id);
        if (!confirmHasCache(id, progressKey)) {
            return TaskStatus.NONE;
        }
        updateTaskActiveTime(progressKey);
        stringRedisTemplate.opsForHash().put(progressKey, hkKey, size.toString());
        if (flushToDb) {
            syncTaskToDb(id);
        }
        return TaskStatus.NONE;
    }

    protected TaskStatus appendTaskSize(Long id, String hkKey, Integer size, boolean flushToDb) {
        if (id == null || size == null || size == 0) {
            return TaskStatus.NONE;
        }
        String progressKey = getProgressKey(id);
        if (!confirmHasCache(id, progressKey)) {
            return TaskStatus.NONE;
        }
        updateTaskActiveTime(progressKey);
        stringRedisTemplate.opsForHash().increment(progressKey, hkKey, size);
        if (flushToDb) {
            syncTaskToDb(id);
        }
        return TaskStatus.NONE;
    }

    public boolean updateTaskProgress(Long id, int appendSuccessSize, int appendFailedSize) {
        boolean completed = false;
        String progressKey = getProgressKey(id);
        if (!confirmHasCache(id, progressKey)) {
            return false;
        }
        updateTaskActiveTime(progressKey);
        long successSize = stringRedisTemplate.opsForHash().increment(progressKey, HK_SUCCESS_SIZE, appendSuccessSize);
        long failedSize = stringRedisTemplate.opsForHash().increment(progressKey, HK_FAILED_SIZE, appendFailedSize);
        Map<Object, Object> map = stringRedisTemplate.opsForHash().entries(progressKey);
        if (MapUtils.isNotEmpty(map)) {
            TaskProgressDto cacheProgress = new TaskProgressDto(null, 0, successSize, failedSize);
            try {
                TaskStatus status = castMapValue(map, HK_STATUS, i -> true, s -> EnumHelper.parse(TaskStatus.values(), s), null);
                int totalSize = castMapValue(map, HK_TOTAL_SIZE, NumberUtils::isDigits, Integer::parseInt, 0);
                if (status == TaskStatus.RUNNING && totalSize > 0 && (successSize + failedSize) >= totalSize) {
                    status = TaskStatus.SUCCESS;
                    updateTaskStatus(id, status, false);
                    completed = true;
                }
                cacheProgress.setStatus(status);
                cacheProgress.setTotalSize(totalSize);
            } finally {
                syncTaskToDb(get(id), i -> cacheProgress);
            }
        }
        return completed;
    }

    public TaskProgressDto getCacheProgress(Long id) {
        String progressKey = getProgressKey(id);
        Map<Object, Object> map = stringRedisTemplate.opsForHash().entries(progressKey);
        if (MapUtils.isNotEmpty(map)) {
            TaskStatus status = castMapValue(map, HK_STATUS, i -> true, s -> EnumHelper.parse(TaskStatus.values(), s), null);
            int totalSize = castMapValue(map, HK_TOTAL_SIZE, NumberUtils::isDigits, Integer::parseInt, 0);
            int successSize = castMapValue(map, HK_SUCCESS_SIZE, NumberUtils::isDigits, Integer::parseInt, 0);
            int failedSize = castMapValue(map, HK_FAILED_SIZE, NumberUtils::isDigits, Integer::parseInt, 0);
            String lastActiveTime = castMapValue(map, HK_LAST_ACTIVE_TIME, i -> true, Function.identity(), null);
            if (status != null) {
                if (status == TaskStatus.RUNNING && totalSize > 0 && (successSize + failedSize) >= totalSize) {
                    status = TaskStatus.SUCCESS;
                    updateTaskStatus(id, status, false);
                }
                return new TaskProgressDto(status, totalSize, successSize, failedSize, lastActiveTime);
            }
        }
        return null;
    }

    public TaskProgress syncTaskToDb(Long id) {
        return syncTaskToDb(get(id));
    }

    public TaskProgress syncTaskToDb(TaskProgress entity) {
        return syncTaskToDb(entity, this::getCacheProgress);
    }

    public TaskProgress syncTaskToDb(TaskProgress entity, Function<Long, TaskProgressDto> getCacheProgress) {
        if (entity == null) {
            return null;
        }
        Long id = entity.getId();
        TaskProgressDto cacheProgress = getCacheProgress.apply(id);
        if (cacheProgress != null) {
            Optional.ofNullable(cacheProgress.getStatus()).ifPresent(entity::setStatus);
            entity.setTotalSize((int) cacheProgress.getTotalSize());
            entity.setSuccessSize((int) cacheProgress.getSuccessSize());
            entity.setFailedSize((int) cacheProgress.getFailedSize());
            repository.save(entity);
            expireCache(getProgressKey(id), cacheProgress.getStatus());
        }
        return entity;
    }

    private <T> T castMapValue(Map<Object, Object> map, String key, Predicate<String> test, Function<String, T> cast, T defaultValue) {
        Object value = map.get(key);
        String v;
        if (value != null && test.test(v = value.toString())) {
            return cast.apply(v);
        }
        return defaultValue;
    }

    protected TaskProgress syncTaskToCache(Long id) {
        return syncTaskToCache(get(id));
    }

    public TaskProgress syncTaskToCache(TaskProgress entity) {
        if (entity == null) {
            return null;
        }
        String progressKey = getProgressKey(entity.getId());
        stringRedisTemplate.opsForHash().putAll(progressKey, Map.of(
                HK_STATUS, entity.getStatus().name(),
                HK_TOTAL_SIZE, Optional.ofNullable(entity.getTotalSize()).orElse(0).toString(),
                HK_SUCCESS_SIZE, Optional.ofNullable(entity.getSuccessSize()).orElse(0).toString(),
                HK_FAILED_SIZE, Optional.ofNullable(entity.getFailedSize()).orElse(0).toString()
        ));
        expireCache(progressKey, entity.getStatus());
        return entity;
    }

    protected void expireCache(String progressKey, TaskStatus status) {
        if (status != null && status.isCompleted()) {
            stringRedisTemplate.expire(progressKey, Duration.ofHours(1));
        }
    }

    protected TaskProgress createTask(Long orgId, Long userId, TaskType type, Integer totalSize, Object params) {
        TaskProgress entity = new TaskProgress();
        entity.setOrgId(orgId);
        entity.setUserId(userId);
        entity.setType(type.name());
        entity.setTypeBelong(type.getBelongTo());
        entity.setStatus(TaskStatus.INIT);
        entity.setTotalSize(totalSize);
        entity.setParams(JsonHelper.toJson(params != null ? List.of(params) : List.of()));
        repository.save(entity);
        syncTaskToCache(entity);
        updateTaskActiveTime(entity.getId());
        return entity;
    }

    protected TaskProgress createTask(Long orgId, Long userId, TaskType type, Integer totalSize, Object params, Long relationId) {
        TaskProgress entity = new TaskProgress();
        entity.setOrgId(orgId);
        entity.setUserId(userId);
        entity.setType(type.name());
        entity.setTypeBelong(type.getBelongTo());
        entity.setStatus(TaskStatus.INIT);
        entity.setTotalSize(totalSize);
        entity.setParams(JsonHelper.toJson(params != null ? List.of(params) : List.of()));
        entity.setRelationId(relationId);
        repository.save(entity);
        syncTaskToCache(entity);
        updateTaskActiveTime(entity.getId());
        return entity;
    }

    protected TaskProgress updateTask(Long id, TaskStatus status, Function<String, String> replaceParams, Function<String, String> replaceResult) {
        return Optional.ofNullable(get(id)).map(entity -> {
            entity.setStatus(status);
            if (replaceParams != null) {
                entity.setParams(replaceParams.apply(entity.getParams()));
            }
            if (replaceResult != null) {
                entity.setResult(replaceResult.apply(entity.getResult()));
            }
            repository.save(entity);
            TaskProgressDto cacheProgress = getCacheProgress(id);
            if (cacheProgress == null) {
                syncTaskToCache(entity);
                updateTaskActiveTime(entity.getId());
            } else {
                updateTaskStatus(id, status, false);
            }
            return entity;
        }).orElse(null);
    }

    public boolean cancel(Long id) {
        updateTaskStatus(id, TaskStatus.CANCELED, true);
        return true;
    }

    /**
     * 如果最后活跃时间到当前的时间的时间间隔超过了 autoFailureInterval (秒) 则直接设置失败
     */
    public TaskProgressDto progress(Long id, long autoFailureInterval) {
        if (id != null && id > 0) {
            TaskProgressDto cacheProgress = getCacheProgress(id);
            if (cacheProgress != null) {
                LocalDateTime lastActiveTime;
                if (autoFailureInterval > 0 && (lastActiveTime = DateHelper.parseDateTime(cacheProgress.getLastActiveTime())) != null) {
                    LocalDateTime now = LocalDateTime.now();
                    if (Duration.between(lastActiveTime, now).getSeconds() > autoFailureInterval) {
                        updateTaskStatus(id, TaskStatus.FAILED, true);
                    }
                }
                return cacheProgress;
            }
            TaskProgress entity = syncTaskToCache(id);
            if (entity != null) {
                return new TaskProgressDto(entity.getStatus(), NumberHelper.unbox(entity.getTotalSize()), NumberHelper.unbox(entity.getSuccessSize()), NumberHelper.unbox(entity.getFailedSize()));
            }
        }
        return new TaskProgressDto(TaskStatus.FAILED, 0, 0, 0);
    }

    public TaskProgressDto progress(Long id) {
        return progress(id, 0);
    }

    public Optional<TaskProgress> getLastByType(Long orgId, Long userId, TaskType taskType) {
        return getLastByType(orgId, userId, taskType, null);
    }

    public Optional<TaskProgress> getLastByType(Long orgId, Long userId, TaskType taskType, Long relationId) {
        GenericSpecification<TaskProgress> specification = buildQuery(orgId, userId, taskType, relationId);
        Page<TaskProgress> page = repository.findAll(specification, PageRequest.of(0, 1, Sort.by("createTime").descending()));
        if (page.hasContent()) {
            TaskProgress entity = page.getContent().get(0);
            TaskProgressDto cacheProgress = getCacheProgress(entity.getId());
            if (cacheProgress != null) {
                syncTaskToDb(entity);
            }
            return Optional.of(entity);
        }
        return Optional.empty();
    }

    private GenericSpecification<TaskProgress> buildQuery(Long orgId, Long userId, TaskType taskType, Long relationId) {
        GenericSpecification<TaskProgress> specification = new GenericSpecification<>();
        specification.add(new ResourceQueryCriteria("orgId", orgId));
        specification.add(new ResourceQueryCriteria("type", taskType.name()));
        if (taskType.getBelongTo() == TaskTypeBelong.USER) {
            specification.add(new ResourceQueryCriteria("userId", userId));
        }
        if (relationId != null) {
            specification.add(new ResourceQueryCriteria("relationId", relationId));
        }
        return specification;
    }

    public List<TaskProgress> getAllByType(Long orgId, Long userId, TaskType taskType, Long relationId) {
        GenericSpecification<TaskProgress> specification = buildQuery(orgId, userId, taskType, relationId);
        return repository.findAll(specification, Sort.by("createTime").descending());
    }


}
