package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Slf4j
public class TaskDelayHelper {

    @Autowired
    private StringRedisTemplate redisTemplate;
    private final static String LATEST_TIME = "latestTime";

    public Duration calcAvailableDuration(String key, int limiter, int intervalSecond) {
        LocalDateTime startInterval = startInterval(LocalDateTime.now(), intervalSecond);
        LocalDateTime latest = latestTime(key, intervalSecond);
        int intervalTimes = 0;
        if (latest != null && latest.isAfter(startInterval)) {
            while (latest.isAfter(startInterval)) {
                intervalTimes++;
                startInterval = startInterval.plusSeconds(intervalSecond);
            }
        }
        do {
            String hashKey = hashKey(startInterval);
            long count = hashOpt().increment(key, hashKey, 1);
            if (count <= limiter) {
                hashOpt().put(key, LATEST_TIME, hashKey);
                break;
            } else {
                intervalTimes++;
                hashOpt().increment(key, hashKey, -1);
                startInterval = startInterval.plusSeconds(intervalSecond);
            }
        } while (true);
        return intervalTimes == 0 ? null : Duration.ofSeconds((long) intervalSecond * intervalTimes);
    }

    private String hashKey(LocalDateTime startInterval) {
        return DateHelper.format(startInterval, DateHelper.DATE_TIME_FORMATTER2);
    }

    private LocalDateTime latestTime(String key, int intervalSecond) {
        String latestTime = hashOpt().get(key, LATEST_TIME);
        if (StringUtils.isNotEmpty(latestTime)) {
            return startInterval(LocalDateTime.parse(latestTime, DateHelper.DATE_TIME_FORMATTER2), intervalSecond);
        }
        return null;
    }

    protected HashOperations<String, String, String> hashOpt() {
        return redisTemplate.opsForHash();
    }

    /**
     * 计算指定时间所在的时间区间段
     */
    public static LocalDateTime startInterval(LocalDateTime time, int intervalSecond) {
        LocalDateTime timeStart = time.truncatedTo(ChronoUnit.DAYS);
        long seconds = Duration.between(timeStart, time).getSeconds();
        long div = seconds / intervalSecond;
        return timeStart.plusSeconds(div * intervalSecond);
    }

//    public static void main(String[] args) {
//        final ConcurrentHashMap<String, String> map = new ConcurrentHashMap<>();
//        TaskDelayHelper taskDelayHelper = new TaskDelayHelper() {
//            @Override
//            protected HashOperations<String, String, String> hashOpt() {
//
//                return new HashOperations<String, String, String>() {
//
//
//                    @Override
//                    public String get(String key, Object hashKey) {
//                        return map.get(hashKey);
//                    }
//
//                    @Override
//                    public Long increment(String key, String hashKey, long delta) {
//                        synchronized (map) {
//                            long old = Optional.ofNullable(map.get(hashKey)).map(Long::parseLong).orElse(0L);
//                            map.put(hashKey, String.valueOf(old + delta));
//                            return old + delta;
//                        }
//                    }
//
//                    @Override
//                    public Long delete(String key, Object... hashKeys) {
//                        return null;
//                    }
//
//                    @Override
//                    public Boolean hasKey(String key, Object hashKey) {
//                        return null;
//                    }
//
//
//                    @Override
//                    public List<String> multiGet(String key, Collection<String> hashKeys) {
//                        return null;
//                    }
//
//
//                    @Override
//                    public Double increment(String key, String hashKey, double delta) {
//                        return null;
//                    }
//
//                    @Override
//                    public Set<String> keys(String key) {
//                        return null;
//                    }
//
//                    @Override
//                    public Long lengthOfValue(String key, String hashKey) {
//                        return null;
//                    }
//
//                    @Override
//                    public Long size(String key) {
//                        return null;
//                    }
//
//                    @Override
//                    public void putAll(String key, Map<? extends String, ? extends String> m) {
//
//                    }
//
//                    @Override
//                    public void put(String key, String hashKey, String value) {
//
//                    }
//
//                    @Override
//                    public Boolean putIfAbsent(String key, String hashKey, String value) {
//                        return null;
//                    }
//
//                    @Override
//                    public List<String> values(String key) {
//                        return null;
//                    }
//
//                    @Override
//                    public Map<String, String> entries(String key) {
//                        return null;
//                    }
//
//                    @Override
//                    public Cursor<Map.Entry<String, String>> scan(String key, ScanOptions options) {
//                        return null;
//                    }
//
//                    @Override
//                    public RedisOperations<String, ?> getOperations() {
//                        return null;
//                    }
//                };
//            }
//        };
//
//        IntStream.range(0, 100).parallel().forEach(i -> {
//            try {
//                Thread.sleep(RandomUtils.nextInt(100, 10000));
//            } catch (InterruptedException e) {
//                throw new RuntimeException(e);
//            }
//            System.out.println(i + ":" + taskDelayHelper.calcAvailableDuration("abc", 1, 120));
//        });
//    }
}
