package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.task.entity.Holiday;
import org.befun.task.repository.HolidayRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class CalendarService {

    @Autowired
    private HolidayRepository holidayRepository;

    private Date lastSyncTime = null;
    private Map<Date, Holiday> holidayMap = new HashMap<>();

    /**
     * 初始化
     */
//    @PostConstruct
//    private void setup() {
//        this.load();
//    }

    /**
     * 通过内存MAP判定是否节假日
     * @return
     */
    public boolean isHoliday(LocalDate date) {
        this.ensureMapUpdated();
        if (this.holidayMap.containsKey(date)) {
            return true;
        }

        return false;
    }

    /**
     * 通过最新的一条记录判定是否需要重新加载Map
     */
    private void ensureMapUpdated() {
        Holiday lastHoliday = holidayRepository.findFirstByOrderByCreateTimeDesc();
        if (lastSyncTime == null || lastHoliday.getCreateTime().after(lastSyncTime)) {
            log.info("detected new holiday record, refresh map");
            this.load();
        }
    }

    /**
     * load map
     */
    private void load() {
        this.holidayMap = holidayRepository.findAll()
                .stream()
                .collect(Collectors.toMap(Holiday::getDate, Function.identity()));
    }
}
