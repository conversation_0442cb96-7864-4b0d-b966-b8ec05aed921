package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.task.annotation.TaskLock;
import org.befun.task.dto.TaskContextDto;
import org.befun.task.dto.TaskScoreDto;
import org.befun.task.metrics.TaskMetrics;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Set;

/**
 * 负责从 delay queue 搬运 延迟任务到相对应的 queue
 */
@Slf4j
public class TaskDelayScheduled {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private TaskDelayService taskDelayService;
    @Autowired(required = false)
    private TaskMetrics taskMetrics;

    /**
     * carry delay task to task stream
     */
    @Scheduled(cron = "${befun.task.delay.cron}")
    @TaskLock(key = "carry", seconds = 60)
    public void carryDelayTasks() {
        log.info("scheduler service start to carry");
        try {
            Set<TaskScoreDto> tasks = taskDelayService.pullDelayedTasks();
            tasks.forEach(task -> {
                TaskContextDto contextDto = task.getContextDto();
                log.debug("carry task {} to queue {}", contextDto.getId(), contextDto.getQueue());
                taskService.addTask(contextDto);
                taskDelayService.removeDelayedTask(task.getScore());
                if (taskMetrics != null) {
                    taskMetrics.consumerIncrement(taskService.getDelayQueue());
                }
            });
        } catch (Exception ex) {
            log.error("failed to carry due to exception {}", ex.getMessage());
        }
        log.info("scheduler service finished carry");
    }
}
