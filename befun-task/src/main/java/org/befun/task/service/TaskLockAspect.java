package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.befun.task.annotation.TaskLock;
import org.befun.task.mq.ITaskService;
import org.befun.task.utils.SpELUtils;
import org.hibernate.sql.OracleJoinFragment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Aspect
public class TaskLockAspect {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Pointcut("@annotation(org.befun.task.annotation.TaskLock)")
    public void lockTask() {
    }

    @Around("lockTask()")
    public Object applyLock(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sig = (MethodSignature) joinPoint.getSignature();
        Method method = sig.getMethod();
        TaskLock lock = method.getAnnotation(TaskLock.class);
        String key = taskService.getWorkerProperty().getPrefix() + ".lock." + lock.key();
        if(lock.isSpEl){
            String parseKey = this.supportSpEL(joinPoint,lock.key());
            key = taskService.getWorkerProperty().getPrefix() + ".lock." + parseKey;
        }
        Boolean lockResult = stringRedisTemplate.opsForValue().setIfAbsent(key, "true", Duration.ofSeconds(lock.seconds()));
        if (lockResult != null && lockResult) {
            try {
                return joinPoint.proceed();
            } catch (Exception ex) {
                log.error("error to deal lock task {}", ex.getMessage());
                ex.printStackTrace();
            } finally {
                stringRedisTemplate.delete(key);
            }
        } else {
            log.info("look like the method is busy, {} will skip", method.getName());
        }
        return null;
    }

    /**
     *
     * @param joinPoint
     * @param expression
     *
     * 调用当前类方法 -> #this.methodName()
     * 方法参数值 -> #paramName
     * 调用bean方法 -> @beanName.method([#paramName])
     *
     * @return
     */
    public String supportSpEL(ProceedingJoinPoint joinPoint,String expression){
        String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        Object[] parameterValues = joinPoint.getArgs();
        Map<String,Object> variableMap = new HashMap<>();

        if (ArrayUtils.isNotEmpty(parameterNames)) {
            for (int index = 0, length = parameterNames.length; index < length; index++) {
                variableMap.put(parameterNames[index], parameterValues[index]);
            }
        }
        Object obj = joinPoint.getTarget();
        try {
             return (String) SpELUtils.parseExpression(obj,variableMap,expression);
        } catch (SpelEvaluationException e) {
            log.error("{} is not SpEl expression,{}",expression,e.getMessage());
            return expression;
        }
    }

}
