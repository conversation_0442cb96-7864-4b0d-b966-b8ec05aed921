<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.befun</groupId>
        <artifactId>parent</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>org.befun.task</groupId>
    <artifactId>befun-task</artifactId>
    <version>${revision}.${sha1}-${changelist}</version>
    <name>befun-task</name>
    <packaging>jar</packaging>
    <description>Befun Task Project</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>${java.version}</java.version>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.befun</groupId>
            <artifactId>befun-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>