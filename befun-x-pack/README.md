# x-pack for enterprise extension

## application.property
```
extension:
    mail:
      host: smtp.partner.outlook.cn
      port: 587
      username: <EMAIL>
      from: <EMAIL>
      password: Survey+0627
      templates:
        - name: default
          subject: 标题${name}
          enable-html: true
          content: 尊敬的用户，${name} 已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！
    sms:
      vendor: feige
      providers:
        - name: feige
          app-id: hysj_test
          app-secret: a9dd33f910e41f5d74dbd7147
          templates:
            - name: default
              id: 116175
              signature: 349733
              content: 尊敬的用户，已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！

```

## SmsService
- 抽象SmsProvider，暂时支持飞歌
- 封装动态模版引擎 (TemplateEngine)

### Sms Provider 属性字段
 - name provider 名称
 - app-id: 开发者id
 - app-secret: 开发者密钥
 - signature: 签名
 - templates: 模版列表

### Template 属性字段
- name template name
- id: 模版id
- signature: 签名
- content: 内容模版

### Example code
```
@Autowired
private SmsService smsService;

smsService.sendMessageByTemplate(templateName, mobile, parameters);
```

## MailService
- 基于spring mail
- 封装动态模版引擎 (TemplateEngine)

### Mail Provider 属性字段
- host SMTP host
- port SMTP port
- username SMTP username
- password SMTP password
- from mail from user
- templates: 模版列表

### Template 属性字段
- name template name
- subject: 标题
- content: 内容模版
- enable-html: 是否开启HTML
- default-encoding: 默认编码 默认值 UTF-8

```
@Autowired
private MailService mailService;

mailService.sendMessageByTemplate(templateName, email, parameters);
```


### 本地文件上传下载

* sql文件
```
-- 这里使用的是 mysql
CREATE TABLE `file`
(
    `id`                bigint(20)                                        NOT NULL AUTO_INCREMENT COMMENT '文件id',
    `url`               varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件访问地址',
    `size`              bigint(20)                                              NULL DEFAULT NULL COMMENT '文件大小，单位字节',
    `filename`          varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
    `original_filename` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原始文件名',
    `base_path`         varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基础存储路径',
    `path`              varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储路径',
    `ext`               varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '文件扩展名',
    `platform`          varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '存储平台',
    `th_url`            varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图访问路径',
    `th_filename`       varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图名称',
    `th_size`           bigint(20)                                              NULL DEFAULT NULL COMMENT '缩略图大小，单位字节',
    `object_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '文件所属对象id',
    `object_type`       varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '文件所属对象类型，例如用户头像，评价图片',
    `create_time`       datetime(0)                                             NULL DEFAULT NULL COMMENT '创建时间',
    `modify_time`       datetime(0)                                             NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '文件记录表'
  ROW_FORMAT = Dynamic;
```
* 配置文件
```
hanyi:
  common:
    file-storage:
      default-platform: default
      local:
        - platform:  default # 存储平台标识
          enable-storage: true
          enable-access: true
          domain: "http://127.0.0.1/tmp/files/"
          base-path: /tmp/files/
          path-patterns: /tmp/files/**
      aliyun-oss:
        - platform: oss
          enable-storage: true
          access-key: ${ALICLOUD_ACCESS_KEY:LTAI4G5RfyxPtajMojKJPvmM}
          secret-key: ${ALICLOUD_SECRET_KEY:******************************}
          end-point: ${ALICLOUD_OSS_ENDPOINT:https://oss-cn-shenzhen.aliyuncs.com}
          bucket-name: ${ALICLOUD_OSS_BUCKET:dev-assets-sp}
          domain: ${ALICLOUD_OSS_DOMAIN:http://dev-assets.surveyplus.cn/}
          base-path: ${ALICLOUD_OSS_BASEPATH:lite/}
```
* 阿里云
```
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.6.0</version>
</dependency>
```
