<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.befun</groupId>
        <artifactId>parent</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>org.befun.extension</groupId>
    <artifactId>befun-x-pack</artifactId>
    <version>${revision}.${sha1}-${changelist}</version>
    <name>befun-x-pack</name>
    <packaging>jar</packaging>
    <description>Befun Extension Package</description>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>${java.version}</java.version>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-activemq</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <version>4.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.30</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-afs</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi.common</groupId>
            <artifactId>file-storage</artifactId>
            <version>0.1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.befun</groupId>
            <artifactId>befun-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.guessencoding</groupId>
            <artifactId>guessencoding</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi.common</groupId>
            <artifactId>ip-resolver</artifactId>
            <version>${hanyi.common.ip-resolver.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>fluent-hc</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hanyi.common</groupId>
            <artifactId>rengine</artifactId>
            <version>1.8.13</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hanyi.common</groupId>
            <artifactId>rserve</artifactId>
            <version>1.8.13</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>