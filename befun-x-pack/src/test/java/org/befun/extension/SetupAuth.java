package org.befun.extension;

import com.google.common.collect.Lists;
import org.befun.core.dto.UserDto;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.stream.Collectors;

public class SetupAuth {

    @Autowired
    private RedisTemplate<String, String> template;

    protected final String mock_token_org1_user1 = "mock_token_org1_user1";
    protected final String mock_token_org1_user2 = "mock_token_org1_user2";
    protected final String mock_token_org2_user1 = "mock_token_org2_user1";
    protected final String mock_token_org2_user2 = "mock_token_org2_user2";
    protected final Long org1_user1 = 1L;
    protected final Long org1_user2 = 3L;
    protected final Long org2_user1 = 2L;
    protected final Long org2_user2 = 4L;
    protected final Long orgId1 = 1L;
    protected final Long orgId2 = 2L;
    protected final Long org1_department1 = 1L;
    protected final Long org1_department2 = 3L;
    protected final Long org2_department1 = 2L;
    protected final Long org2_department2 = 4L;
    protected final List<Long> org1AdminSubDepartmentIds = Lists.newArrayList(org1_department1, org1_department2);
    protected final List<Long> org1SubDepartmentIds = Lists.newArrayList(org1_department2);
    protected final List<Long> org2AdminSubDepartmentIds = Lists.newArrayList(org2_department1, org2_department2);
    protected final List<Long> org2SubDepartmentIds = Lists.newArrayList(org2_department2);

    @BeforeEach
    public void setupAuth() {
        UserDto user1 = newUser(org1_user1, orgId1, org1_department1, true, org1AdminSubDepartmentIds);
        UserDto user1_1 = newUser(org1_user2, orgId1, org1_department2, false, org1SubDepartmentIds);
        UserDto user2 = newUser(org2_user1, orgId2, org2_department1, true, org2AdminSubDepartmentIds);
        UserDto user2_1 = newUser(org2_user2, orgId2, org2_department1, false, org2SubDepartmentIds);
        saveToken(mock_token_org1_user1, user1);
        saveToken(mock_token_org1_user2, user1_1);
        saveToken(mock_token_org2_user1, user2);
        saveToken(mock_token_org2_user2, user2_1);
    }

    private UserDto newUser(long userId, long orgId, long departmentId, boolean isAdmin, List<Long> subDepartmentIds) {
        UserDto user = new UserDto();
        user.setId(userId);
        user.setUsername("用户" + userId);
        user.setOrgId(orgId);
//        user.setDepartmentId(departmentId);
        user.setIsAdmin(isAdmin);
        user.setSubDepartmentIds(subDepartmentIds);
        return user;
    }

    private void saveToken(String token, UserDto user) {
        template.opsForHash().put("session:" + token, "userId", user.getId().toString());
        template.opsForHash().put("session:" + token, "username", user.getUsername());
        template.opsForHash().put("session:" + token, "orgId", user.getOrgId().toString());
//        template.opsForHash().put("session:" + token, "departmentId", user.getDepartmentId().toString());
        template.opsForHash().put("session:" + token, "is_admin", user.getIsAdmin() ? "1" : "0");
        template.opsForHash().put("session:" + token, "departments", user.getSubDepartmentIds().stream().map(Object::toString).collect(Collectors.joining(",", "[", "]")));
    }
}
