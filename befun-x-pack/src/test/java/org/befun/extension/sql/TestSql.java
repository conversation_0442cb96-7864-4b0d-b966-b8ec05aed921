package org.befun.extension.sql;

import org.befun.extension.nativesql.SqlBuilder;
import org.junit.jupiter.api.Test;

public class TestSql {

    @Test
    public void test() {
        SqlBuilder sqlBuilder = SqlBuilder.select("select * form user u")
                .join("inner join user_role ur on ur.user_id=u.id and ur.role_id=%d", 1).ifTrue(true)
                .join("inner join user_role ur2 on ur2.user_id=u.id and ur.role_id=%d", 2).ifTrue(true)
                .where("u.status=1").alwaysTrue()
                .groupBy("u.status,u.delete")
                .groupBy("u.gender")
                .orderByDesc("u.create_time")
                .orderByAsc("u.id")
                .limit(2, 10);

        System.out.println(sqlBuilder.buildSelectSql());
    }
}
