package org.befun.extension.service;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.utils.DateHelper;
import org.befun.extension.TestRedisConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext()
class OpenTokenServiceTest {

    @Autowired
    private OpenTokenService openTokenService;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Param {
        private long id;
        private String name;
        private Date birthday;
        private int age;
        private Integer score;
    }

    @Test
    void createToken() throws InterruptedException {
        Date birthday = DateHelper.toDate(DateHelper.parseDateTime("2020-10-23 12:12:12"));
        Param params2 = new Param(1L, "abc", birthday, 2, 99);
        Map<String, Object> params1 = Map.of(
                "id", 1L,
                "name", "abc",
                "param", params2,
                "age", 2,
                "score", 99);
        String token1 = openTokenService.createToken(params1);
        String token2 = openTokenService.createToken(2, params1);
        String token3 = openTokenService.createToken(params2);
        String token4 = openTokenService.createToken(2, params2);

        Optional.of(openTokenService.getTokenParams(token1)).ifPresent(p -> {
            assertEquals(1, p.get("id"));
            assertEquals("abc", p.get("name"));
            assertEquals(1,((Map<String,Object>) p.get("param")).get("id"));
            assertEquals(2, p.get("age"));
            assertEquals(99, p.get("score"));
        });
        Optional.of(openTokenService.getTokenParams(token3, Param.class)).ifPresent(p -> {
            assertEquals(1, p.getId());
            assertEquals("abc", p.getName());
            assertEquals(birthday, p.getBirthday());
            assertEquals(2, p.getAge());
            assertEquals(99, p.getScore());
        });
        Thread.sleep(3000);
        assertNull(openTokenService.getTokenParams(token2));
        assertNull(openTokenService.getTokenParams(token4));
    }

}