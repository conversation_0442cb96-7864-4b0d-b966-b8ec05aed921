package org.befun.extension.service;

import org.befun.extension.TestRedisConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class LinkServiceTest {

    @Autowired
    private LinkService linkService;

    @Test
    void toShortUrl() {
        String shortUrl1 = linkService.toShortUrl("http://127.0.0.1:8080/api/example/test", true);
        assertNotNull(shortUrl1);
        String shortUrl2 = linkService.toShortUrl("http://127.0.0.1:8080/api/example/test", false);
        assertNotNull(shortUrl2);
        String shortUrl3 = linkService.toShortUrl("http://127.0.0.1:8080/api/example/test1", false);
        assertNotNull(shortUrl3);
    }
}