package org.befun.extension.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.befun.extension.TestRedisConfiguration;
import org.befun.extension.property.SmartVerifyProperty;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;


import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @Description
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SmartVerifyServiceTest {

    @Autowired(required = false)
    SmartVerifyService verifyService;
    @Autowired
    private SmartVerifyProperty verifyProperty;

    IAcsClient client = null;


    @PostConstruct
    public void setUp() throws Exception {
        //YOUR ACCESS_KEY、YOUR ACCESS_SECRET请替换成您的阿里云accesskey id和secret
        IClientProfile profile = DefaultProfile.getProfile(verifyProperty.getRegionId(), verifyProperty.getAccessKeyId(), verifyProperty.getAccessKeySecret());
        client = new DefaultAcsClient(profile);
        DefaultProfile.addEndpoint(verifyProperty.getRegionId(), verifyProperty.getProduct(), verifyProperty.getDomain());
    }


    @Test
    public void testVerifyService() {

        AuthenticateSigRequest request = new AuthenticateSigRequest();
        request.setSessionId("13211111111");// 会话ID。必填参数，从前端sucess回调中获取，不可更改。
        request.setSig("Pc5WB8gokVn0xfeu%2FZV%2BiNM1dgI%3D");// 签名串。必填参数，从前端sucess回调中获取，不可更改。
        request.setToken("2BiNM1dgI");// 请求唯一标识。必填参数，从前端sucess回调中获取，不可更改。
        request.setScene("ic_other");// 场景标识。必填参数，与前端页面填写数据一致，不可更改。
        request.setAppKey(verifyProperty.getAppKey());// 应用类型标识。必填参数，后端填写。
        request.setRemoteIp("***************");// 客户端IP。必填参数，后端填写。

//        System.out.println("AccessKeySecret: " + verifyProperty.getAccessKeySecret());
//        System.out.println("AppKey: " + verifyProperty.getAppKey());

        //response的code枚举：100验签通过，900验签失败。
        AuthenticateSigResponse response = null;
        try {
            response = client.getAcsResponse(request);
        } catch (ClientException e) {
            e.printStackTrace();
        }

        System.out.println("checkShowJsonItemName=" + response.checkShowJsonItemName() + ",getDetail="+ response.getDetail() +  ",getMsg=" + response.getMsg());
        System.out.println("RequestId+" + response.getRequestId());
        System.out.println("RiskLevel=" + response.getRiskLevel());

        if (response.getCode() == 100) {
            System.out.println("验签通过,code=" + response.getCode());
        } else {
            System.out.println("验签失败,code=" + response.getCode());
        }
    }
}


