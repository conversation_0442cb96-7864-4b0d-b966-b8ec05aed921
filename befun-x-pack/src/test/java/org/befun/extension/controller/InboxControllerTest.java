package org.befun.extension.controller;

import org.befun.extension.AssertController;
import org.befun.extension.TestRedisConfiguration;
import org.befun.extension.entity.InboxMessage;
import org.befun.extension.repository.InboxMessageRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class InboxControllerTest extends AssertController {

    @Autowired
    private InboxMessageRepository inboxMessageRepository;

    private static final String URL_BASE = "/inbox-messages";
    private static final String URL_READ_ONE = "/inbox-messages/%d/read-one";
    private static final String URL_READ_ALL = "/inbox-messages/read-all";

    @Test
    void create() throws Exception {
        // 添加一条消息，然后从数据库中可查询
        String body = "{" +
                "    \"type\": 1," +
                "    \"title\": \"hello\"," +
                "    \"description\": \"hello world\"" +
                "}";
        postOk(URL_BASE, body, mock_token_org1_user1, null);
        List<InboxMessage> messages = getFromDb(1L);
        Assertions.assertNotNull(messages);
        Assertions.assertEquals(messages.size(), 1);
        messages.forEach(i -> {
            Assertions.assertEquals(i.getReadStatus(), false);
            Assertions.assertEquals(i.getOrgId(), orgId1);
            Assertions.assertEquals(i.getUserId(), org1_user1);
        });
    }

    @Test
    @Sql(statements = INIT_SQL_MESSAGE)
    void findAll() throws Exception {
        getOk(URL_BASE, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.meta.total").value(6))
                        .andExpect(jsonPath("$.items.length()").value(6))
        );
        getOk(URL_BASE, mock_token_org2_user1, actions ->
                actions.andExpect(jsonPath("$.meta.total").value(7))
                        .andExpect(jsonPath("$.items.length()").value(7))
        );

        get403(URL_BASE);
    }

    @Test
    @Sql(statements = INIT_SQL_MESSAGE)
    void readOne() throws Exception {
        // user1 标记 messageId=1 消息已读
        postOk(String.format(URL_READ_ONE, 1), null, mock_token_org1_user1, null);
        InboxMessage db = inboxMessageRepository.findById(1L).orElse(null);
        Assertions.assertNotNull(db);
        Assertions.assertEquals(db.getReadStatus(), true);

        // user1 标记 messageId=1 消息已读 没有token
        post403(String.format(URL_READ_ONE, 1L), null);

        // user1 标记 user2 的消息已读
        post400(String.format(URL_READ_ONE, 7L), null, mock_token_org1_user1, 20001);
    }

    @Test
    @Sql(statements = INIT_SQL_MESSAGE)
    void readAll() throws Exception {
        // user1 标记全部已读
        postOk(URL_READ_ALL, null, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true)));
        List<InboxMessage> messages = getFromDb(1L);
        Assertions.assertNotNull(messages);
        messages.forEach(i -> {
            Assertions.assertEquals(i.getReadStatus(), true);
        });

        // user1 标记全部已读 没有token
        post403(URL_READ_ALL, null);

    }

    private List<InboxMessage> getFromDb(Long userId) {
        return inboxMessageRepository.findAll()
                .stream()
                .filter(k -> k.getUserId().equals(userId))
                .collect(Collectors.toList());
    }

    private static final String INIT_SQL_MESSAGE = "INSERT INTO `inbox_message` " +
            "(`id`, `org_id`, `user_id`, `type`, `title`, `description`, `target_url`, `from_user_id`, `read_status`, `deleted`, `create_time`, `modify_time`) VALUES " +
            "('1', '1', '1', 0, '平台消息1', '平台消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('2', '1', '1', 0, '平台消息2', '平台消息描述2', '/test', '0', 0, 0, now(), now())," +
            "('3', '1', '1', 1, '租户消息1', '租户消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('4', '1', '1', 1, '租户消息2', '租户消息描述2', '/test', '0', 0, 0, now(), now())," +
            "('5', '1', '1', 2, '用户消息1', '用户消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('6', '1', '1', 2, '用户消息2', '用户消息描述2', '/test', '0', 0, 0, now(), now())," +
            "('7', '2', '2', 0, '平台消息1', '平台消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('8', '2', '2', 0, '平台消息2', '平台消息描述2', '/test', '0', 0, 0, now(), now())," +
            "('9', '2', '2', 1, '租户消息1', '租户消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('10','2', '2', 1, '租户消息2', '租户消息描述2', '/test', '0', 0, 0, now(), now())," +
            "('11','2', '2', 2, '用户消息1', '用户消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('12','2', '2', 2, '用户消息1', '用户消息描述1', '/test', '0', 0, 0, now(), now())," +
            "('13','2', '2', 2, '用户消息2', '用户消息描述2', '/test', '0', 0, 0, now(), now());";
}