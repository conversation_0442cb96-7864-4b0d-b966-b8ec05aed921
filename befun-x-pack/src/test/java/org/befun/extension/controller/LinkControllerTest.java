package org.befun.extension.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.befun.core.exception.BadRequestException;
import org.befun.core.generator.SysConvert;
import org.befun.extension.TestRedisConfiguration;
import org.befun.extension.dto.CreateLinkDto;
import org.befun.extension.dto.CreateLinkResponseDto;
import org.befun.extension.entity.Link;
import org.befun.extension.property.LinkProperty;
import org.befun.extension.repository.LinkRepository;
import org.befun.extension.service.LinkService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class LinkControllerTest {

    @Autowired
    protected MockMvc mvc;
    @Autowired
    protected ObjectMapper objectMapper;
    @Autowired
    private LinkRepository linkRepository;
    @Autowired
    LinkService linkService;
    @Autowired
    LinkProperty linkProperty;

    private static final String URL = "/";


    @Test
    void testCreate() throws Exception {
        CreateLinkDto createLinkDto = new CreateLinkDto();
        String json = objectMapper.writeValueAsString(createLinkDto);
        mvc.perform(MockMvcRequestBuilders.post("/t")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json).accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content()
                        .contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    void testAcces() throws Exception {
        String id1 = "abchhh1244nnn";
        String id2 = "1234455";
        mvc.perform(MockMvcRequestBuilders.get("/{id}", id2))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    void testURLEncode() {
        String url = "http://192.168.1.1:8080/resources/测试测试?id=中文abc.xls";
        //对url中文解码，放入Location
        //正则表达式匹配参数中的中文
        Pattern pattern = Pattern.compile("([\u4e00-\u9fa5]+)");
        Matcher matcher = pattern.matcher(url);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {  //匹配有中文，进行编码并替换
            String resultStr = null;
            try {
                resultStr = URLEncoder.encode(matcher.group(1), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            matcher.appendReplacement(sb, resultStr);
        }
        matcher.appendTail(sb);
        String resultUrl = sb.toString();
        System.out.println("resultUrl:" + resultUrl);

        String encodeUrl = null;
            encodeUrl = linkService.encodeUrl(resultUrl);

        System.out.println("encodeUrl:" + encodeUrl);
        HttpServletResponse httpServletResponse;
//        httpServletResponse.setHeader();

    }









    @Test
    void testGenerateShortUrl() throws UnsupportedEncodingException {
        String url1 = "http://192.168.1.1:8080/resources/测试测试?id=6767600066676677670000007644.xls";
        String url2 = "http://192.168.1.1:8080/resources/测试测试?id=中文abc1234.xls";
        CreateLinkResponseDto response = new CreateLinkResponseDto();
        Optional<Link> link = linkRepository.findFirstByUrl(url2);
        if (!link.isPresent()) {
            Link newlink = new Link();
            newlink.setUrl(url2);
            linkRepository.save(newlink);
            link = Optional.ofNullable(newlink);
        }
        String resultId = SysConvert.toX(link.get().getId());//将主键id转化为自定义字符串id
        if (linkProperty.getRoot() != null) {
            response.setId(resultId);
            response.setUrl(linkProperty.getRoot() + "/" + resultId);
        } else throw new BadRequestException("root配置信息为空");
        System.out.println(linkProperty.getRoot() + "/" + resultId);
        System.out.println("resultId:" + resultId);


    }


}
