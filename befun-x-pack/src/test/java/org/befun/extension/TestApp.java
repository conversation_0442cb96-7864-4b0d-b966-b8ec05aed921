package org.befun.extension;

import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication(scanBasePackages = {"cn.hanyi", "org.befun.core", "org.befun.nlp.core"})
public class TestApp {

    public static void main(String[] args) {
        SpringApplication.run(TestApp.class, args);
    }

    @Configuration
    @EntityScan({
            "cn.hanyi.ctm.entity",
            "org.befun.core.entity",
            XPackAutoConfiguration.PACKAGE_ENTITY,
    })
    @EnableJpaRepositories(basePackages = {
            "cn.hanyi.ctm.repository",
            "org.befun.core.repo",
            XPackAutoConfiguration.PACKAGE_REPOSITORY,
    }, repositoryBaseClass = BaseRepositoryImpl.class)
    public class BaseJPAConfig {
    }
}
