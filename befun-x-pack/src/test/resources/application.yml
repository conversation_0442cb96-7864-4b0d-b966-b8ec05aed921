
spring:
  datasource:
    driverClassName: org.h2.Driver
    url: jdbc:h2:mem:myDb1;DB_CLOSE_DELAY=-1
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create
      generate-ddl: true
  redis:
    host: localhost
    port: 6378

logging:
  level:
    root: ${LOG_LEVEL:info}
    org:
      hibernate:
        SQL: info
        type: trace


befun:
  server:
    enable-open-api-filter: true
  extension:
    shorturl:
      root: ${ROOT_URL:http://localhost:8080}
    smart-verify:
      enable-smart-verify: true
      regionId: cn-hangzhou
      accessKeyId: LTAI4G5RfyxPtajMojKJPvmM
      accessKeySecret: ******************************
      product: afs
      domain: afs.aliyuncs.com
      appKey: FFFF0N00000000008B36
hanyi:
  common:
    ip-resolver:
      default-platform: local
      local:
        algorithm: memory
    file-storage:
      default-platform: default
      local:
        - platform: default # 存储平台标识
          enable-storage: true
          enable-access: true
          domain: "http://localhost:8080/tmp/files/"
          base-path: /tmp/files/
          path-patterns: /tmp/files/**
