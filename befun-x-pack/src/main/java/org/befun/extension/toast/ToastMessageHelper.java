package org.befun.extension.toast;

import org.befun.core.constant.IErrorInternalCode;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.extension.Extensions;
import org.befun.extension.property.ToastProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

@Component
@ConditionalOnProperty(name = Extensions.TOAST_MESSAGE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.TOAST_MESSAGE_MATCH_IF_MISSING)
public class ToastMessageHelper {

    private final ToastProperty property;
    private static ToastMessageHelper self;

    public ToastMessageHelper(ToastProperty property) {
        this.property = property;
        self = this;
    }

    public static BadRequestException badRequestException(ToastMessage message) {
        return new BadRequestException(message.getInternalCode(), getMessage(message));
    }

    public static BadRequestException badRequestException(IErrorInternalCode internalCode, ToastMessage message) {
        return new BadRequestException(internalCode.getValue(), getMessage(message));
    }

    public static BadRequestException badRequestException(ToastMessage message, Object... args) {
        return new BadRequestException(message.getInternalCode(), getMessage(message, args));
    }

    public static BadRequestException badRequestException(IErrorInternalCode internalCode, ToastMessage message, Object... args) {
        return new BadRequestException(internalCode.getValue(), getMessage(message, args));
    }

    public static BusinessException businessException(ToastMessage message) {
        return new BusinessException(getMessage(message), message.getInternalCode());
    }

    public static BusinessException businessException(ToastMessage message, Object data) {
        return new BusinessException(getMessage(message), message.getInternalCode(), data);
    }

    public static BusinessException businessException(ToastMessage message, Object data, Object... args) {
        return new BusinessException(getMessage(message, args), message.getInternalCode(), data);
    }

    private static String getMessage(ToastMessage message, Object... args) {
        Assert.notNull(self, "befun.extension.toast-message.enable 需要配置为true");
        String value = self.property.getMessages().getOrDefault(message.getKey(), message.getDefaultMessage());
        if (args != null && args.length > 0) {
            return String.format(value, args);
        } else {
            return value;
        }
    }


    private static String getMessage(ToastMessage message) {
        Assert.notNull(self, "befun.extension.toast-message.enable 需要配置为true");
        return self.property.getMessages().getOrDefault(message.getKey(), message.getDefaultMessage());
    }
}
