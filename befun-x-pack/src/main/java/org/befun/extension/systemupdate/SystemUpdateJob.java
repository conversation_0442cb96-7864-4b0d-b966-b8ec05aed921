package org.befun.extension.systemupdate;

import org.springframework.core.Ordered;

public interface SystemUpdateJob extends Ordered {

    SystemVersion systemVersion();

    default String jobName() {
        return getClass().getSimpleName();
    }

    default String jobKey() {
        return String.format("%s-%s", systemVersion().version(), jobName());
    }

    @Override
    default int getOrder() {
        return systemVersion().ordinal();
    }
}
