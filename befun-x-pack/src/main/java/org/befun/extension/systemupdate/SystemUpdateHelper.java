package org.befun.extension.systemupdate;


import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
public class SystemUpdateHelper {

    private static final String SYSTEM_UPDATE_ORG = "system-update:org:%d";     // orgId
    private static final String SYSTEM_UPDATE_USER = "system-update:user:%d";   // userId

    @Autowired(required = false)
    private List<SystemUpdateOrgJob> allOrgJobs;
    @Autowired(required = false)
    private List<SystemUpdateUserJob> allUserJobs;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public void userLogin(Long orgId, Long userId) {
        if (orgId == null || userId == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(allOrgJobs)) {
            update(String.format(SYSTEM_UPDATE_ORG, orgId), allOrgJobs, job -> job.triggerOrgJob(orgId));
        }
        if (CollectionUtils.isNotEmpty(allUserJobs)) {
            update(String.format(SYSTEM_UPDATE_USER, userId), allUserJobs, job -> job.triggerUserJob(orgId, userId));
        }
    }

    private <JOB extends SystemUpdateJob> void update(String key, List<JOB> jobs, Consumer<JOB> triggerJob) {
        Set<String> allJobKeys = jobs.stream().map(SystemUpdateJob::jobKey).collect(Collectors.toSet());
        Set<String> notExistsJobKeys = compareNotExists(key, allJobKeys);
        if (CollectionUtils.isNotEmpty(notExistsJobKeys)) {
            jobs.forEach(job -> {
                String jobKey = job.jobKey();
                if (notExistsJobKeys.contains(jobKey)) {
                    if (lockJob(key, jobKey)) {
                        triggerJob.accept(job);
                    }
                }
            });
        }
    }

    private HashOperations<String, String, String> hashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    private Set<String> compareNotExists(String key, Set<String> allJobKeys) {
        Set<String> notExistsJobKeys = new HashSet<>();
        Set<String> existsJobKeys = hashOpt().keys(key);
        if (CollectionUtils.isEmpty(existsJobKeys)) {
            notExistsJobKeys.addAll(allJobKeys);
        }
        allJobKeys.forEach(jobKey -> {
            if (!existsJobKeys.contains(jobKey)) {
                notExistsJobKeys.add(jobKey);
            }
        });
        return notExistsJobKeys;
    }

    @SuppressWarnings("ConstantConditions")
    private boolean lockJob(String key, String jobKey) {
        return Optional.ofNullable(hashOpt().putIfAbsent(key, jobKey, LocalDateTime.now().toString())).orElse(false);
    }

}
