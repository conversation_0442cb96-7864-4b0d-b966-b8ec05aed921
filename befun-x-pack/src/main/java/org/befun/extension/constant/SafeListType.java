package org.befun.extension.constant;

import org.jsoup.safety.Safelist;

public enum SafeListType {
    none {
        @Override
        public Safelist get() {
            return Safelist.none();
        }
    },
    simpleText {
        @Override
        public Safelist get() {
            return Safelist.simpleText();
        }
    },
    basic {
        @Override
        public Safelist get() {
            return Safelist.basic();
        }
    },
    basicWithImages {
        @Override
        public Safelist get() {
            return Safelist.basicWithImages();
        }
    },
    relaxed {
        @Override
        public Safelist get() {
            return Safelist.relaxed();
        }
    },
    ;

    public Safelist get() {
        return null;
    }

    ;
}
