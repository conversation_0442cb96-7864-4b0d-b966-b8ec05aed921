package org.befun.extension.constant;

import lombok.Getter;

@Getter
public enum InboxMessageType {
    COOPERATION,
    WARNING,
    ACTION,
    JOURNEY,
    VERIFY,
    ADMINX_CHANNEL_REJECT, // 社区订单拒绝
    ADMINX_CHANNEL_QUOTED, // 社区订单报价
    ADMINX_CHANNEL_REFUND, // 社区订单退款
    SURVEY_CONTENT_AUDIT, // 问卷内容审核
    ADMINX_CHANNEL_START, // 社区订单开始
    ADMINX_CHANNEL_END, // 社区订单结束
    SURVEY_CHANGE_OWNER, // 问卷转移拥有人
    ;
}
