package org.befun.extension.constant;

import lombok.Getter;
import org.befun.core.utils.DateHelper;

import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Getter
public enum AccessLimitCacheType {
    SECOND("yyyy-MM-dd-HH-mm-ss"),
    MINUTE("yyyy-MM-dd-HH-mm"),
    HOUR("yyyy-MM-dd-HH"),
    DAY("yyyy-MM-dd"),
    ;

    private final String format;

    AccessLimitCacheType(String format) {
        this.format = format;
    }

    public String getFormat() {
       return DateHelper.format(new Date(), DateTimeFormatter.ofPattern(format));
    }

    public Duration getDuration() {
        return switch (this) {
            case SECOND -> Duration.ofSeconds(1);
            case MINUTE -> Duration.ofMinutes(1);
            case HOUR -> Duration.ofHours(1);
            case DAY -> Duration.ofDays(1);
            default -> Duration.ofMinutes(1);
        };
    }
}
