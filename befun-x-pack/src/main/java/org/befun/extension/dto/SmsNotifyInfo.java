package org.befun.extension.dto;

import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class SmsNotifyInfo extends SmsNotifyBaseInfo {
    // 短信模板id
    private String templateId;
    // 短信模板名称，cem定义的每个模板的唯一名称
    private String templateName;
    // 短信签名
    private String signature;
    private String realSignature;
    private String mobile;
    // 短信内容部分的参数
    private String content;             // 平台定义的内容 ，变量名已经替换为平台可读的名称
    private String originTemplate;      // 第三方短信平台的短信模版的原始内容
    private String url;
    private Map<String, Object> params = new HashMap<>();
    // 短信参数
    private List<TemplateVariableValue> variableValues;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TemplateVariableValue {
        private String name;
        private String value;
    }

    public Map<String, String> convertVariablesToMap() {
        Map<String, String> map = new HashMap<>();
        if (variableValues != null) {
            variableValues.forEach(i -> {
                map.put(i.name, i.value);
            });
        }
        return map;
    }
}
