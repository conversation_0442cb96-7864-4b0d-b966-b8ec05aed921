package org.befun.extension.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

@Setter
@Getter
public class OperateLogConfigDto extends BaseDTO {
    @Schema(description = "请求方法")
    private String method;
    @Schema(description = "请求路径")
    private String path;
    @Schema(description = "功能模块")
    private String module;
    @Schema(description = "操作内容")
    private String action;
    @Schema(hidden = true)
    private Integer code;
    @Schema(hidden = true)
    private Integer internalCode;
}
