package org.befun.extension.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.constant.SystemUpdateType;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SystemUpdateRequestDto {

    @NotEmpty
    private String version;
    @NotEmpty
    private String secret;
    @NotNull
    private SystemUpdateType type;

    private Map<String, String> allData;

    @Valid
    private List<SystemUpdatePartRequestDto> parts;

}
