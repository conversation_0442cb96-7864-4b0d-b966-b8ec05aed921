package org.befun.extension.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TemplateFileDto extends BaseDTO {

    @Schema(description = "模板类型")
    private String type;
    @Schema(description = "模板文件地址")
    private String fileUrl;
}
