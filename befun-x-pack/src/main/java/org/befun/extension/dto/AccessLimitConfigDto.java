package org.befun.extension.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.extension.constant.AccessLimitCacheType;
import org.befun.extension.constant.AccessLimitType;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AccessLimitConfigDto {
    private String name;
    private AccessLimitType type;
    private Integer limit;
    private AccessLimitCacheType cacheType = AccessLimitCacheType.MINUTE;
}
