package org.befun.extension.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MessageSendResponseInfo extends BaseDTO {
    private boolean success;
    private int realCost;
    private String content;
    private String response;
    private String thirdpartyMessageType;
    private String thirdpartyMessageId;

    public MessageSendResponseInfo(boolean success, int realCost, String content, String response) {
        this.success = success;
        this.realCost = realCost;
        this.content = content;
        this.response = response;
    }
}
