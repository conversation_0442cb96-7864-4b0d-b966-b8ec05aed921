package org.befun.extension.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AlipayQueryRefundOrderResponseDto extends AlipayResponseDto{

    private String payNo;
    private String alipayNo;
    private String refundNo;
    private String refundStatus;
    private Date refundTime;

    public boolean isSuccess(){
        return "REFUND_SUCCESS".equalsIgnoreCase(refundStatus);
    }
}
