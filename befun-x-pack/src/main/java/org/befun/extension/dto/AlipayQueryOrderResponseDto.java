package org.befun.extension.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AlipayQueryOrderResponseDto extends AlipayResponseDto{

    private String payNo;
    private String alipayNo;
    private Date payTime;
    // 交易状态：
    // WAIT_BUYER_PAY（交易创建，等待买家付款）、
    // TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、
    // TRADE_SUCCESS（交易支付成功）、
    // TRADE_FINISHED（交易结束，不可退款
    private String payStatus;

    private boolean notExist;

    public boolean isSuccess() {
        return "TRADE_SUCCESS".equalsIgnoreCase(payStatus);
    }

}
