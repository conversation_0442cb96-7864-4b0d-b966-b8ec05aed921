package org.befun.extension.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class SmsNotifyTextInfo extends SmsNotifyBaseInfo {
    private String mobile;
    private String content;
    private Map<String, Object> params;

    private String realSignature;
    private String originTemplate;

    public SmsNotifyTextInfo(String mobile, String content, Map<String, Object> params) {
        this.mobile = mobile;
        this.content = content;
        this.params = params;
    }

    public SmsNotifyTextInfo(String mobile, String content, Map<String, Object> params, String realSignature, String originTemplate) {
        this.mobile = mobile;
        this.content = content;
        this.params = params;
        this.realSignature = realSignature;
        this.originTemplate = originTemplate;
    }
}
