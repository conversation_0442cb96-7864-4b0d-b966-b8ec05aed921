package org.befun.extension.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class CsvDto<T> {
    /**
     * 文件名
     */
    private String name;
    /**
     * 表头
     */
    private List<String> header = new ArrayList<>();
    /**
     * 表格数据
     */
    private List<T> rows = new ArrayList<>();
    /**
     * 大小 bytes
     */
    private Long size;
}
