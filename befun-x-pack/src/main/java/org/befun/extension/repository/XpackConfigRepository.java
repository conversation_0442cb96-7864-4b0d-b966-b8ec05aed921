package org.befun.extension.repository;


import org.befun.core.repository.ResourceRepository;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.entity.XpackConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XpackConfigRepository extends ResourceRepository<XpackConfig, Long> {

    XpackConfig findFirstByType(XPackAppType type);

    List<XpackConfig> findByType(XPackAppType type);

    XpackConfig findFirstByTypeAndSubType(XPackAppType type, String subType);

    List<XpackConfig> findByTypeAndSubTypeOrSubTypeOrderBySubTypeDesc(XPackAppType type, String subType, String subType2);
}
