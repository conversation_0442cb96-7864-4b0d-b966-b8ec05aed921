package org.befun.extension.repository;


import cn.hanyi.common.file.storage.FileInfo;
import org.befun.core.repository.ResourceRepository;
import org.befun.extension.entity.File;
import org.befun.extension.entity.Link;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FileRepository extends ResourceRepository<File, Long> {
    Optional<File> findOneByUrlOrFilename(String url, String filename);
}
