package org.befun.extension.repository;


import org.befun.extension.entity.Link;
import org.befun.extension.entity.LinkExist;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LinkExistRepository extends CrudRepository<LinkExist, Long> {
    Optional<LinkExist> findFirstByHash(String hash);
}
