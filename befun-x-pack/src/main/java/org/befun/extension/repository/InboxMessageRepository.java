package org.befun.extension.repository;

import org.befun.core.repository.ResourceRepository;
import org.befun.extension.entity.InboxMessage;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Repository
public interface InboxMessageRepository extends ResourceRepository<InboxMessage, Long> {

    List<InboxMessage> findByOrgIdAndUserIdAndReadStatus(Long orgId, Long userId, boolean readStatus);
}