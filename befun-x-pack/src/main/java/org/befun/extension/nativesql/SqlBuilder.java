package org.befun.extension.nativesql;

import java.util.ArrayList;
import java.util.List;

public class SqlBuilder implements SqlSelectSection {
    private String select;
    private String countSelect;
    private final List<String> join = new ArrayList<>();
    private final List<String> where = new ArrayList<>();
    private final List<String> groupBy = new ArrayList<>();
    private final List<String> orderBy = new ArrayList<>();
    private String limit;
    private int page;
    private int size;

    public static SqlBuilder select(String sql, Object... args) {
        return SqlSection.create(new SqlBuilder(), SqlBuilder::setSelect, sql, args).alwaysTrue();
    }

    @Override
    public SqlBuilder getSqlBuilder() {
        return this;
    }

    public String buildCountSql() {
        StringBuilder s = new StringBuilder();
        s.append(countSelect);
        if (!join.isEmpty()) {
            s.append(" ").append(String.join(" ", join));
        }
        if (!where.isEmpty()) {
            s.append(" where ").append(String.join(" and ", where));
        }
        if (!groupBy.isEmpty()) {
            s.append(" group by ").append(String.join(",", groupBy));
        }
        return s.toString();
    }

    public String buildSelectSql() {
        StringBuilder s = new StringBuilder();
        s.append(select);
        if (!join.isEmpty()) {
            s.append(" ").append(String.join(" ", join));
        }
        if (!where.isEmpty()) {
            s.append(" where ").append(String.join(" and ", where));
        }
        if (!groupBy.isEmpty()) {
            s.append(" group by ").append(String.join(",", groupBy));
        }
        if (!orderBy.isEmpty()) {
            s.append(" order by ").append(String.join(",", orderBy));
        }
        if (limit != null) {
            s.append(" ").append(limit);
        }
        return s.toString();
    }

    void setSelect(String select) {
        this.select = select;
    }

    void setCountSelect(String countSelect) {
        this.countSelect = countSelect;
    }

    void addJoin(String joinSql) {
        join.add(joinSql);
    }

    void addWhere(String whereSql) {
        where.add(whereSql);
    }

    void addGroupBy(String groupByColumn) {
        groupBy.add(groupByColumn);
    }

    void addOrderBy(String orderByColumn) {
        orderBy.add(orderByColumn);
    }

    void setLimit(String limit) {
        this.limit = limit;
    }

    void setPage(int page) {
        this.page = page;
    }

    void setSize(int size) {
        this.size = size;
    }

    public int getPage() {
        return page;
    }

    public int getSize() {
        return size;
    }
}