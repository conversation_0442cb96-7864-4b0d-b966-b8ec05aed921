package org.befun.extension.nativesql;

import org.apache.commons.lang3.StringUtils;

import java.util.function.BiConsumer;
import java.util.function.Supplier;

public class SqlSection {

    private final SqlBuilder sqlBuilder;
    private String format;
    private Supplier<String> formatSupplier;
    private final Object[] args;
    private final BiConsumer<SqlBuilder, String> setSqlBuilder;

    private SqlSection(SqlBuilder sqlBuilder, BiConsumer<SqlBuilder, String> setSqlBuilder, String format, Object[] args) {
        this.sqlBuilder = sqlBuilder;
        this.setSqlBuilder = setSqlBuilder;
        this.format = format;
        this.args = args;
    }

    private SqlSection(SqlBuilder sqlBuilder, BiConsumer<SqlBuilder, String> setSqlBuilder, Supplier<String> formatSupplier, Object[] args) {
        this.sqlBuilder = sqlBuilder;
        this.setSqlBuilder = setSqlBuilder;
        this.formatSupplier = formatSupplier;
        this.args = args;
    }

    public static SqlSection create(SqlBuilder sqlBuilder, BiConsumer<SqlBuilder, String> setSqlBuilder, String format, Object[] args) {
        return new SqlSection(sqlBuilder, setSqlBuilder, format, args);
    }

    public static SqlSection create(SqlBuilder sqlBuilder, BiConsumer<SqlBuilder, String> setSqlBuilder, Supplier<String> formatSupplier, Object[] args) {
        return new SqlSection(sqlBuilder, setSqlBuilder, formatSupplier, args);
    }

//    @Override
//    public SqlBuilder getSqlBuilder() {
//        return sqlBuilder;
//    }

    public SqlBuilder ifTrue(boolean test) {
        if (test) {
            String sql = getSql();
            if (StringUtils.isNotEmpty(sql)) {
                setSqlBuilder.accept(sqlBuilder, getSql());
            }
        }
        return sqlBuilder;
    }

    public SqlBuilder ifTrue(Supplier<Boolean> test) {
        return ifTrue(test.get());
    }

    public SqlBuilder alwaysTrue() {
        return ifTrue(true);
    }

    private String getSql() {
        if (format == null) {
            if (formatSupplier != null) {
                format = formatSupplier.get();
            }
        }
        if (format == null) {
            throw new RuntimeException("sql format 不能为null");
        }
        if (args == null || args.length == 0) {
            return format;
        }
        return String.format(format, args);
    }

}
