package org.befun.extension.nativesql;

import java.util.function.Consumer;
import java.util.function.Supplier;

public interface SqlWhereSection extends SqlAppender {

    default SqlSection where(String sql, Object... args) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addWhere, sql, args);
    }

    default SqlSection where(Supplier<String> where) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addWhere, where, null);
    }

    default SqlSection where(Consumer<SqlOrSection> or) {
        return where(() -> {
            SqlOrSection sqlOrSection = SqlOrSection.create();
            or.accept(sqlOrSection);
            return sqlOrSection.getSql();
        });
    }

}
