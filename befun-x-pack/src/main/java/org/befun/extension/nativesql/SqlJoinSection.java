package org.befun.extension.nativesql;

import java.util.function.Supplier;

public interface SqlJoinSection extends SqlAppender {


    default SqlSection join(String sql, Object... args) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addJoin, sql, args);
    }

    default SqlSection join(Supplier<String> join) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addJoin, join, null);
    }

}
