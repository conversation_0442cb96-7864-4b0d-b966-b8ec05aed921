package org.befun.extension.nativesql;

public interface SqlSelectSection extends SqlJoinSection, SqlWhereSection {

    default SqlBuilder countSelect(String sql, Object... args) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::setCountSelect, sql, args).alwaysTrue();
    }

    default SqlBuilder groupBy(String column) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addGroupBy, column, null).alwaysTrue();
    }

    default SqlBuilder orderByDesc(String column) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addOrderBy, "%s desc", new Object[]{column}).alwaysTrue();
    }

    default SqlBuilder orderByAsc(String column) {
        return SqlSection.create(getSqlBuilder(), SqlBuilder::addOrderBy, "%s asc", new Object[]{column}).alwaysTrue();
    }

    default SqlBuilder limit(int page, int size) {
        SqlBuilder sqlBuilder = getSqlBuilder();
        sqlBuilder.setPage(page);
        sqlBuilder.setSize(size);
        return SqlSection.create(sqlBuilder, SqlBuilder::setLimit, "limit %d,%d", new Object[]{(page - 1) * size, size}).alwaysTrue();
    }

}
