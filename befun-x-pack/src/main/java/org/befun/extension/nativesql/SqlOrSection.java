package org.befun.extension.nativesql;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class SqlOrSection {

    private final List<String> orList = new ArrayList<>();

    private SqlOrSection() {
    }

    static SqlOrSection create() {
        return new SqlOrSection();
    }

    String getSql() {
        return orList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(" or ", "(", ")"));
    }

    public SqlOrSection or(String format, Object... args) {
        String or;
        if (args == null || args.length == 0) {
            or = format;
        } else {
            or = String.format(format, args);
        }
        orList.add(or);
        return this;
    }

}
