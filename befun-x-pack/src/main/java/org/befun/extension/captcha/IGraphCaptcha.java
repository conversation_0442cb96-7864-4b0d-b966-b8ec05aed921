package org.befun.extension.captcha;

import org.befun.extension.constant.GraphCaptchaType;
import org.befun.extension.dto.GraphCaptchaResponseDto;
import org.befun.extension.dto.GraphCaptchaVerifyDto;

public interface IGraphCaptcha {

    GraphCaptchaType type();

    GraphCaptchaResponseDto create();

    /**
     * 校验图形验证码
     * 校验成功后，会清除缓存
     */
    boolean verify(GraphCaptchaVerifyDto dto);

    /**
     * 校验图形验证码
     * 只是校验，不清除缓存
     */
    boolean check(GraphCaptchaVerifyDto dto);

    void clear(String key);
}
