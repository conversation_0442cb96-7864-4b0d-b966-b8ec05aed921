package org.befun.extension.captcha;

import org.apache.commons.lang3.StringUtils;
import org.befun.extension.dto.GraphCaptchaVerifyDto;
import org.befun.extension.property.GraphCaptchaProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.UUID;

public abstract class BaseGraphCaptcha implements IGraphCaptcha {

    @Autowired
    protected GraphCaptchaProperty property;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    private String cacheKey(String key) {
        return "graphCaptcha:" + type().name() + ":" + key;
    }

    protected String cacheText(String text) {
        String key = UUID.randomUUID().toString();
        String cacheKey = cacheKey(key);
        stringRedisTemplate.opsForValue().set(cacheKey, text, Duration.ofSeconds(property.getExpireSeconds()));
        return key;
    }

    protected boolean verifyCompare(String cacheValue, String value) {
        return cacheValue.equals(value);
    }

    @Override
    public boolean verify(GraphCaptchaVerifyDto dto) {
        String cacheKey = cacheKey(dto.getKey());
        Object value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (value != null && StringUtils.isNotEmpty(dto.getValue())) {
            if (verifyCompare(value.toString(), dto.getValue())) {
                stringRedisTemplate.delete(cacheKey);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean check(GraphCaptchaVerifyDto dto) {
        String cacheKey = cacheKey(dto.getKey());
        Object value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (value != null && StringUtils.isNotEmpty(dto.getValue())) {
            return verifyCompare(value.toString(), dto.getValue());
        }
        return false;
    }

    @Override
    public void clear(String key) {
        String cacheKey = cacheKey(key);
        stringRedisTemplate.delete(cacheKey);
    }
}
