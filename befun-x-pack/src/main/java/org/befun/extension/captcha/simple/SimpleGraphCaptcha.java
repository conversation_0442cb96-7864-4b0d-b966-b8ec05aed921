package org.befun.extension.captcha.simple;

import org.befun.extension.captcha.BaseGraphCaptcha;
import org.befun.extension.constant.GraphCaptchaType;
import org.befun.extension.dto.GraphCaptchaResponseDto;
import org.springframework.stereotype.Component;


@Component
public class Simple<PERSON>raphCaptcha extends BaseGraphCaptcha {

    @Override
    public GraphCaptchaType type() {
        return GraphCaptchaType.SIMPLE;
    }

    @Override
    public GraphCaptchaResponseDto create() {
        return new SimpleGraphHelper(property.getSimple()).createImage((text, image) -> {
            String key = cacheText(text);
            return new GraphCaptchaResponseDto(type(), key, image);
        });
    }

    @Override
    protected boolean verifyCompare(String cacheValue, String value) {
        if (property.getSimple().isIgnoreCase()) {
            return cacheValue.equalsIgnoreCase(value);
        } else {
            return cacheValue.equals(value);
        }
    }
}
