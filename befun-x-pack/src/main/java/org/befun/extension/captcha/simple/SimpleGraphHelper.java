package org.befun.extension.captcha.simple;

import org.apache.commons.lang3.StringUtils;
import org.befun.extension.property.GraphCaptchaProperty;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import java.util.function.BiFunction;

@SuppressWarnings("unused")
public class SimpleGraphHelper {
    // 默认验证码字符集
    private static final char[] chars = {
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            'A', 'B', 'c', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'k', 'L', 'M', 'N', 'o', 'p', 'Q', 'R', 's', 'T', 'u', 'v', 'w', 'x', 'Y', 'z'};
    // 默认字符数量
    private final Integer size;
    // 默认干扰线数量
    private final int lines;
    // 默认宽度
    private final int width;
    // 默认高度
    private final int height;
    // 默认字体大小
    private final int fontSize;
    // 默认字体倾斜
    private final boolean tilt;

    private final Color backgroundColor;

    private final char[] customChars;

    /**
     * 初始化基础参数
     */
    private SimpleGraphHelper(Builder builder) {
        size = builder.size;
        lines = builder.lines;
        width = builder.width;
        height = builder.height;
        fontSize = builder.fontSize;
        tilt = builder.tilt;
        backgroundColor = builder.backgroundColor;
        if (StringUtils.isNotEmpty(builder.customChars)) {
            customChars = builder.customChars.toCharArray();
        } else {
            customChars = null;
        }
    }

    public SimpleGraphHelper(GraphCaptchaProperty.Simple simple) {
        size = simple.getSize();
        lines = simple.getLines();
        width = simple.getWidth();
        height = simple.getHeight();
        fontSize = simple.getFontSize();
        tilt = simple.isTilt();
        backgroundColor = Color.getColor(simple.getBackgroundColor());
        if (StringUtils.isNotEmpty(simple.getChars())) {
            customChars = simple.getChars().toCharArray();
        } else {
            customChars = null;
        }
    }

    /**
     * 实例化构造器对象
     */
    public static Builder newBuilder() {
        return new Builder();
    }

    /**
     * @return 生成随机验证码及图片
     */
    public <X> X createImage(BiFunction<String, String, X> consumer) {
        StringBuilder sb = new StringBuilder();
        // 创建空白图片
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 获取图片画笔
        Graphics2D graphic = image.createGraphics();
        // 设置抗锯齿
        graphic.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        // 设置画笔颜色
        graphic.setColor(backgroundColor);
        // 绘制矩形背景
        graphic.fillRect(0, 0, width, height);
        // 画随机字符
        Random ran = new Random();

        // 计算每个字符占的宽度，这里预留一个字符的位置用于左右边距
        int codeWidth = width / (size + 1);
        // 字符所处的y轴的坐标
        int y = height * 3 / 4;

        char[] supportChars = customChars != null ? customChars : chars;

        for (int i = 0; i < size; i++) {
            // 设置随机颜色
            graphic.setColor(getRandomColor());
            // 初始化字体
            Font font = new Font(null, Font.BOLD + Font.ITALIC, fontSize);

            if (tilt) {
                // 随机一个倾斜的角度 -45到45度之间
                int theta = ran.nextInt(45);
                // 随机一个倾斜方向 左或者右
                theta = (ran.nextBoolean()) ? theta : -theta;
                AffineTransform affineTransform = new AffineTransform();
                affineTransform.rotate(Math.toRadians(theta), 0, 0);
                font = font.deriveFont(affineTransform);
            }
            // 设置字体大小
            graphic.setFont(font);

            // 计算当前字符绘制的X轴坐标
            int x = (i * codeWidth) + (codeWidth / 2);

            // 取随机字符索引
            int n = ran.nextInt(supportChars.length);
            // 得到字符文本
            String code = String.valueOf(supportChars[n]);
            // 画字符
            graphic.drawString(code, x, y);

            // 记录字符
            sb.append(code);
        }
        // 画干扰线
        for (int i = 0; i < lines; i++) {
            // 设置随机颜色
            graphic.setColor(getRandomColor());
            // 随机画线
            graphic.drawLine(ran.nextInt(width), ran.nextInt(height), ran.nextInt(width), ran.nextInt(height));
        }
        // 返回验证码和图片
        return consumer.apply(sb.toString(), imageBase64(image));
    }

    private String imageBase64(BufferedImage image) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        String imageBase64;
        try {
            ImageIO.write(image, "png", bos);
            imageBase64 = Base64.getEncoder().encodeToString(bos.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return imageBase64;
    }

    /**
     * 随机取色
     */
    private Color getRandomColor() {
        Random ran = new Random();
        return new Color(ran.nextInt(256), ran.nextInt(256), ran.nextInt(256));
    }

    /**
     * 构造器对象
     */
    public static class Builder {
        // 默认字符数量
        private int size = 4;
        // 默认干扰线数量
        private int lines = 10;
        // 默认宽度
        private int width = 80;
        // 默认高度
        private int height = 35;
        // 默认字体大小
        private int fontSize = 25;
        // 默认字体倾斜
        private boolean tilt = true;
        //背景颜色
        private Color backgroundColor = Color.LIGHT_GRAY;

        private String customChars;

        public Builder setSize(int size) {
            this.size = size;
            return this;
        }

        public Builder setLines(int lines) {
            this.lines = lines;
            return this;
        }

        public Builder setWidth(int width) {
            this.width = width;
            return this;
        }

        public Builder setHeight(int height) {
            this.height = height;
            return this;
        }

        public Builder setFontSize(int fontSize) {
            this.fontSize = fontSize;
            return this;
        }

        public Builder setTilt(boolean tilt) {
            this.tilt = tilt;
            return this;
        }

        public Builder setBackgroundColor(Color backgroundColor) {
            this.backgroundColor = backgroundColor;
            return this;
        }

        public Builder setCustomChars(String customChars) {
            this.customChars = customChars;
            return this;
        }

        public SimpleGraphHelper build() {
            return new SimpleGraphHelper(this);
        }
    }
}
