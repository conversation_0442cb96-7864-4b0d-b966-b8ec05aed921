package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.context.TenantData;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.security.OpenApiTokenFilter;
import org.befun.extension.Extensions;
import org.springframework.boot.autoconfigure.condition.ConditionOutcome;
import org.springframework.boot.autoconfigure.condition.SpringBootCondition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.context.annotation.Conditional;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Order(XPackFilter.ORDER_REQUEST_INFO)
@Component
@ControllerAdvice
@Conditional(RequestInfoFilter.HttpLogCondition.class)
public class RequestInfoFilter implements ResponseBodyAdvice<Object>, WebInternalFilter {

    public static class HttpLogCondition extends SpringBootCondition {

        @Override
        public ConditionOutcome getMatchOutcome(ConditionContext context, AnnotatedTypeMetadata metadata) {
            boolean enable = Optional.of(context.getEnvironment()).map(env ->
                    match(env, Extensions.HTTP_LOG_ENABLE_KEY, "true", Extensions.HTTP_LOG_MATCH_IF_MISSING)
                            || match(env, Extensions.OPERATE_LOG_ENABLE_KEY, "true", Extensions.OPERATE_LOG_MATCH_IF_MISSING)
            ).orElse(false);
            return enable ? ConditionOutcome.match() : ConditionOutcome.noMatch("未开启日志");
        }

        private boolean match(Environment env, String key, String value, boolean matchIfMissing) {
            String v = env.getProperty(key);
            return StringUtils.isEmpty(v) ? matchIfMissing : v.equals(value);
        }
    }


    @Override
    public Boolean preFilter(XPackFilterContext context) {
        try {
            parseParam(context);
        } catch (Throwable e) {
            log.error("解析请求参数失败，忽略错误{}", e.getMessage());
        }
        return true;
    }

    private void parseParam(XPackFilterContext context) {
        HttpServletRequest request = context.getRequest();

        var httpData = RequestInfoContext.createAndCache();
        httpData.setMethod(request.getMethod());
        httpData.setUrl(request.getRequestURI());
        String token = (request).getHeader(LegacyAuthTokenFilter.AUTHORIZATION) == null ? (request).getHeader(OpenApiTokenFilter.HEADER) : (request).getHeader(LegacyAuthTokenFilter.AUTHORIZATION);
        Optional.ofNullable(token).ifPresent(httpData::setToken);
        Optional.ofNullable(request.getParameterMap()).ifPresent(m -> {
            List<String> ps = new ArrayList<>();
            m.forEach((k, v) -> {
                if (k != null && v != null && v.length > 0) {
                    ps.add(k + "=" + v[0]);
                }
            });
            if (!ps.isEmpty()) {
                httpData.setRequestParams(String.join("&", ps));
            }
        });
        context.applyMatchRequestContentType(i -> {
            httpData.setRequestBody(context.getWrapperRequest().getData());
        });
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        RequestInfoContext.Data logData = RequestInfoContext.get();
        if (logData != null) {
            logData.setOriginResponse(body);
            logData.setTenantData(TenantData.currentOrCopied());
        }
        return body;
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

}
