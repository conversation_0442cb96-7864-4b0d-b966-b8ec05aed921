package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.context.TenantData;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.Extensions;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.AccessLimitConfigDto;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.filter.limiter.LimitFilter;
import org.befun.extension.service.XpackConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Order(XPackFilter.ORDER_ACCESS_LIMIT)
@Component("xPackAccessLimitFilter")
@ConditionalOnProperty(name = Extensions.ACCESS_LIMIT_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.ACCESS_LIMIT_MATCH_IF_MISSING)
public class AccessLimitFilter implements WebInternalFilter {


    public static final String CACHE_KEY_PREFIX = "cache:xpack-config:access-limit";

    @Autowired
    private XpackConfigService xpackConfigService;

    @Autowired
    public StringRedisTemplate stringRedisTemplate;


    @Autowired(required = false)
    private List<LimitFilter> limiters = new ArrayList<>();

    @Override
    public Boolean preFilter(XPackFilterContext context){
        try {
            TenantData tenantData;
            if((tenantData = TenantData.current()) != null){
                List<AccessLimitConfigDto> configs = getConfig(tenantData.getOrgId());
                if (configs == null) {
                    return true;
                }
                for(LimitFilter limiter : limiters){
                    AccessLimitConfigDto config = configs.stream().filter(conf -> conf.getType().equals(limiter.type())).findFirst().orElse(null);
                    if(config !=null && !limiter.skip(config)){
                        log.info("start AccessLimitFilter:{} config:{}", limiter.getClass().getName(), JsonHelper.toJson(config));
                        if (!limiter.check(limiter.buildLimitKey(context, config), config.getLimit(), config.getCacheType().getDuration())) {
                            context.getResponse().sendError(HttpServletResponse.SC_BAD_REQUEST, "请求过于频繁，请稍后再试。或联系CEM销售人员升级版本");
                            return false;
                        } else return true;
                    }
                }
            };

            return true;
        } catch (Exception e) {
            log.error("AccessLimitFilter error:{}", e.getMessage());
            return false;
        }
    }


    private List<AccessLimitConfigDto> getConfig(Long orgId) {
        String cacheKey = CACHE_KEY_PREFIX + ":" + orgId;
        String config = stringRedisTemplate.opsForValue().get(cacheKey);

        if (config != null) {
            log.info("get config from redis:{}", config);
            return JsonHelper.toList(config, AccessLimitConfigDto.class);
        } else {
            List<XpackConfig> xpackConfigs = xpackConfigService.getConfigsByTypeAndSubTypeOrSubType(XPackAppType.ACCESS_LIMIT, orgId.toString(), "0");
            if (xpackConfigs != null) {
                log.info("get config from db:{}", JsonHelper.toJson(xpackConfigs));
                List<AccessLimitConfigDto> accessLimitConfigDto = xpackConfigs.stream().map(x -> JsonHelper.toObject(x.getConfig(), AccessLimitConfigDto.class)).collect(Collectors.toList());
                stringRedisTemplate.opsForValue().set(cacheKey, JsonHelper.toJson(accessLimitConfigDto), Duration.ofDays(1));
                return accessLimitConfigDto;
            }
        }
        return null;
    }

}
