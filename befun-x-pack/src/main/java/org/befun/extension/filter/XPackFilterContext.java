package org.befun.extension.filter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.befun.extension.property.XPackFilterProperty;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;


public class XPackFilterContext {

    private final HttpServletRequest request;
    private final HttpServletResponse response;

    private Throwable throwable;

    private HttpServletRequestWrapper wrapperRequest;
    private HttpServletResponseWrapper wrapperResponse;
    private String upperCaseMethod;
    private String replacedPath;
    private String fullUrl;  // path+queryString

    public XPackFilterContext(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
        try {
            this.wrapperResponse = new HttpServletResponseWrapper(response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void setThrowable(Throwable throwable) {
        this.throwable = throwable;
    }

    public Throwable getThrowable() {
        return throwable;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public HttpServletRequest getWrapperRequestOrDefault() {
        return wrapperRequest == null ? request : wrapperRequest;
    }

    public HttpServletRequestWrapper getWrapperRequest() {
        if (wrapperRequest == null) {
            wrapperRequest = new HttpServletRequestWrapper(request);
        }
        return wrapperRequest;
    }

    public HttpServletResponseWrapper getWrapperResponse() {
        return wrapperResponse;
    }

    public String getUpperCaseMethod() {
        if (upperCaseMethod == null) {
            upperCaseMethod = request.getMethod().toUpperCase();
        }
        return upperCaseMethod;
    }

    public String getReplacedPath(List<XPackFilterProperty.PathReplaceRule> replaceRules) {
        if (replacedPath == null) {
            replacedPath = request.getRequestURI();
            if (CollectionUtils.isNotEmpty(replaceRules)) {
                for (XPackFilterProperty.PathReplaceRule rule : replaceRules) {
                    replacedPath = replacedPath.replaceAll(rule.getReg(), rule.getReplacement());
                }
            }
        }
        return replacedPath;
    }

//    public static void main(String[] args) {
//        String s = "/api/auth/login/0111/1234/callback/cas-abcedf1234/cem";
//        List<XPackFilterProperty.PathReplaceRule> replaceRules =  new XPackFilterProperty().getPathReplaceRules();
//        for (XPackFilterProperty.PathReplaceRule rule : replaceRules) {
//            s = s.replaceAll(rule.getReg(), rule.getReplacement());
//        }
//        System.out.println(s);
//    }

    public String getFullUrl() {
        if (fullUrl == null) {
            StringBuilder sb = new StringBuilder(request.getRequestURI());
            if (StringUtils.isNotEmpty(request.getQueryString())) {
                sb.append("?").append(request.getQueryString());
            }
            fullUrl = sb.toString();
        }
        return fullUrl;
    }

    public boolean applyMatchRequestContentType(Consumer<Boolean> apply) {
        ContentType contentType = Optional.ofNullable(request.getContentType()).map(ContentType::parse).orElse(null);
        return applyMatchContentType(contentType, apply);
    }

    public boolean applyMatchResponseContentType(Consumer<Boolean> apply) {
        if (response.getStatus() != 200) {
            return true;
        }
        ContentType contentType = Optional.ofNullable(response.getContentType()).map(ContentType::parse).orElse(null);
        return applyMatchContentType(contentType, apply);
    }

    public boolean applyMatchContentType(ContentType contentType, Consumer<Boolean> apply) {
        // 只解析数据类型为 json | text | xml 的 body
        String mimeType;
        if (contentType != null
                && (StringUtils.isNotEmpty(mimeType = contentType.getMimeType()))
                && (mimeType.equalsIgnoreCase(ContentType.APPLICATION_JSON.getMimeType())
                || mimeType.equalsIgnoreCase(ContentType.TEXT_PLAIN.getMimeType())
                || mimeType.equalsIgnoreCase(ContentType.APPLICATION_XML.getMimeType())
                || mimeType.equalsIgnoreCase(ContentType.TEXT_XML.getMimeType())
        )) {
            Optional.ofNullable(apply).ifPresent(i -> i.accept(true));
            return true;
        }
        return false;
    }
}
