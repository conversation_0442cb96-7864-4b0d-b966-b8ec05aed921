package org.befun.extension.filter;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.befun.extension.Extensions;
import org.befun.extension.property.XPackFilterProperty;
import org.befun.extension.property.XssProperty;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@Order(XPackFilter.ORDER_XSS)
@Component("xPackXSSFilter")
@ConditionalOnProperty(name = Extensions.XSS_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.XSS_MATCH_IF_MISSING)
public class XSSFilter implements WebInternalFilter {

    @Autowired
    private XssProperty xssProperty;
    @Autowired
    private XPackFilterProperty xPackFilterProperty;

    @Override
    public Boolean preFilter(XPackFilterContext context) throws IOException {
        if (isSkip(context)) {
            return true;
        }
        if (validate(context.getWrapperRequest())) {
            return true;
        } else {
            context.getResponse().sendError(HttpServletResponse.SC_BAD_REQUEST, "输入的内容含非法数据，请按现有的富文本编辑器使用");
            return false;
        }
    }

    private Boolean isSkip(XPackFilterContext context) {

        if (!List.of("POST", "PUT").contains(context.getUpperCaseMethod())) {
            return true;
        }

        String uri = context.getReplacedPath(xPackFilterProperty.getPathReplaceRules());

        if (xssProperty.getWhiteList().contains(uri)) {
            return true;
        }

        if (xssProperty.getBlackList().contains(uri)) {
            return false;
        }
        return true;
    }

    private Boolean validate(HttpServletRequestWrapper requestWrapper) {

        String data = StringEscapeUtils.unescapeJava(StringEscapeUtils.unescapeEcmaScript(StringEscapeUtils.unescapeHtml4(requestWrapper.getData())));
        if (StringUtils.isNotEmpty(data)) {
            var cleanData = StringEscapeUtils.unescapeHtml4(StringEscapeUtils.unescapeEcmaScript(Jsoup.clean(data, getSafeList())));
            var cleanLength = replaceSpecialChar(cleanData).length();
            var dataLength = replaceSpecialChar(data).length();
            // 有些自闭合的标签 jsoup会处理<img />
            // 通过精确值来做控制
            if (Math.abs(dataLength - cleanLength) > 5) {
                return false;
            }
        }
        return true;
    }

    private String replaceSpecialChar(String data) {
        return data
                .replace(" ", "")
                .replace("\n", "")
                .replace("/", "")
                ;
    }

    private Safelist getSafeList() {
        Safelist safelist = xssProperty.getType().get();
        if (CollectionUtil.isNotEmpty(xssProperty.getExtraTags())) {
            xssProperty.getExtraTags().forEach((tag, attrMap/*Map<String, List<String>>*/) -> {
                safelist.addTags(tag);
                if (MapUtils.isNotEmpty(attrMap)) {
                    attrMap.forEach((attr, protocols/*List<String>*/) -> {
                        safelist.addAttributes(tag, attr);
                        if (CollectionUtils.isNotEmpty(protocols)) {
                            safelist.addProtocols(tag, attr, protocols.toArray(new String[0]));
                        }
                    });
                }
            });
        }
        return safelist;
    }
}
