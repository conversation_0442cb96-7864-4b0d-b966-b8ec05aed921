package org.befun.extension.filter.limiter;

import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.security.OpenApiTokenFilter;
import org.befun.extension.Extensions;
import org.befun.extension.constant.AccessLimitType;
import org.befun.extension.dto.AccessLimitConfigDto;
import org.befun.extension.filter.XPackFilterContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Component
@Order(BaseLimitFilter.ORDER_AUTHORIZATION)
@ConditionalOnProperty(name = Extensions.ACCESS_LIMIT_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.ACCESS_LIMIT_MATCH_IF_MISSING)
public class AuthorizationLimiter extends BaseLimitFilter implements LimitFilter{

    @Override
    public AccessLimitType type() {
        return AccessLimitType.AUTHORIZATION;
    }

    @Override
    public String buildLimitKey(XPackFilterContext context, AccessLimitConfigDto config) {
        HttpServletRequest req = context.getRequest();
        String token = req.getHeader(LegacyAuthTokenFilter.AUTHORIZATION) == null
                ? req.getHeader(OpenApiTokenFilter.HEADER)
                : req.getHeader(LegacyAuthTokenFilter.AUTHORIZATION);
        return buildPrefixKey(config.getCacheType()) + ":" + token;
    }
}
