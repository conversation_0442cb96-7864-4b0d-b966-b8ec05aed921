package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.charset.StandardCharsets;

@Slf4j
public class HttpServletRequestWrapper extends javax.servlet.http.HttpServletRequestWrapper {

    private byte[] bytes;
    private String data;
    private int position;

    public HttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        loadData(request);
    }

    private void loadData(HttpServletRequest request) {
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            var is = request.getInputStream();
            is.transferTo(os);
            bytes = os.toByteArray();
            if (bytes.length > 0) {
                data = os.toString(StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public String getData() {
        return data;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return bytes == null || position >= bytes.length;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener listener) {

            }

            @Override
            public int read() throws IOException {
                if (isFinished()) {
                    return -1;
                }
                return bytes[position++];
            }
        };
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bytes)));
    }
}
