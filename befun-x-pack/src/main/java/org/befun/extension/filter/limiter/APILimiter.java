package org.befun.extension.filter.limiter;

import org.befun.extension.Extensions;
import org.befun.extension.constant.AccessLimitType;
import org.befun.extension.dto.AccessLimitConfigDto;
import org.befun.extension.filter.XPackFilterContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Order(BaseLimitFilter.ORDER_API)
@ConditionalOnProperty(name = Extensions.ACCESS_LIMIT_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.ACCESS_LIMIT_MATCH_IF_MISSING)
public class APILimiter extends BaseLimitFilter implements LimitFilter{


    private static final Pattern pattern = Pattern.compile("/(\\d+)/?");

    @Override
    public AccessLimitType type() {
        return AccessLimitType.API;
    }

    @Override
    public String buildLimitKey(XPackFilterContext context, AccessLimitConfigDto config) {
        HttpServletRequest req = context.getRequest();
        String path = req.getRequestURI();
        Matcher matcher = pattern.matcher(path);
        String newPath = matcher.replaceAll("ID");
        return buildPrefixKey(config.getCacheType()) + ":" + newPath;
    }
}
