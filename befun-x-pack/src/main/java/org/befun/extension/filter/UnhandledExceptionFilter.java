package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Order(XPackFilter.ORDER_UNHANDLED_EXCEPTION)
@Component("xPackUnhandledExceptionFilter")
public class UnhandledExceptionFilter implements WebInternalFilter {

    @Override
    public void postFilter(XPackFilterContext context) {
        Optional.ofNullable(context.getThrowable()).ifPresent(e -> {
            log.error("befunUnhandledException", e);
        });
    }

}
