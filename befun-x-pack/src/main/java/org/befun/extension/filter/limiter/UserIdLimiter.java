package org.befun.extension.filter.limiter;

import org.befun.core.rest.context.TenantContext;
import org.befun.extension.Extensions;
import org.befun.extension.constant.AccessLimitType;
import org.befun.extension.dto.AccessLimitConfigDto;
import org.befun.extension.filter.XPackFilterContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(BaseLimitFilter.ORDER_USER_ID)
@ConditionalOnProperty(name = Extensions.ACCESS_LIMIT_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.ACCESS_LIMIT_MATCH_IF_MISSING)
public class UserIdLimiter extends BaseLimitFilter implements LimitFilter{

    @Override
    public AccessLimitType type() {
        return AccessLimitType.USER_ID;
    }

    @Override
    public String buildLimitKey(XPackFilterContext context, AccessLimitConfigDto config) {
        return buildPrefixKey(config.getCacheType()) + ":" + TenantContext.getCurrentUserId();
    }
}
