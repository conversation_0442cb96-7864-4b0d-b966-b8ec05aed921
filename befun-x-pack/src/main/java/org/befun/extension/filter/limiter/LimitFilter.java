package org.befun.extension.filter.limiter;

import org.befun.extension.constant.AccessLimitType;
import org.befun.extension.dto.AccessLimitConfigDto;
import org.befun.extension.filter.XPackFilterContext;

import java.time.Duration;

public interface LimitFilter {

    AccessLimitType type();

    Boolean skip(AccessLimitConfigDto config);

    Boolean check(String key, Integer max, Duration duration);

    String buildLimitKey(XPackFilterContext context, AccessLimitConfigDto config);

}
