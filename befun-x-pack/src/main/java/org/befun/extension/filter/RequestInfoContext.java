package org.befun.extension.filter;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.context.TenantData;
import org.befun.core.utils.JsonHelper;

public class RequestInfoContext {

    private static final ThreadLocal<Data> httpData = new ThreadLocal<>();

    public static Data get() {
        return httpData.get();
    }

    public static Data createAndCache() {
        Data data = new Data();
        httpData.set(data);
        return data;
    }

    public static void clear() {
        httpData.set(null);
    }

    @Getter
    @Setter
    public static class Data {
        private long start = System.currentTimeMillis();
        private String method;
        private String url;
        private String token;
        private String requestParams;
        private String requestBody;

        private TenantData tenantData;
        private String user;
        private Object originResponse;
        private int responseStatus;
        private String responseBody;

        public String getUser() {
            if (user == null) {
                if (tenantData == null) {
                    return null;
                } else {
                    user = JsonHelper.toJson(tenantData);
                }
            }
            return user;
        }
    }
}
