package org.befun.extension.filter.limiter;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantData;
import org.befun.extension.constant.AccessLimitCacheType;
import org.befun.extension.constant.AccessLimitType;
import org.befun.extension.dto.AccessLimitConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;


@Component
@Slf4j
public class BaseLimitFilter {

    public AccessLimitType type;

    public static final String ACCESS_LIMIT_KEY_PREFIX = "limiter:access-limit";

    @Autowired
    public StringRedisTemplate stringRedisTemplate;

    public static final int ORDER_USER_ID = 100;
    public static final int ORDER_API = 200;
    public static final int ORDER_AUTHORIZATION = 300;
    public static final int ORDER_VERSIONS = 400;
    public static final int ORDER_ORG_ID = 500;

    public String buildPrefixKey(AccessLimitCacheType cacheType) {
        return ACCESS_LIMIT_KEY_PREFIX
                + ":" + cacheType.getFormat()
                + ":" + Objects.requireNonNull(TenantData.current()).getOrgId();
    }

    public Boolean skip(AccessLimitConfigDto config) {
        return config == null || config.getType().equals(type);
    }

    public Boolean check(String key, Integer max, Duration duration) throws BadRequestException {
        Long count = stringRedisTemplate.opsForValue().increment(key);
        log.info("incCount key:{}-count:{}-max:{}", key, count, max);
        if(count == null || count <= 1){
            stringRedisTemplate.expire(key, duration);
        }
        else return count <= max;
        return true;
    }
}
