package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;

@Slf4j
public class HttpServletResponseWrapper extends javax.servlet.http.HttpServletResponseWrapper {

    private final ResponsePrintWriter writer;
    private final ResponseOutputStream outputStream;
    private final ByteArrayOutputStream byteArray = new ByteArrayOutputStream();
    private String responseString;

    public HttpServletResponseWrapper(HttpServletResponse response) throws IOException {
        super(response);
        this.outputStream = new ResponseOutputStream(response.getOutputStream());
        this.writer = new ResponsePrintWriter(outputStream);
    }

    @Override
    public ServletOutputStream getOutputStream() {
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() {
        return writer;
    }

    public String getResponseString() {
        if (responseString == null && byteArray.size() > 0) {
            responseString = byteArray.toString();
        }
        return responseString;
    }

    public class ResponseOutputStream extends ServletOutputStream {

        private final ServletOutputStream os;

        public ResponseOutputStream(ServletOutputStream os) {
            this.os = os;
        }

        @Override
        public void write(int b) throws IOException {
            os.write(b);
            byteArray.write(b);
        }

        @Override
        public boolean isReady() {
            return os.isReady();
        }

        @Override
        public void setWriteListener(WriteListener listener) {
            os.setWriteListener(listener);
        }

    }

    public static class ResponsePrintWriter extends PrintWriter {

        public ResponsePrintWriter(OutputStream outputStream) {
            super(outputStream);
        }
    }
}
