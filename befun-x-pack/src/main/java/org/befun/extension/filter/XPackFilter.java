package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@Order(Integer.MAX_VALUE)
public class XPackFilter implements Filter, ApplicationRunner {

    @Autowired(required = false)
    private List<WebInternalFilter> filters;
    private final List<WebInternalFilter> reversedFilters = new ArrayList<>();

    public static final int ORDER_UNHANDLED_EXCEPTION = 1;
    public static final int ORDER_REQUEST_INFO = 2;
    public static final int ORDER_HTTP_LOG = 3;
    public static final int ORDER_OPERATE_LOG = 4;
    public static final int ORDER_ACCESS_LIMIT = 5;
    public static final int ORDER_XSS = 6;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (filters == null) {
            filters = new ArrayList<>();
        } else {
            for (WebInternalFilter f : filters) {
                reversedFilters.add(0, f);
            }
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        XPackFilterContext context = new XPackFilterContext((HttpServletRequest) request, (HttpServletResponse) response);
        try {
            for (WebInternalFilter filter : filters) {
                if (!filter.preFilter(context)) {
                    return;
                }
            }
            chain.doFilter(context.getWrapperRequestOrDefault(),context.getWrapperResponse());
        } catch (Throwable throwable) {
            context.setThrowable(throwable);
            throw throwable;
        } finally {
            for (WebInternalFilter filter : reversedFilters) {
                filter.postFilter(context);
            }
            clearTenant();
        }
    }


    private void clearTenant() {
        TenantContext.clear();
        TenantContext.copiedTenantData.set(null);
    }

}
