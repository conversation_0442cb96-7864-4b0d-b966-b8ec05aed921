package org.befun.extension.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CsvFieldOrderAnnotation {
        /**
         * 标注该属性的顺序
         * @return 该属性的顺序
         */
        int order();

        /**
         * 字段名称
         * @return 字段名称
         */
        String name();
}
