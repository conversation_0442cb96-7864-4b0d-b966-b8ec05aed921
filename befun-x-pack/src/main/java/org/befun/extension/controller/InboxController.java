package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.extension.Extensions;
import org.befun.extension.entity.InboxMessage;
import org.befun.extension.entity.InboxMessageDto;
import org.befun.extension.repository.InboxMessageRepository;
import org.befun.extension.service.InboxMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Tag(name = "站内信")
@RestController
@ResourceController(
        entityClass = InboxMessage.class,
        serviceClass = InboxMessageService.class,
        repositoryClass = InboxMessageRepository.class,
        permission = "isAuthenticated()",
        excludeActions = {CREATE, UPDATE_ONE, BATCH_UPDATE, DELETE_ONE, FIND_ONE},
        docTag = "站内信",
        docCrud = "站内信"
)
@RequestMapping("/inbox-messages")
@PreAuthorize("isAuthenticated()")
@ConditionalOnProperty(name = Extensions.INBOX_MESSAGE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.INBOX_MESSAGE_MATCH_IF_MISSING)
public class InboxController {

    @Autowired
    private InboxMessageService inboxMessageService;

    @PostMapping("{id}/read-one")
    @Operation(summary = "标记为已读-单条")
    public ResourceResponseDto<InboxMessageDto> readOne(@PathVariable("id") long id) {
        return new ResourceResponseDto<>(inboxMessageService.read(id));
    }

    @PostMapping("read-all")
    @Operation(summary = "标记为已读-全部")
    public ResourceResponseDto<Boolean> readAll() {
        return new ResourceResponseDto<>(inboxMessageService.readAll());
    }
}
