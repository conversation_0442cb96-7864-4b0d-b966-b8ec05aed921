package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.Extensions;
import org.befun.extension.dto.TemplateFileDto;
import org.befun.extension.property.TemplateFileProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.stream.Collectors;

@Tag(name = "模板文件下载")
@RestController
@RequestMapping("template-file")
@ConditionalOnProperty(name = Extensions.TEMPLATE_FILE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.TEMPLATE_FILE_MATCH_IF_MISSING)
public class TemplateFileController {

    @Autowired
    private TemplateFileProperty templateFileProperty;

    @GetMapping
    @Operation(summary = "获得所有模板地址")
    public ResourceListResponseDto<TemplateFileDto> findAll() {
        ResourceListResponseDto<TemplateFileDto> dto = new ResourceListResponseDto<>();
        if (templateFileProperty.isEnable()) {
            dto.setItems(templateFileProperty.getItems().stream().map(i -> new TemplateFileDto(i.getType(), i.getFileUrl())).collect(Collectors.toList()));
        }
        return dto;
    }

    @GetMapping("{type}")
    @Operation(summary = "获得指定的模板地址")
    public ResourceResponseDto<TemplateFileDto> findOne(@PathVariable String type) {
        TemplateFileDto template = null;
        if (templateFileProperty.isEnable()) {
            template = templateFileProperty.getItems()
                    .stream()
                    .filter(i -> type.equals(i.getType()))
                    .findFirst()
                    .map(i -> new TemplateFileDto(i.getType(), i.getFileUrl()))
                    .orElse(null);
        }
        if (template == null) {
            throw new BadRequestException("模板不存在");
        } else {
            return new ResourceResponseDto<>(template);
        }
    }

    @GetMapping("download/{type}")
    @Operation(summary = "下载指定的模板")
    public void downloadOne(@PathVariable String type, HttpServletResponse response) throws IOException {
        if (templateFileProperty.isEnable()) {
            String fileUrl = templateFileProperty.getItems()
                    .stream()
                    .filter(i -> type.equals(i.getType())).findFirst()
                    .map(TemplateFileProperty.TemplateFileItem::getFileUrl)
                    .orElse(null);
            if (StringUtils.isNotEmpty(fileUrl)) {
                response.sendRedirect(fileUrl);
                return;
            }
        }
        throw new BadRequestException("模板不存在");
    }

}
