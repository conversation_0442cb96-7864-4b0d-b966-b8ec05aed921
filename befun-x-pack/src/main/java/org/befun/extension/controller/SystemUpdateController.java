package org.befun.extension.controller;


import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.extension.Extensions;
import org.befun.extension.dto.SystemUpdateRequestDto;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@Hidden
@RestController
@RequestMapping("/system-update")
@ConditionalOnProperty(name = Extensions.SYSTEM_UPDATE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.SYSTEM_UPDATE_MATCH_IF_MISSING)
public class SystemUpdateController {

    @Autowired(required = false)
    private Map<String, AbstractSystemUpdateService> updateServiceMap;

    /**
     * 此接口可以多次调用，要保证幂等性
     * 参数格式如下
     * {
     * "version":"1.6.8",
     * "secret":"bf4d4a6a-5e2b-4b70-9e27-19d4bb499b81",
     * "type":"parts", // parts 只更新指定部分的; all 更新所有的
     * "parts":[
     * {
     * "name":"journeyMap",
     * "data":{"name":"journey"}
     * }
     * ]
     * }
     */
    @PostMapping
    public String update(@Validated @RequestBody SystemUpdateRequestDto dto) {
        TenantContext.clear();//清空登录状态
        String key = "systemUpdateService_" + dto.getVersion().replace(".", "_");
        AbstractSystemUpdateService service = updateServiceMap.get(key);
        if (service == null) {
            throw new BadRequestException("版本错误");
        }
        if (!dto.getSecret().equals(service.getSecret())) {
            throw new BadRequestException("密钥错误");
        }
        return service.systemUpdate(dto.getType(), dto.getAllData(), dto.getParts());
    }

}
