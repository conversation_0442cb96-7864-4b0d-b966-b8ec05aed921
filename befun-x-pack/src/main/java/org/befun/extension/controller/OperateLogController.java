package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.extension.Extensions;
import org.befun.extension.entity.OperateLog;
import org.befun.extension.repository.OperateLogRepository;
import org.befun.extension.service.AbstractOperateLogService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "日志管理")
@RestController
@ResourceController(
        entityClass = OperateLog.class,
        serviceClass = AbstractOperateLogService.class,
        repositoryClass = OperateLogRepository.class,
        permission = "isAuthenticated()",
        excludeActions = {CREATE, UPDATE_ONE, BATCH_UPDATE, DELETE_ONE, FIND_ONE},
        docTag = "日志管理",
        docCrud = "日志管理"
)
@RequestMapping("/operate-log")
@PreAuthorize("isAuthenticated()")
@ConditionalOnProperty(name = Extensions.OPERATE_LOG_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.HTTP_LOG_MATCH_IF_MISSING)
public class OperateLogController {

}
