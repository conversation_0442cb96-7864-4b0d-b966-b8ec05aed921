package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.extension.Extensions;
import org.befun.extension.constant.GraphCaptchaType;
import org.befun.extension.dto.GraphCaptchaResponseDto;
import org.befun.extension.dto.GraphCaptchaVerifyDto;
import org.befun.extension.service.GraphCaptchaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@Tag(name = "图形验证码")
@Validated
@RestController
@RequestMapping("graph-captcha")
@ConditionalOnProperty(name = Extensions.GRAPH_CAPTCHA_KEY, havingValue = "true", matchIfMissing = Extensions.GRAPH_CAPTCHA_IF_MISSING)
public class GraphCaptchaController {

    @Autowired
    private GraphCaptchaService graphCaptchaService;

    @GetMapping("get")
    @Operation(summary = "获得图形验证码")
    public ResourceResponseDto<GraphCaptchaResponseDto> create(@RequestParam GraphCaptchaType type) {
        return new ResourceResponseDto<>(graphCaptchaService.create(type));
    }

    @PostMapping("check")
    @Operation(summary = "校验图形验证码-成功后不清除缓存")
    public ResourceResponseDto<Boolean> check(@Valid @RequestBody GraphCaptchaVerifyDto dto) {
        return new ResourceResponseDto<>(graphCaptchaService.check(dto));
    }

    @PostMapping("verify")
    @Operation(summary = "校验图形验证码-成功后清除缓存")
    public ResourceResponseDto<Boolean> verify(@Valid @RequestBody GraphCaptchaVerifyDto dto) {
        return new ResourceResponseDto<>(graphCaptchaService.verify(dto));
    }

}

