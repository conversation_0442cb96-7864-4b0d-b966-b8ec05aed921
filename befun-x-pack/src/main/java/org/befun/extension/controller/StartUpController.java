package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.SneakyThrows;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.util.concurrent.atomic.AtomicBoolean;

@Hidden
@RestController
public class StartUpController implements ApplicationRunner {

    private final AtomicBoolean startUp = new AtomicBoolean();

    @Hidden
    @Operation(hidden = true)
    @GetMapping("start-up-status")
    public boolean startUpStatus() {
        return startUp.get();
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        startUp.compareAndSet(false, true);
    }

    @SneakyThrows
    @GetMapping("/create-time")
    public Object version() {
        ApplicationHome ah = new ApplicationHome();
        File source = ah.getSource();
        if (source != null) {
            BasicFileAttributes attrs = Files.readAttributes(source.toPath(), BasicFileAttributes.class);
            FileTime time = attrs.creationTime();
            String createTime = String.format("%s Creation time: %s%n", source.getName(), time);
            System.out.printf(createTime);
            return createTime;
        }
        return null;
    }


}
