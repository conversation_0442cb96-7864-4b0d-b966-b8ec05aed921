package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.Hidden;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.generator.SysConvert;
import org.befun.extension.Extensions;
import org.befun.extension.dto.CreateLinkDto;
import org.befun.extension.dto.CreateLinkResponseDto;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@Hidden
@RestController
@RequestMapping("t")
@ConditionalOnProperty(name = Extensions.SHORT_URL_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.SHORT_URL_MATCH_IF_MISSING)
public class LinkController {

    @Autowired
    private LinkService linkService;

    @Hidden
    @RequestMapping(method = RequestMethod.POST)
    public CreateLinkResponseDto create(@RequestBody CreateLinkDto dto) {
        return linkService.generateShortUrl(dto);
    }

    @Hidden
    @RequestMapping(value = "/{code}", method = RequestMethod.GET)
    public String access(@PathVariable String code, HttpServletResponse httpServletResponse) {

        String url = null;
        if (StringUtils.isNumeric(code)) {  //判断字符串，纯数字，则保存原始url，将对应id转化为自定义字符串
            url = linkService.findUrlById(Long.valueOf(code));
        } else {   //包含字母，则解析成数据库中对应的id，查询对应原始url
            url = linkService.parseRedirectUrl(code);
        }
        if (url != null) {
            String encodeUrl = linkService.encodeUrl(url); //对url中文参数解码，放入Location
            httpServletResponse.setHeader("Location", encodeUrl);
            httpServletResponse.setStatus(302);
        }
        return "";
    }
}
