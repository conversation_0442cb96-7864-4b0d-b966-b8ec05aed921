package org.befun.extension.service;

import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.template.TemplateEngine;
import org.befun.extension.Extensions;
import org.befun.extension.property.MailProperty;
import org.befun.extension.property.MailTemplateProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Service("xPackMailService")
@ConditionalOnProperty(name = Extensions.MAIL_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.MAIL_MATCH_IF_MISSING)
public class MailService {
    @Autowired
    private MailProperty mailProperty;
    private JavaMailSenderImpl mailSender;
    @Getter
    private final Map<String, MailTemplateProperty> templatePropertyMap = new HashMap<>();

    @PostConstruct
    private void setup() {
        if (!mailProperty.isEnable()) {
            return;
        }
        if (mailProperty.getHost() == null) {
            return;
        }
        log.info("init mail sender");
        mailSender = new JavaMailSenderImpl();
        mailSender.setHost(mailProperty.getHost());
        mailSender.setPort(mailProperty.getPort());

        mailSender.setUsername(mailProperty.getUsername());
        mailSender.setPassword(mailProperty.getPassword());

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", mailProperty.getProtocol());
        props.put("mail.smtp.auth", mailProperty.getEnableAuth().toString());
        props.put("mail.smtp.starttls.enable", mailProperty.getEnableStartTls().toString());
        props.put("mail.debug", mailProperty.getEnableDebug().toString());
        if (!mailProperty.getConfigs().isEmpty()) {
            mailProperty.getConfigs().forEach(i -> props.put(i.getName(), i.getValue()));
        }
        mailProperty.getTemplates().forEach(i -> {
            templatePropertyMap.put(i.getName(), i);
        });
    }

    /**
     * 用系统模版发送
     *
     * @param templateName
     * @param email
     * @param params
     * @return
     */
    public void sendMessageByTemplate(String templateName, String email, Map<String, Object> params) {
        MailTemplateProperty template = templatePropertyMap.get(templateName);
        if (template == null) {
            throw new BadRequestException("missing template");
        }
        sendMessage(email,
                template.getSubject(),
                template.getContent(),
                template.getDefaultEncoding(),
                template.getEnableHtml(),
                params);
    }

    /**
     * sendMessage
     *
     * @param email
     * @param subject
     * @param content
     * @param encoding
     * @param isHtml
     * @param params
     */
    @SneakyThrows
    public void sendMessage(String email, String subject, String content, String encoding, Boolean isHtml, Map<String, Object> params) {
        if (mailSender == null) {
            throw new BadRequestException("mail not config");
        }
        String newSubject = TemplateEngine.renderTextTemplate(
                subject,
                params
        );
        String newContent = TemplateEngine.renderTextTemplate(
                content,
                params
        );
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper message = new MimeMessageHelper(mimeMessage, encoding);
        message.setFrom(mailProperty.getFrom());
        message.setTo(email);
        message.setSubject(newSubject);
        message.setText(newContent, isHtml);
        mailSender.send(mimeMessage);
    }

    public static class EmptyMailService extends MailService {
        @Override
        public void sendMessageByTemplate(String templateName, String email, Map<String, Object> params) {
        }

        @Override
        public void sendMessage(String email, String subject, String content, String encoding, Boolean isHtml, Map<String, Object> params) {
        }
    }
}
