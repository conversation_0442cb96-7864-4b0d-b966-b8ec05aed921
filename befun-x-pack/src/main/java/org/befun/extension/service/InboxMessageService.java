package org.befun.extension.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.template.TemplateEngine;
import org.befun.extension.entity.InboxMessage;
import org.befun.extension.entity.InboxMessageDto;
import org.befun.extension.property.InboxMesasgeProperty;
import org.befun.extension.repository.InboxMessageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class InboxMessageService extends BaseService<InboxMessage, InboxMessageDto, InboxMessageRepository> {

    @Autowired
    private InboxMesasgeProperty inboxMesasgeProperty;
    @Autowired
    private InboxMessageRepository inboxMessageRepository;

    @Transactional(rollbackFor = Exception.class)
    public void addInboxMessage(Long orgId, Long userId, String templateName, Map<String, Object> params) {
        InboxMesasgeProperty.InboxMesasgeTemplateProperty property = inboxMesasgeProperty.getTemplates().get(templateName);
        if (property == null) {
            throw new BadRequestException("missing inbox message template " + templateName);
        }
        String text = TemplateEngine.renderTextTemplate2(property.getTitle(), params);
        String targetUrl = TemplateEngine.renderTextTemplate2(property.getUrl(), params);
        InboxMessage entity = new InboxMessage();
        entity.setOrgId(orgId);
        entity.setUserId(userId);
        entity.setType(property.getType());
        entity.setTitle(text);
        entity.setDescription(text);
        entity.setTargetUrl(targetUrl);
        entity.setReadStatus(false);
        entity.setDeleted(false);
        inboxMessageRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addInboxMessage(InboxMessage entity) {
        entity.setReadStatus(false);
        entity.setDeleted(false);
        inboxMessageRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public InboxMessageDto read(long id) {
        InboxMessage entity = inboxMessageRepository.findById(id).orElse(null);
        if (entity == null) {
            return null;
        }
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        if (entity.getUserId() == null || userId == null || !userId.equals(entity.getUserId()) || entity.getOrgId() == null || orgId == null || !orgId.equals(entity.getOrgId())) {
            throw new EntityNotFoundException();
        }
        if (entity.getReadStatus() != null && !entity.getReadStatus()) {
            entity.setReadStatus(true);
            inboxMessageRepository.save(entity);
        }
        return mapperService.map(entity, InboxMessageDto.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean readAll() {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        List<InboxMessage> list = inboxMessageRepository.findByOrgIdAndUserIdAndReadStatus(orgId, userId, false).stream().peek(i -> i.setReadStatus(true)).collect(Collectors.toList());
        inboxMessageRepository.saveAll(list);
        return true;
    }
}
