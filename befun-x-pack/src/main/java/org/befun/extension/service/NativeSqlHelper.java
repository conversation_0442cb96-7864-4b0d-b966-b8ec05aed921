package org.befun.extension.service;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.query.PageResult;
import org.befun.extension.nativesql.SqlBuilder;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.ReflectionUtils;

import javax.persistence.EntityManager;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class NativeSqlHelper {

    @Autowired
    private ConversionService conversionService;
    @Autowired
    private EntityManager entityManager;

    private final Map<Class<?>, MapToObjectConvert<?>> convertMap = new HashMap<>();

    public int update(String sql) {
        try (Session session = entityManager.unwrap(Session.class)) {
            NativeQuery query = session.createNativeQuery(sql);
            return query.executeUpdate();
        }
    }

    public long count(String sql) {
        return count(sql, null);
    }

    public String queryString(String sql) {
        return queryString(sql, null);
    }

    public List<String> queryListString(String sql) {
        return queryListString(sql, null);
    }

    public <T> T queryObject(String sql, Class<T> tClass) {
        return queryObject(sql, null, tClass);
    }

    public <T> List<T> queryListObject(String sql, Class<T> tClass) {
        return queryListObject(sql, null, tClass);
    }

    public long count(String sql, Consumer<NativeQuery> queryConsumer) {
        List<Object> result = query(sql, queryConsumer);
        Object value = getFirstValue(result);
        long count = 0;
        if (value instanceof Number) {
            count = ((Number) value).longValue();
        }
        return count;
    }

    private Object getFirstValue(List<Object> list) {
        Object value;
        if (CollectionUtils.isEmpty(list) || (value = list.get(0)) == null) {
            return null;
        }
        return getFirstValue(value);
    }

    private Object getFirstValue(Object value) {
        // 如果value是数组，则取第一个元素
        if (value instanceof Object[] && ((Object[]) value).length > 0) {
            value = ((Object[]) value)[0];
        }
        // 如果value是列表，则取第一个元素
        if (value instanceof List && ((List) value).size() > 0) {
            value = ((List) value).get(0);
        }
        return value;
    }

    public String queryString(String sql, Consumer<NativeQuery> queryConsumer) {
        List<Object> result = query(sql, queryConsumer);
        Object value = getFirstValue(result);
        return value == null ? null : conversionService.convert(value, String.class);
    }

    public List<String> queryListString(String sql, Consumer<NativeQuery> queryConsumer) {
        List<Object> result = query(sql, queryConsumer);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result.stream().map(value -> {
            Object i = getFirstValue(value);
            return i == null ? null : conversionService.convert(i, String.class);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public <T> T queryObject(String sql, Consumer<NativeQuery> queryConsumer, Class<T> tClass) {
        if (tClass.equals(String.class)) {
            return (T) queryString(sql);
        }
        List<Map<String, Object>> result = queryMap(sql, queryConsumer);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        MapToObjectConvert<T> convert = getConvert(tClass);
        return convert.mapToObject(result.get(0));
    }

    public <T> List<T> queryListObject(String sql, Consumer<NativeQuery> queryConsumer, Class<T> tClass) {
        if (tClass.equals(String.class)) {
            return (List<T>) queryListString(sql);
        }
        List<Map<String, Object>> result = queryMap(sql, queryConsumer);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        MapToObjectConvert<T> convert = getConvert(tClass);
        return result.stream().map(convert::mapToObject).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public <E extends BaseEntity> Page<E> queryEntityPage(SqlBuilder sqlBuilder, BiFunction<Integer, List<Long>, List<E>> getList) {
        return queryPage(sqlBuilder, BaseEntity::getId, getList);
    }

    public <D extends BaseEntityDTO<?>> Page<D> queryDtoPage(SqlBuilder sqlBuilder, BiFunction<Integer, List<Long>, List<D>> getList) {
        return queryPage(sqlBuilder, BaseEntityDTO::getId, getList);
    }

    public <T> Page<T> queryPage(SqlBuilder sqlBuilder, Function<T, Long> getId, BiFunction<Integer, List<Long>, List<T>> getList) {
        String countSql = sqlBuilder.buildCountSql();
        log.debug("countSql: {}", countSql);
        long count = count(countSql);
        if (count > 0) {
            List<T> dataList = Optional.ofNullable(queryList(sqlBuilder, getId, getList)).orElse(new ArrayList<>());
            return new PageResult<>(dataList, PageRequest.of(sqlBuilder.getPage() - 1, sqlBuilder.getSize()), count);
        }
        return Page.empty();
    }

    public <T> List<T> queryList(SqlBuilder sqlBuilder, Function<T, Long> getId, BiFunction<Integer, List<Long>, List<T>> getList) {
        String selectSql = sqlBuilder.buildSelectSql();
        log.debug("selectSql: {}", selectSql);
        List<IdType> idTypes = queryListObject(selectSql, IdType.class);
        if (CollectionUtils.isNotEmpty(idTypes)) {
            Map<Integer, List<Long>> typeIdMap = new HashMap<>();
            idTypes.forEach(idType -> {
                if (idType.getId() != null) {
                    idType.key = String.format("%d-%d", idType.type, idType.id);
                    typeIdMap.computeIfAbsent(idType.type, i -> new ArrayList<>()).add(idType.id);
                }
            });
            Map<String, T> dataMap = new HashMap<>();
            typeIdMap.forEach((type, ids) -> {
                if (type != null && CollectionUtils.isNotEmpty(ids)) {
                    Optional.ofNullable(getList.apply(type, ids)).ifPresent(list -> {
                        list.forEach(data -> {
                            dataMap.put(String.format("%d-%d", type, getId.apply(data)), data);
                        });
                    });
                }
            });
            List<T> dataList = new ArrayList<>();
            idTypes.forEach(idType -> {
                T data = dataMap.get(idType.getKey());
                if (data != null) {
                    dataList.add(data);
                }
            });
            return dataList;
        }
        return null;
    }

    public List<Long> queryIds(SqlBuilder sqlBuilder) {
        String selectSql = sqlBuilder.buildSelectSql();
        log.debug("selectSql: {}", selectSql);
        List<IdType> idTypes = queryListObject(selectSql, IdType.class);
        if (CollectionUtils.isNotEmpty(idTypes)) {
            return idTypes.stream().map(IdType::getId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<Map<String, Object>> queryMap(String sql, Consumer<NativeQuery> queryConsumer) {
        try (Session session = entityManager.unwrap(Session.class)) {
            NativeQuery query = session.createNativeQuery(sql);
            Optional.ofNullable(queryConsumer).ifPresent(consumer -> consumer.accept(query));
            return query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        }
    }

    private List<Object> query(String sql, Consumer<NativeQuery> queryConsumer) {
        try (Session session = entityManager.unwrap(Session.class)) {
            NativeQuery query = session.createNativeQuery(sql);
            Optional.ofNullable(queryConsumer).ifPresent(consumer -> consumer.accept(query));
            return query.list();
        }
    }

    private <T> MapToObjectConvert<T> getConvert(Class<T> tClass) {
        MapToObjectConvert<T> convert = (MapToObjectConvert<T>) convertMap.get(tClass);
        if (convert == null) {
            synchronized (this) {
                convert = (MapToObjectConvert<T>) convertMap.computeIfAbsent(tClass, k -> new MapToObjectConvert<>(tClass));
            }
        }
        return convert;
    }

    public class MapToObjectConvert<T> {
        private final Class<T> tClass;
        private final List<FieldConsumer> fieldConsumers;

        public MapToObjectConvert(Class<T> tClass) {
            this.tClass = tClass;
            fieldConsumers = new ArrayList<>();
            ReflectionUtils.doWithFields(tClass, field -> fieldConsumers.add(new FieldConsumer(field)));
        }

        public T mapToObject(Map<String, Object> values) {
            try {
                T object = tClass.getConstructor().newInstance();
                fieldConsumers.forEach(consumer -> consumer.accept(object, values));
                return object;
            } catch (Throwable e) {
                log.error("", e);
                return null;
            }
        }

        public class FieldConsumer implements BiConsumer<Object, Map<String, Object>> {
            private final Field field;
            private final List<String> maybeNames;   // 把驼峰和下划线这两种命名方式都对应到同一个属性映射方法

            public FieldConsumer(Field field) {
                this.field = field;
                this.maybeNames = maybeNames(field.getName());
            }

            @Override
            public void accept(Object object, Map<String, Object> values) {
                Object originValue = originValue(values);
                if (originValue != null) {
                    Object value = conversionService.convert(originValue, field.getType()); // 把 value 转换为和 field 相同的类型
                    ReflectionUtils.makeAccessible(field);
                    ReflectionUtils.setField(field, object, value);
                }
            }

            private Object originValue(Map<String, Object> values) {
                for (String name : maybeNames) {
                    Object originValue = values.get(name);
                    if (originValue != null) {
                        return originValue;
                    }
                }
                return null;
            }
        }
    }

    /**
     * 通过驼峰名称，解析出所有可能的下划线名称
     */
    public static List<String> maybeNames(String name) {
        List<String> names = new ArrayList<>();
        names.add(name);
        names.add(name.toUpperCase());
        boolean has = false;
        StringBuilder chars = new StringBuilder();
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);
            if (i > 0 && c >= 'A' && c <= 'Z') {
                chars.append('_');
                chars.append((char) (c + 32));
                has = true;
            } else {
                chars.append(c);
            }
        }
        if (has) {
            Optional.of(chars.toString()).ifPresent(i -> {
                names.add(i);
                names.add(i.toUpperCase());
            });
        }
        return names;
    }

    @Getter
    @Setter
    public static class IdType {
        private Long id;
        private Integer type = 1;
        private String key;
    }
}
