package org.befun.extension.service;

import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedisBetterConfigImpl;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import org.befun.extension.Extensions;
import org.befun.extension.property.WeChatMiniProgramProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;


@Service("xPackWeChatMiniProgramService")
@ConditionalOnProperty(name = Extensions.WX_MINIPROGRAM_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.WX_MINIPROGRAM_MATCH_IF_MISSING)
public class WeChatMiniProgramService extends WxMaServiceImpl {

    public WeChatMiniProgramService(StringRedisTemplate stringRedisTemplate, WeChatMiniProgramProperty property) {
        RedisTemplateWxRedisOps ops = new RedisTemplateWxRedisOps(stringRedisTemplate);
        WxMaRedisBetterConfigImpl config = new WxMaRedisBetterConfigImpl(ops, property.getPrefix());
        config.setAppid(property.getAppId());   // 设置小程序的appid
        config.setSecret(property.getAppSecret()); // 设置小程序的app corpSecret
        config.setToken(property.getToken());   // 设置小程序的token
        config.setAesKey(property.getAesKey()); // 设置小程序的EncodingAESKey
        config.setMsgDataFormat(property.getMsgDateFormat());
        setWxMaConfig(config);
    }
}