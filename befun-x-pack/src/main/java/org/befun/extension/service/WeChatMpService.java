package org.befun.extension.service;

import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import org.befun.extension.Extensions;
import org.befun.extension.property.WeChatMpProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;


@Service("xPackWeChatMpService")
@ConditionalOnProperty(name = Extensions.WX_MP_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.WX_MP_MATCH_IF_MISSING)
public class WeChatMpService extends WxMpServiceImpl {

    public WeChatMpService(StringRedisTemplate stringRedisTemplate, WeChatMpProperty property) {
        RedisTemplateWxRedisOps ops = new RedisTemplateWxRedisOps(stringRedisTemplate);
        WxMpDefaultConfigImpl config = new WxMpRedisConfigImpl(ops, property.getPrefix());
        config.setAppId(property.getAppId());   // 设置微信公众号的appid
        config.setSecret(property.getAppSecret()); // 设置微信公众号的app corpSecret
        config.setToken(property.getToken());   // 设置微信公众号的token
        config.setAesKey(property.getAesKey()); // 设置微信公众号的EncodingAESKey
        setWxMpConfigStorage(config);
    }
}