package org.befun.extension.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.Extensions;
import org.befun.extension.dto.*;
import org.befun.extension.property.AlipayConfig;
import org.befun.extension.property.AlipayProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service("xPackAliPayService")
@ConditionalOnClass(AlipayClient.class)
@ConditionalOnProperty(name = Extensions.ALIPAY_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.ALIPAY_MATCH_IF_MISSING)
public class AlipayService {

    private final AlipayProperty property;
    private final AlipayClient alipayClient;
    private AlipayClient sanboxAlipayClient;

    public AlipayService(AlipayProperty property) {
        this.property = property;
        this.alipayClient = buildClient(property);
        if (property.isUseSandbox() && property.getSandbox() != null) {
            this.sanboxAlipayClient = buildClient(property.getSandbox());
        }
    }

    private AlipayClient buildClient(AlipayConfig config) {
        return new DefaultAlipayClient(
                config.getAlipayServerUrl(),
                config.getAppId(),
                config.getAppPrivateKey(),
                config.getFormat(),
                config.getCharset(),
                config.getAlipayPublicKey(),
                config.getSignType(),
                config.getEncryptKey(),
                config.getEncryptType()
        );
    }

    public AlipayPlaceOrderResponseDto webPlaceOrder(boolean sandbox, String payNo, long amount, String title, Date expire, String pageUrl) {
        if (sandbox) {
            return webPlaceOrderSandbox(payNo, amount, title, expire, pageUrl);
        } else {
            return webPlaceOrder(payNo, amount, title, expire, pageUrl);
        }
    }

    public AlipayPlaceOrderResponseDto webPlaceOrder(String payNo, long amount, String title, Date expire, String pageUrl) {
        return webPlaceOrder0(false, alipayClient, property, payNo, amount, title, expire, pageUrl);
    }

    public AlipayPlaceOrderResponseDto webPlaceOrderSandbox(String payNo, long amount, String title, Date expire, String pageUrl) {
        if (sanboxAlipayClient == null) {
            throw new BadRequestException("支付宝下单失败，沙箱模式未启用");
        }
        return webPlaceOrder0(true, sanboxAlipayClient, property.getSandbox(), payNo, amount, title, expire, pageUrl);
    }

    private AlipayPlaceOrderResponseDto webPlaceOrder0(boolean sandbox, AlipayClient client, AlipayConfig config, String payNo, long amount, String title, Date expire, String pageUrl) {
        String tag = "web place order";
        if (sandbox && sanboxAlipayClient == null) {
            throw new BadRequestException("支付宝下单失败，沙箱模式未启用");
        }
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        request.setNotifyUrl(config.getPayNotifyUrl());
        request.setReturnUrl(pageUrl);
        Map<String, Object> content = new LinkedHashMap<>();
        content.put("out_trade_no", payNo);
        content.put("total_amount", amount * 1.0 / 100);
        content.put("subject", title);
        content.put("product_code", "FAST_INSTANT_TRADE_PAY");
        if (expire != null) {
            content.put("time_expire", DateHelper.formatDateTime(expire));
        }
        request.setBizContent(JsonHelper.toJson(content));
        try {
            AlipayTradePagePayResponse response = client.pageExecute(request, config.getPayMethod());
            logOrder(tag, sandbox, response.isSuccess(), request, response, null);
            if (response.isSuccess()) {
                AlipayPlaceOrderResponseDto dto = new AlipayPlaceOrderResponseDto();
                dto.setPayNo(payNo);
                dto.setPayUrl(response.getBody());
                dto.setRequest(request);
                dto.setResponse(response);
                return dto;
            }
        } catch (AlipayApiException e) {
            logOrder(tag, sandbox, false, request, null, e);
        }
        throw new BadRequestException("支付宝下单失败");
    }

    public AlipayQueryOrderResponseDto queryOrder(boolean sandbox, String payNo, String alipayNo) {
        if (sandbox) {
            return queryOrderSandbox(payNo, alipayNo);
        } else {
            return queryOrder(payNo, alipayNo);
        }
    }

    public AlipayQueryOrderResponseDto queryOrder(String payNo, String alipayNo) {
        return queryOrder0(false, alipayClient, payNo, alipayNo);
    }

    public AlipayQueryOrderResponseDto queryOrderSandbox(String payNo, String alipayNo) {
        if (sanboxAlipayClient == null) {
            throw new BadRequestException("支付宝订单查询失败，沙箱模式未启用");
        }
        return queryOrder0(true, sanboxAlipayClient, payNo, alipayNo);
    }

    private AlipayQueryOrderResponseDto queryOrder0(boolean sandbox, AlipayClient client, String payNo, String alipayNo) {
        String tag = "query order";
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        Map<String, Object> content = new LinkedHashMap<>();
        content.put("trade_no", alipayNo);
        content.put("out_trade_no", payNo);
        request.setBizContent(JsonHelper.toJson(content));
        try {
            AlipayTradeQueryResponse response = client.execute(request);
            logOrder(tag, sandbox, response.isSuccess(), request, response, null);
            AlipayQueryOrderResponseDto dto = new AlipayQueryOrderResponseDto();
            dto.setRequest(request);
            dto.setResponse(response);
            if (response.isSuccess()) {
                dto.setPayNo(response.getOutTradeNo());
                dto.setAlipayNo(response.getTradeNo());
                dto.setPayStatus(response.getTradeStatus());
                dto.setPayTime(response.getSendPayDate());
                return dto;
            } else if ("ACQ.TRADE_NOT_EXIST".equals(response.getSubCode())) {
                dto.setNotExist(true);
                return dto;
            }
        } catch (AlipayApiException e) {
            logOrder(tag, sandbox, false, request, null, e);
        }
        throw new BadRequestException("支付宝订单查询失败");
    }

    public AlipayCloseOrderResponseDto closeOrder(boolean sandbox, String payNo, String alipayNo) {
        if (sandbox) {
            return closeOrderSandbox(payNo, alipayNo);
        } else {
            return closeOrder(payNo, alipayNo);
        }
    }

    public AlipayCloseOrderResponseDto closeOrder(String payNo, String alipayNo) {
        return closeOrder0(false, alipayClient, payNo, alipayNo);
    }

    public AlipayCloseOrderResponseDto closeOrderSandbox(String payNo, String alipayNo) {
        if (sanboxAlipayClient == null) {
            throw new BadRequestException("支付宝订单关闭失败，沙箱模式未启用");
        }
        return closeOrder0(true, sanboxAlipayClient, payNo, alipayNo);
    }

    private AlipayCloseOrderResponseDto closeOrder0(boolean sandbox, AlipayClient client, String payNo, String alipayNo) {
        String tag = "close order";
        AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
        Map<String, Object> content = new LinkedHashMap<>();
        content.put("trade_no", alipayNo);
        content.put("out_trade_no", payNo);
        request.setBizContent(JsonHelper.toJson(content));
        try {
            AlipayTradeCloseResponse response = client.execute(request);
            logOrder(tag, sandbox, response.isSuccess(), request, response, null);
            if (response.isSuccess()) {
                AlipayCloseOrderResponseDto dto = new AlipayCloseOrderResponseDto();
                dto.setPayNo(response.getOutTradeNo());
                dto.setAlipayNo(response.getTradeNo());
                dto.setRequest(request);
                dto.setResponse(response);
                return dto;
            }
        } catch (AlipayApiException e) {
            logOrder(tag, sandbox, false, request, null, e);
        }
        throw new BadRequestException("支付宝订单关闭失败");
    }

    public AlipayRefundOrderResponseDto refundOrder(boolean sandbox, String payNo, String alipayNo, String refundNo, long refundAmount) {
        if (sandbox) {
            return refundOrderSandbox(payNo, alipayNo, refundNo, refundAmount);
        } else {
            return refundOrder(payNo, alipayNo, refundNo, refundAmount);
        }
    }

    public AlipayRefundOrderResponseDto refundOrder(String payNo, String alipayNo, String refundNo, long refundAmount) {
        return refundOrder0(false, alipayClient, payNo, alipayNo, refundNo, refundAmount);
    }

    public AlipayRefundOrderResponseDto refundOrderSandbox(String payNo, String alipayNo, String refundNo, long refundAmount) {
        if (sanboxAlipayClient == null) {
            throw new BadRequestException("支付宝订单退款失败，沙箱模式未启用");
        }
        return refundOrder0(true, sanboxAlipayClient, payNo, alipayNo, refundNo, refundAmount);
    }

    private AlipayRefundOrderResponseDto refundOrder0(boolean sandbox, AlipayClient client, String payNo, String alipayNo, String refundNo, long refundAmount) {
        String tag = "refund order";
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        Map<String, Object> content = new LinkedHashMap<>();
        content.put("trade_no", alipayNo);
        content.put("out_trade_no", payNo);
        content.put("refund_amount", refundAmount * 1.0 / 100);
        content.put("out_request_no", refundNo);
        request.setBizContent(JsonHelper.toJson(content));
        try {
            AlipayTradeRefundResponse response = client.execute(request);
            logOrder(tag, sandbox, response.isSuccess(), request, response, null);
            if (response.isSuccess()) {
                AlipayRefundOrderResponseDto dto = new AlipayRefundOrderResponseDto();
                dto.setPayNo(payNo);
                dto.setAlipayNo(alipayNo);
                dto.setRefundNo(refundNo);
                dto.setRefundStatus(response.getFundChange());
                dto.setRequest(request);
                dto.setResponse(response);
                return dto;
            }
        } catch (AlipayApiException e) {
            logOrder(tag, sandbox, false, request, null, e);
        }
        throw new BadRequestException("支付宝订单退款失败");
    }

    public AlipayQueryRefundOrderResponseDto queryRefundOrder(boolean sandbox, String payNo, String alipayNo, String refundNo) {
        if (sandbox) {
            return queryRefundOrderSandbox(payNo, alipayNo, refundNo);
        } else {
            return queryRefundOrder(payNo, alipayNo, refundNo);
        }
    }

    public AlipayQueryRefundOrderResponseDto queryRefundOrder(String payNo, String alipayNo, String refundNo) {
        return queryRefundOrder0(false, alipayClient, payNo, alipayNo, refundNo);
    }

    public AlipayQueryRefundOrderResponseDto queryRefundOrderSandbox(String payNo, String alipayNo, String refundNo) {
        if (sanboxAlipayClient == null) {
            throw new BadRequestException("支付宝订单退款查询失败，沙箱模式未启用");
        }
        return queryRefundOrder0(true, sanboxAlipayClient, payNo, alipayNo, refundNo);
    }

    private AlipayQueryRefundOrderResponseDto queryRefundOrder0(boolean sandbox, AlipayClient client, String payNo, String alipayNo, String refundNo) {
        String tag = "refund query order";
        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        Map<String, Object> content = new LinkedHashMap<>();
        content.put("trade_no", alipayNo);
        content.put("out_trade_no", payNo);
        content.put("out_request_no", refundNo);
        List<String> options = new ArrayList<>();
        options.add("gmt_refund_pay");
        content.put("query_options", options);
        request.setBizContent(JsonHelper.toJson(content));
        try {
            AlipayTradeFastpayRefundQueryResponse response = client.execute(request);
            logOrder(tag, sandbox, response.isSuccess(), request, response, null);
            if (response.isSuccess()) {
                AlipayQueryRefundOrderResponseDto dto = new AlipayQueryRefundOrderResponseDto();
                dto.setPayNo(payNo);
                dto.setAlipayNo(alipayNo);
                dto.setRefundNo(refundNo);
                dto.setRefundStatus(response.getRefundStatus());
                dto.setRefundTime(response.getGmtRefundPay());
                dto.setRequest(request);
                dto.setResponse(response);
                return dto;
            }
        } catch (AlipayApiException e) {
            logOrder(tag, sandbox, false, request, null, e);
        }
        throw new BadRequestException("支付宝订单退款查询失败");
    }

    private void logOrder(String tag, boolean sandbox, boolean success, Object request, Object response, Throwable e) {
        log.info("alipay {} {} {}, request: {}, response: {}",
                tag,
                sandbox ? "sandbox" : "",
                success ? "success" : e == null ? "failure" : "error",
                JsonHelper.toJson(request),
                JsonHelper.toJson(response),
                e
        );
    }

    public void checkSign(boolean sandbox, Map<String, String> params) {
        if (sandbox) {
            if (sanboxAlipayClient == null) {
                throw new BadRequestException("支付宝订单退款查询失败，沙箱模式未启用");
            }
            checkSign(params, property.getSandbox());
        } else {
            checkSign(params, property);
        }
    }

    private void checkSign(Map<String, String> params, AlipayConfig config) {
        Throwable ee = null;
        boolean success = false;
        try {
            success = AlipaySignature.rsaCheckV1(params,
                    config.getAlipayPublicKey(),
                    config.getCharset(),
                    config.getSignType());
        } catch (AlipayApiException e) {
            ee = e;
        }
        if (!success) {
            log.error("alipay check sign fail, params: {}", JsonHelper.toJson(params), ee);
            throw new BadRequestException("支付宝订单支付回调验签失败");
        }
    }

    /**
     * 支付宝的回调没有明确的字段区分是支付成功回调还是退款回调，
     * 这里只解析出订单号，然后通过接口去查询结果
     * <a href="https://opendocs.alipay.com/open/270/105902?pathHash=d5cd617e">异步通知说明</a>
     */
    public String parsePlaceOrderCallback(boolean sandbox, Map<String, String> params) {
        checkSign(sandbox, params);
        return params.get("out_trade_no");
    }

}
