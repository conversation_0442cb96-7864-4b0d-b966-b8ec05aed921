package org.befun.extension.service;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.open.api.WxOpenMpService;
import me.chanjar.weixin.open.api.impl.WxOpenInRedisConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.Extensions;
import org.befun.extension.dto.MessageSendResponseInfo;
import org.befun.extension.property.WeChatOpenProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service("xPackWeChatOpenService")
@ConditionalOnProperty(name = Extensions.WX_OPEN_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.WX_OPEN_MATCH_IF_MISSING)
public class WeChatOpenService extends WxOpenServiceImpl {

    public WeChatOpenService(StringRedisTemplate stringRedisTemplate, WeChatOpenProperty weChatOpenProperty) {
        RedisTemplateWxRedisOps ops = new RedisTemplateWxRedisOps(stringRedisTemplate);
        WxOpenInRedisConfigStorage storage = new WxOpenInRedisConfigStorage(ops, null);
        storage.setComponentAppId(weChatOpenProperty.getComponentAppId());
        storage.setComponentAppSecret(weChatOpenProperty.getComponentSecret());
        storage.setComponentToken(weChatOpenProperty.getComponentToken());
        storage.setComponentAesKey(weChatOpenProperty.getComponentAesKey());
        if (weChatOpenProperty.isHttpProxyEnable()) {
            storage.setHttpProxyHost(weChatOpenProperty.getHttpProxyHost());
            storage.setHttpProxyPort(weChatOpenProperty.getHttpProxyPort());
            if (StringUtils.isNotEmpty(weChatOpenProperty.getHttpProxyUsername()) && StringUtils.isNotEmpty(weChatOpenProperty.getHttpProxyUsername())) {
                storage.setHttpProxyUsername(weChatOpenProperty.getHttpProxyUsername());
                storage.setHttpProxyPassword(weChatOpenProperty.getHttpProxyPassword());
            }
        }
        setWxOpenConfigStorage(storage);
    }

    /**
     * 获得公众号服务
     */
    private WxOpenMpService getMpService(String appId) {
        return Optional.ofNullable(getWxOpenComponentService().getWxMpServiceByAppid(appId))
                .orElseGet(() -> {
                    log.warn("微信公众号未授权，appId = {}", appId);
                    return null;
                });
    }

    /**
     * 发送模板消息
     */
    public MessageSendResponseInfo sendTemplateMsg2(String appId, WxMpTemplateMessage message) {
        MessageSendResponseInfo response = new MessageSendResponseInfo();
        if (StringUtils.isNotEmpty(appId) && message != null) {
            response.setContent(message.toJson());
            String result = Optional.ofNullable(getMpService(appId))
                    .map(WxMpService::getTemplateMsgService)
                    .map(k -> {
                        try {
                            return k.sendTemplateMsg(message);
                        } catch (WxErrorException e) {
                            log.error("发送微信模板消息失败, appId={}, message = {}", appId, message.toJson(), e);
                        }
                        return null;
                    })
                    .orElse(null);
            if (result != null) {
                response.setSuccess(true);
                response.setResponse(result);
            }
        }
        if (response.isSuccess()) {
            log.warn("发送微信模板消息失败，appId = {}, message = {}", appId, response.getContent());
        }
        return response;

    }

    /**
     * 发送模板消息
     */
    public String sendTemplateMsg(String appId, WxMpTemplateMessage message) {
        return sendTemplateMsg2(appId, message).getResponse();
    }

    /**
     * 发送客服消息
     */
    public boolean sendKefuMessage(String appId, WxMpKefuMessage message) {
        boolean result = false;
        if (StringUtils.isNotEmpty(appId) && message != null) {
            result = Optional.ofNullable(getMpService(appId))
                    .map(WxMpService::getKefuService)
                    .map(k -> {
                        try {
                            return k.sendKefuMessage(message);
                        } catch (WxErrorException e) {
                            log.error("发送微信客服消息失败, appId={}, message = {}", appId, message.toJson(), e);
                        }
                        return false;
                    })
                    .orElse(false);
        }
        if (!result) {
            log.warn("发送微信客服消息失败，appId = {}, message = {}", appId, message == null ? "null" : message.toJson());
        }
        return result;
    }

    /**
     * 网页签名
     */
    public WxJsapiSignature createJsapiSignature(String appId, String url) {
        WxJsapiSignature result = null;
        if (StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(url)) {
            result = Optional.ofNullable(getMpService(appId))
                    .map(k -> {
                        try {
                            return k.createJsapiSignature(url);
                        } catch (WxErrorException e) {
                            log.error("创建微信网页授权签名失败, appId={}, url = {}", appId, url, e);
                        }
                        return null;
                    })
                    .orElse(null);
        }
        if (result == null) {
            log.warn("创建微信网页授权签名失败，appId = {}, url = {}", appId, url);
        }
        return result;
    }

    /**
     * 获得关注用户openId列表
     */
    public WxMpUserList getUserIdList(String appId, String nextId) {
        WxMpUserList result = null;
        if (StringUtils.isNotEmpty(appId)) {
            result = Optional.ofNullable(getMpService(appId))
                    .map(WxMpService::getUserService)
                    .map(k -> {
                        try {
                            return k.userList(nextId);
                        } catch (WxErrorException e) {
                            log.error("获取关注用户openId列表失败, appId={}, nextId = {}", appId, nextId, e);
                        }
                        return null;
                    })
                    .orElse(null);
        }
        if (result == null) {
            log.warn("获取关注用户openId列表失败，appId = {}, nextId = {}", appId, nextId);
        }
        return result;
    }

    /**
     * 获得关注用户信息列表
     */
    public List<WxMpUser> getUserInfoList(String appId, List<String> openIds) {
        List<WxMpUser> result = null;
        if (StringUtils.isNotEmpty(appId) && CollectionUtils.isNotEmpty(openIds)) {
            result = Optional.ofNullable(getMpService(appId))
                    .map(WxMpService::getUserService)
                    .map(k -> {
                        try {
                            return k.userInfoList(openIds);
                        } catch (WxErrorException e) {
                            log.error("获取关注用户信息列表失败, appId={}, openIds = {}", appId, JsonHelper.toJson(openIds), e);
                        }
                        return null;
                    })
                    .orElse(null);
        }
        if (result == null) {
            log.warn("获取关注用户信息列表失败，appId = {}, openIds = {}", appId, JsonHelper.toJson(openIds));
        }
        return result;
    }

    /**
     * 获得消息模版列表
     */
    public List<WxMpTemplate> getTemplateList(String appId) {
        List<WxMpTemplate> result = null;
        if (StringUtils.isNotEmpty(appId)) {
            result = Optional.ofNullable(getMpService(appId))
                    .map(WxMpService::getTemplateMsgService)
                    .map(k -> {
                        try {
                            return k.getAllPrivateTemplate();
                        } catch (WxErrorException e) {
                            log.error("获取消息模版列表表失败, appId={}", appId, e);
                        }
                        return null;
                    })
                    .orElse(null);
        }
        if (result == null) {
            log.warn("获取消息模版列表失败，appId = {},", appId);
        }
        return result;
    }

}
