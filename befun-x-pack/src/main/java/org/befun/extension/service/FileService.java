package org.befun.extension.service;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.common.file.storage.MockMultipartFile;
import cn.hanyi.common.file.storage.UploadPretreatment;
import cn.hanyi.common.file.storage.platform.FileStorage;
import cn.hanyi.common.file.storage.platform.LocalFileStorage;
import cn.hanyi.common.file.storage.recorder.FileRecorder;
import com.glaforge.i18n.io.CharsetToolkit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.RegHelper;
import org.befun.core.utils.SafeStringUtils;
import org.befun.extension.annotations.CsvFieldOrderAnnotation;
import org.befun.extension.dto.CsvDto;
import org.befun.extension.dto.FileUploadDto;
import org.befun.extension.repository.FileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service("xPackFileService")
@ConditionalOnProperty(name = "hanyi.common.file-storage.default-platform")
public class FileService implements FileRecorder {

    final static String CSV_TYPE = "text/csv";
    final static String TYPE_ERROR = "文件类型错误";
    final static String DATA_ERROR = "数据内容格式错误";
    final static String PARSER_ERROR = "CSV解析错误";
    final public static String PRIVATE_PATH = "private/";

    @Lazy
    @Autowired
    public FileStorageService fileStorageService;

    @Autowired
    private FileRepository fileRepository;

    @Value("${hanyi.common.file-storage.enable-delete:false}")
    private boolean enableDelete;

    @Value("${hanyi.common.file-storage.xss-check:true}")
    private boolean xssCheck;

    @Override
    public boolean record(FileInfo fileInfo) {
        return true;
    }

    protected static final String TOKEN = "a146efda-f7f3-4a65-8607-dd7fb58263e5";

    @SneakyThrows
    public FileInfo uploadWithToken(MultipartFile file, String path, String token, Boolean useFileName) {
        if (!TOKEN.equals(token)) {
            throw new BadRequestException("error token");
        }

        if (useFileName != null && useFileName) {
            MockMultipartFile mockMultipartFile = new MockMultipartFile(file.getOriginalFilename(), file.getOriginalFilename(), MediaType.MULTIPART_FORM_DATA_VALUE, file.getBytes());
            return upload(mockMultipartFile, path, URLEncoder.encode(Objects.toString(file.getOriginalFilename()), StandardCharsets.UTF_8));
        }

        return upload(file, path, null);
    }

    public FileInfo upload(MultipartFile file, FileUploadDto fileUploadDto, String fileName, Boolean encode) {
        return upload(file, fileUploadDto, buildFileName(file, false));
    }

    public FileInfo upload(MultipartFile file, FileUploadDto uploadDto, String fileName) {
        log.info("upload original file name: {}, file name:{}", file.getOriginalFilename(), file.getName());
        validateFileForXss(file);
        UploadPretreatment uploadPretreatment = fileStorageService.of(file);
        fileName = StringUtils.isEmpty(fileName) ?  buildFileName(file, true): SafeStringUtils.safeTitle(fileName);
        uploadPretreatment.setSaveFilename(fileName);
        uploadPretreatment.setName(fileName);
        if(uploadDto.isPrivate){
            uploadPretreatment.setPath(PRIVATE_PATH);
        }
        FileInfo fileInfo = uploadPretreatment.upload();

        log.info("upload file url: {}", fileInfo.getUrl());
        return fileInfo;
    }

    public FileInfo upload(MultipartFile file, String path, String fileName) {
        log.info("upload original file name: {}, file name:{}", file.getOriginalFilename(), file.getName());
        UploadPretreatment uploadPretreatment = fileStorageService.of(file);

        if (path != null) {
            uploadPretreatment.setPath(path + "/");
        }

        fileName = StringUtils.isEmpty(fileName)?  buildFileName(file, true) : SafeStringUtils.safeTitle(fileName);
        uploadPretreatment.setSaveFilename(fileName);
        uploadPretreatment.setName(fileName);

        FileInfo fileInfo = uploadPretreatment.upload();
        log.info("upload file url: {}", fileInfo.getUrl());
        return fileInfo;

    }
    private String buildFileName(MultipartFile file, Boolean encode) {
        encode = encode == null || encode;
        // 文件名_时间戳.后缀
        String fileOriginalName = file.getOriginalFilename();
        String suffix = null;
        if (StringUtils.isNotEmpty(fileOriginalName)) {
            int index = fileOriginalName.lastIndexOf(".");
            if (index >= 0) {
                suffix = fileOriginalName.substring(index);
                fileOriginalName = fileOriginalName.substring(0, index);
            }
        }
        if (StringUtils.isEmpty(fileOriginalName)) {
            fileOriginalName = UUID.randomUUID().toString();
        } else {
            fileOriginalName = encode ? URLEncoder.encode(fileOriginalName, StandardCharsets.UTF_8) : fileOriginalName;
        }
        if (StringUtils.isEmpty(suffix)) {
            try {
                String s = file.getContentType();
                if (StringUtils.isNotEmpty(s)) {
                    ContentType contentType = ContentType.parse(s);
                    String mimeType = contentType.getMimeType();
                    if (mimeType != null) {
                        String[] ss = mimeType.split("/");
                        if (ss.length == 2 || ss[0].equalsIgnoreCase("image")) {
                            suffix = "." + ss[1];
                        }
                    }
                }
            } catch (Throwable e) {
                log.error("files upload content parse error {}", e.getMessage());
            }
        }
        if (suffix == null) {
            suffix = "";
        } else {
            suffix = encode ? URLEncoder.encode(suffix, StandardCharsets.UTF_8) : suffix;
        }
        return fileOriginalName + "_" + System.currentTimeMillis() + suffix;
    }

    @SneakyThrows
    public byte[] download(String url, HttpServletResponse response) {
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(url);
        response.setHeader("content-disposition", String.format("attachment;filename=%s", URLEncoder.encode(fileInfo.getFilename(), "UTF-8")));
        return fileStorageService.download(fileInfo).bytes();

    }

    @SneakyThrows
    public byte[] download(String url, Consumer<String> fileName) {
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(url);
        fileName.accept(URLEncoder.encode(fileInfo.getFilename(), "UTF-8"));
        return fileStorageService.download(fileInfo).bytes();

    }

    @Override
    public FileInfo getByUrl(String s) {
        try {
            FileStorage storagePlatform = fileStorageService.getFileStorage();
            FileInfo fileInfo = new FileInfo();
            String filename = RegHelper.parseFileName(s);
            fileInfo.setBasePath(storagePlatform.getBasePath());
            fileInfo.setFilename(filename);
            fileInfo.setPath(s.contains(PRIVATE_PATH)? PRIVATE_PATH : "");
            fileInfo.setUrl(fileInfo.getBasePath() + filename);
            fileInfo.setPlatform(storagePlatform.getPlatform());
            log.info("get file:{} info by url: {} ", fileInfo.getFilename(), s);
            return fileInfo;
        } catch (Exception e) {
            log.error(e.toString());
        }
        return null;

    }

    @Override
    public boolean delete(String s) {
        try {
            if (enableDelete) {
                FileInfo file = fileStorageService.getFileInfoByUrl(s);
                log.info("delete file:{} by url: {} ", file.getFilename(), s);
                return fileStorageService.delete(file, fileInfo -> fileStorageService.exists(fileInfo));
            }
        } catch (Exception e) {
            log.error("file service delete:{} error: {}", s, e.toString());
        }
        return false;
    }

    /**
     * 解析csv文件
     * 需要定义好csv的DTO，使用@CsvFieldOrderAnnotation注解标明字段顺序和字段名称
     * DTO需要由无惨构造
     *
     * @param multipartFile
     * @param dtoClass
     * @param <T>
     * @return CSVDto or null
     */
    public <T> CsvDto parseCsvFile(MultipartFile multipartFile, Class<T> dtoClass) {
        if (!CSV_TYPE.equalsIgnoreCase(multipartFile.getContentType())) {
            log.error("{}: {}", TYPE_ERROR, multipartFile.getOriginalFilename());
            return null;
        }

        CsvDto<T> csvDto = new CsvDto<>();
        csvDto.setName(multipartFile.getOriginalFilename());
        csvDto.setSize(multipartFile.getSize());

        try {

            InputStream is = multipartFile.getInputStream();
            Charset charset = guessCharset(is);
            CSVParser csvRecords = CSVParser.parse(new BOMInputStream(multipartFile.getInputStream()), charset, CSVFormat.DEFAULT);
            List<CSVRecord> records = csvRecords.getRecords();

            // 无数据返回null
            if (records.size() < 2) {
                log.error("{}: {}", DATA_ERROR, multipartFile.getOriginalFilename());
                return null;
            }

            // 获取带CsvFieldOrderAnnotation的字段并且排序
            Field[] fields = Arrays.stream(dtoClass.getDeclaredFields())
                    .filter(field -> field.isAnnotationPresent(CsvFieldOrderAnnotation.class))
                    .sorted(Comparator.comparingInt(field -> field.getAnnotation(CsvFieldOrderAnnotation.class).order()))
                    .toArray(Field[]::new);

            List<String> headers = Arrays.stream(fields).map(field -> field.getAnnotation(CsvFieldOrderAnnotation.class).name()).collect(Collectors.toList());

            // dto的字段与csv文件的字段不匹配
            if (headers.size() != records.subList(0, 1).get(0).size()) {
                log.error("{}: {}", DATA_ERROR, multipartFile.getOriginalFilename());
                return null;
            }

            csvDto.setHeader(headers);
            List<CSVRecord> rows = records.subList(1, records.size());

            for (CSVRecord row : rows) {
                T rowObj = dtoClass.getConstructor().newInstance();
                for (int i = 0; i < fields.length; i++) {
                    PropertyUtils.setProperty(
                            rowObj,
                            fields[i].getName(),
                            ConvertUtils.convert(row.get(i), fields[i].getType()));
                }
                csvDto.getRows().add(rowObj);
            }

        } catch (Exception e) {
            log.error("{}: {} with {}", PARSER_ERROR, multipartFile.getOriginalFilename(), e.toString());
            return null;
        }
        return csvDto;
    }

    /**
     * 获取字符编码
     *
     * @param is
     * @return
     */
    private Charset guessCharset(InputStream is) {
        Charset charset = null;
        try {
            byte[] buffer = new byte[1024];
            if (is.read(buffer) != -1) {
                CharsetToolkit toolkit = new CharsetToolkit(buffer);
                toolkit.setDefaultCharset(Charset.forName("GBK"));
                is.reset();
                charset = toolkit.guessEncoding();
            }
        } catch (Exception e) {
            //ignore
        }
        return charset == null ? StandardCharsets.UTF_8 : charset;
    }

    // 添加XSS检测的正则表达式
    private static final Pattern XSS_PATTERN = Pattern.compile(
            "<script[^>]*>.*?</script>|javascript:|onerror=|onload=|eval\\(|setTimeout\\(|setInterval\\(|document\\.cookie",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    
    /**
     * 检查文件内容是否包含XSS脚本
     * @param inputStream 文件输入流
     * @return 如果包含XSS返回true，否则返回false
     */
    public boolean containsXssContent(InputStream inputStream) {
        try {
            // 只检查文本类型的文件
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
            
            return XSS_PATTERN.matcher(content.toString()).find();
        } catch (Exception e) {
            // 如果无法读取文件内容，出于安全考虑返回false
            return false;
        }
    }
    
    /**
     * 上传文件前进行XSS检查
     * @param file 要上传的文件
     * @throws SecurityException 如果文件包含XSS脚本
     */
    public void validateFileForXss(MultipartFile file) throws SecurityException {
        if (!xssCheck || file == null || file.isEmpty()) {
            return;
        }
        
        // 检查文件名是否包含XSS
        String filename = file.getOriginalFilename();
        if (filename != null && XSS_PATTERN.matcher(filename).find()) {
            throw new BadRequestException("请勿上传XSS文件");
        }
        
        // 检查文件内容是否包含XSS (仅检查文本类型文件)
        String contentType = file.getContentType();
        if (contentType != null && (
                contentType.startsWith("text/") || 
                contentType.contains("javascript") || 
                contentType.contains("json") || 
                contentType.contains("xml") ||
                contentType.contains("html"))) {
            try {
                if (containsXssContent(file.getInputStream())) {
                    throw new BadRequestException("请勿上传XSS文件");
                }
            } catch (IOException e) {
                throw new BadRequestException("无法读取文件内容" + e);
            }
        }
    }
    

}
