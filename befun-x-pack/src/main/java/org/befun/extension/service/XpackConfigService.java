package org.befun.extension.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.BaseService;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.entity.XpackConfigDto;
import org.befun.extension.repository.XpackConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class XpackConfigService extends BaseService<XpackConfig, XpackConfigDto, XpackConfigRepository> {

    @Autowired
    private XpackConfigRepository xpackConfigRepository;

    public XpackConfig getConfigByType(XPackAppType type) {
        return xpackConfigRepository.findFirstByType(type);
    }

    public List<XpackConfig> getConfigsByType(XPackAppType type) {
        return xpackConfigRepository.findByType(type);
    }

    public XpackConfig getConfigByTypeAndSubType(XPackAppType type, String subType) {
        return xpackConfigRepository.findFirstByTypeAndSubType(type, subType);
    }

    public List<XpackConfig> getConfigsByTypeAndSubTypeOrSubType(XPackAppType type, String subType, String orSubType) {
        return xpackConfigRepository.findByTypeAndSubTypeOrSubTypeOrderBySubTypeDesc(type, subType, orSubType);
    }
}
