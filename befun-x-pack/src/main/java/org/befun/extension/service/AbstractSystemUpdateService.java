package org.befun.extension.service;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.constant.SystemUpdateType;
import org.befun.extension.dto.SystemUpdatePartRequestDto;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public abstract class AbstractSystemUpdateService {

    private final AtomicBoolean lock = new AtomicBoolean();

    private final List<String> completeParts = new ArrayList<>();
    private String currentPart = null;

    @Transactional
    public String systemUpdate(SystemUpdateType type, Map<String, String> allData, List<SystemUpdatePartRequestDto> parts) {
        if (lock.compareAndSet(false, true)) {
            try {
                Map<String, Consumer<Map<String, String>>> updateTasks = getUpdateTasks();
                if (MapUtils.isNotEmpty(updateTasks)) {
                    if (type == SystemUpdateType.ALL) {
                        updateTasks.forEach((k, v) -> {
                            currentPart = k;
                            Optional.ofNullable(v).ifPresent(i -> i.accept(allData));
                            completeParts.add(k);
                        });
                    } else {
                        if (CollectionUtils.isEmpty(parts)) {
                            throw new BadRequestException("parts不能为空");
                        }
                        String notExists = parts.stream().map(SystemUpdatePartRequestDto::getName).filter(name -> !updateTasks.containsKey(name)).collect(Collectors.joining(","));
                        if (StringUtils.isNotEmpty(notExists)) {
                            throw new BadRequestException("parts不存在：" + notExists);
                        }
                        parts.forEach(part -> {
                            currentPart = part.getName();
                            Optional.ofNullable(updateTasks.get(part.getName())).orElseThrow().accept(part.getData());
                            completeParts.add(part.getName());
                        });
                    }
                }
            } finally {
                currentPart = null;
                completeParts.clear();
                lock.set(false);
            }
            return "ok";
        } else {
            return completeText();
        }
    }

    private String completeText() {
        StringBuilder sb = new StringBuilder();
        if (currentPart != null) {
            sb.append("正在处理：").append(currentPart);
        }
        if (!completeParts.isEmpty()) {
            sb.append("，已处理：").append(String.join(",", completeParts));
        }
        return sb.toString();
    }

    /**
     * 获得此版本的密钥
     */
    public abstract String getSecret();

    public abstract Map<String, Consumer<Map<String, String>>> getUpdateTasks();

}
