package org.befun.extension.service;

import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.github.binarywang.wxpay.util.RequestUtils;
import com.github.binarywang.wxpay.v3.util.RsaCryptoUtil;
import java.security.cert.X509Certificate;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.Extensions;
import org.befun.extension.dto.transfer.TransferBillsCancelResult;
import org.befun.extension.dto.transfer.TransferBillsGetResult;
import org.befun.extension.dto.transfer.TransferBillsNotifyResult;
import org.befun.extension.dto.transfer.TransferBillsRequest;
import org.befun.extension.dto.transfer.TransferBillsResult;
import org.befun.extension.property.WeChatPayProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Slf4j
@Service("xPackWeChatPayService")
@ConditionalOnProperty(name = Extensions.WX_PAY_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.WX_PAY_MATCH_IF_MISSING)
public class WeChatPayService extends WxPayServiceImpl {

    public WeChatPayService(WeChatPayProperty property) {
        WxPayConfig config = new WxPayConfig();
        config.setAppId(property.getAppId());
        config.setMchId(property.getMchId());
        config.setMchKey(property.getV2MchKey());
        config.setApiV3Key(property.getV3MchKey());
        config.setUseSandboxEnv(property.isUseSandbox());
        config.setNotifyUrl(property.getPayNotifyUrl());
        config.setKeyPath(property.getCertP12Path());
        config.setPrivateKeyPath(property.getPrivateKeyPath());
        config.setPrivateCertPath(property.getCertPemPath());
        addConfig(property.getMchId(), config);
    }

    /**
     * 发起商家转账
     * @param request
     * @return
     * @throws WxPayException
     */
    public TransferBillsResult transferBills(TransferBillsRequest request) throws WxPayException {
        String url = String.format("%s/v3/fund-app/mch-transfer/transfer-bills", this.getPayBaseUrl());
        if (request.getUserName() != null && !request.getUserName().isEmpty()) {
            X509Certificate validCertificate = this.getConfig().getVerifier().getValidCertificate();
            RsaCryptoUtil.encryptFields(request, validCertificate);
        }
        String result = this.postV3WithWechatpaySerial(url, JsonHelper.toJson(request));
        return JsonHelper.toObject(result, TransferBillsResult.class);
    }

    /**
     *撤销转账
     * @param outBillNo
     * @return
     * @throws WxPayException
     */
    public TransferBillsCancelResult transformBillsCancel(String outBillNo) throws WxPayException {
        String url = String.format("%s/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/%s/cancel",
                this.getPayBaseUrl(), outBillNo);
        String result = this.postV3(url, "");

        return JsonHelper.toObject(result, TransferBillsCancelResult.class);
    }

    /**
     * 商户单号查询转账账单
     * @param outBillNo
     * @return
     * @throws WxPayException
     */
    public TransferBillsGetResult getBillsByOutBillNo(String outBillNo) throws WxPayException {
        String url = String.format("%s/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/%s",
                this.getPayBaseUrl(), outBillNo);
        String result = this.getV3(url);
        return JsonHelper.toObject(result, TransferBillsGetResult.class);
    }

    /**
     * 微信单号查询转账账单
     * @param transferBillNo
     * @return
     * @throws WxPayException
     */
    public TransferBillsGetResult getBillsByTransferBillNo(String transferBillNo) throws WxPayException {
        String url = String.format("%s/v3/fund-app/mch-transfer/transfer-bills/transfer-bill-no/%s",
                this.getPayBaseUrl(), transferBillNo);
        String result = this.getV3(url);
        return JsonHelper.toObject(result, TransferBillsGetResult.class);
    }

    /**
     * 解析转账回调通
     * @return
     * @throws WxPayException
     */
    public TransferBillsNotifyResult parseTransferBillsNotifyResult(HttpServletRequest request) throws WxPayException {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        Optional.ofNullable(timestamp).orElseThrow(() -> new RuntimeException("时间戳不能为空"));

        String nonce = request.getHeader("Wechatpay-Nonce");
        Optional.ofNullable(nonce).orElseThrow(() -> new RuntimeException("nonce不能为空"));

        String serialNo = request.getHeader("Wechatpay-Serial");
        Optional.ofNullable(serialNo).orElseThrow(() -> new RuntimeException("serialNo不能为空"));

        String signature = request.getHeader("Wechatpay-Signature");
        Optional.ofNullable(signature).orElseThrow(() -> new RuntimeException("signature不能为空"));

        log.info("转账回调请求头参数为：timestamp:{} nonce:{} serialNo:{} signature:{}", timestamp, nonce, serialNo, signature);

        return this.baseParseOrderNotifyV3Result(RequestUtils.readData(request),
                new SignatureHeader(timestamp, nonce, signature, serialNo),
                TransferBillsNotifyResult.class, TransferBillsNotifyResult.DecryptNotifyResult.class);
    }

}
