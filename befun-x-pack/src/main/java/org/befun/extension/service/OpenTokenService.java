package org.befun.extension.service;

import org.befun.core.utils.JsonHelper;
import org.befun.extension.property.OpenTokenProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.UUID;

@Service
public class OpenTokenService {

    private static final String OPEN_TOKEN_PREFIX = "openToken:";

    @Autowired
    private OpenTokenProperty openTokenProperty;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public String createToken(Map<String, Object> params) {
        return createToken0(null, params);
    }

    public String createToken(Integer expiredSecond, Map<String, Object> params) {
        return createToken0(expiredSecond, params);
    }

    public String createToken(Integer expiredSecond, Object params) {
        return createToken0(expiredSecond, params);
    }

    public String createToken(Object params) {
        return createToken0(null, params);
    }

    private <T> String createToken0(Integer expiredSecond, T params) {
        if (expiredSecond == null) {
            expiredSecond = openTokenProperty.getExpiredSeconds();
        }
        if (expiredSecond <= 0) {
            expiredSecond = 1800;
        }
        String value;
        if (params == null) {
            value = "{}";
        } else {
            value = JsonHelper.toJson(params);
        }
        String token = UUID.randomUUID().toString();
        String key = OPEN_TOKEN_PREFIX + token;
        stringRedisTemplate.opsForValue().set(key, value, Duration.ofSeconds(expiredSecond));
        return token;
    }

    public <T> T getTokenParams(String token, Class<T> type) {
        return JsonHelper.toObject(getTokenParamsString(token), type);
    }

    public Map<String, Object> getTokenParams(String token) {
        return JsonHelper.toMap(getTokenParamsString(token));
    }

    private String getTokenParamsString(String token) {
        String key = OPEN_TOKEN_PREFIX + token;
        return stringRedisTemplate.opsForValue().get(key);
    }
}
