package org.befun.extension.service;

import org.befun.core.exception.BadRequestException;
import org.befun.extension.captcha.IGraphCaptcha;
import org.befun.extension.constant.GraphCaptchaType;
import org.befun.extension.dto.GraphCaptchaResponseDto;
import org.befun.extension.dto.GraphCaptchaVerifyDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GraphCaptchaService {

    @Autowired
    private List<IGraphCaptcha> graphCaptchaList = new ArrayList<>();

    private IGraphCaptcha requireGraphCaptcha(GraphCaptchaType type) {
        return graphCaptchaList.stream().filter(captcha -> captcha.type() == type)
                .findFirst()
                .orElseThrow(() -> new BadRequestException("不支持的验证码类型"));
    }

    public GraphCaptchaResponseDto create(GraphCaptchaType type) {
        return requireGraphCaptcha(type).create();
    }

    public boolean verify(GraphCaptchaVerifyDto dto) {
        return requireGraphCaptcha(dto.getType()).verify(dto);
    }

    public boolean check(GraphCaptchaVerifyDto dto) {
        return requireGraphCaptcha(dto.getType()).check(dto);
    }

    public void clear(GraphCaptchaVerifyDto dto) {
        requireGraphCaptcha(dto.getType()).clear(dto.getKey());
    }
}
