package org.befun.extension.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.RestUtils;
import org.befun.extension.Extensions;
import org.befun.extension.dto.SmartVerifyRequestDto;
import org.befun.extension.property.SmartVerifyProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;


@Slf4j
@Service("xPackSmartVerifyService")
@ConditionalOnProperty(name = Extensions.SMART_VERIFY_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.SMART_VERIFY_MATCH_IF_MISSING)
public class SmartVerifyService {

    private static IAcsClient client;

    @Autowired
    private SmartVerifyProperty verifyProperty;


    @PostConstruct
    private void setUp() {
        //初始化IClientProfile，对象profile存放ak、aks、默认的地域信息
        IClientProfile profile = DefaultProfile.getProfile(verifyProperty.getRegionId(), verifyProperty.getAccessKeyId(), verifyProperty.getAccessKeySecret());
        //构建客户端，链接阿里云
        client = new DefaultAcsClient(profile);
        DefaultProfile.addEndpoint(verifyProperty.getRegionId(), verifyProperty.getProduct(), verifyProperty.getDomain());
    }


    public boolean isVerify(SmartVerifyRequestDto dto, HttpServletRequest httpRequest) {
        //构建验签请求
        AuthenticateSigRequest request = new AuthenticateSigRequest();
        request.setSessionId(dto.getSessionId());// 会话ID。必填参数，从前端获取，不可更改。
        request.setSig(dto.getSign());// 签名串。必填参数，从前端获取，不可更改。
        request.setToken(dto.getToken());// 请求唯一标识。必填参数，从前端获取，不可更改。
        request.setScene(dto.getScene());// 场景标识：问卷提交。必填参数，从前端获取，不可更改。
        request.setAppKey(verifyProperty.getAppKey());// 应用类型标识。必填参数，后端填写。
        request.setRemoteIp(RestUtils.getClientIpAddress(httpRequest));// 客户端IP。必填参数，后端填写。

        try {
            AuthenticateSigResponse response = client.getAcsResponse(request);
            if (response != null) {
                log.debug("fetch response info code:{} msg:{}", response.getCode(), response.getMsg());
            } else throw new BadRequestException("response为空，验签失败");
            //response的code枚举：100验签通过，900验签失败。
            if (response.getCode() == 100) {
                return true;
            }
        } catch (ClientException e) {
            e.printStackTrace();
        }
        return false;
    }

}
