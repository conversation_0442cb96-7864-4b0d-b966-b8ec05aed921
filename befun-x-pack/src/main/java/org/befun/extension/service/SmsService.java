package org.befun.extension.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.Extensions;
import org.befun.extension.constant.AccountSafetyType;
import org.befun.extension.dto.MessageSendResponseInfo;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.dto.SmsTemplatePatternDto;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.property.SmsProperty;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.sms.ISmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service("xPackSmsService")
@ConditionalOnProperty(name = Extensions.SMS_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.SMS_MATCH_IF_MISSING)
public class SmsService {

    @Autowired
    private XpackConfigService xpackConfigService;
    @Autowired(required = false)
    private Map<String, ISmsProvider<?>> providerMap = new HashMap<>();
    @Autowired
    private SmsProperty property;
    private Map<String, SmsProviderProperty> propertyMap = new HashMap<>();

    @PostConstruct
    private void setup() {
        if (property.isEnable()) {
            property.getProviders().forEach(p -> {
                p.replacePlaceholder();
                propertyMap.put(p.getName(), p);
                Optional.ofNullable(providerMap.get(p.getName())).ifPresent(i -> i.init(p));
            });
        }
    }

    /**
     * 列举支持类型
     * - provider support
     * - config support
     *
     * @return
     */
    public Set<String> supported() {
        return providerMap.keySet()
                .stream()
                .filter(p -> propertyMap.containsKey(p))
                .collect(Collectors.toSet());
    }

    public String getRealSignature() {
        String vendor = property.getVendor();
        return requireProvider(vendor).requireConfig(propertyMap.get(vendor)).getRealSignature();
    }

    public Map<String, SmsTemplateProperty> getTemplatePropertyMap() {
        String vendor = property.getVendor();
        return requireProvider(vendor).getTemplatePropertyMap();
    }

    private ISmsProvider<?> requireProvider(String vendor) {
        ISmsProvider<?> provider = providerMap.get(vendor);
        if (provider == null || !propertyMap.containsKey(vendor)) {
            throw new BadRequestException("missing sms provider" + vendor);
        }
        return provider;
    }

    private SmsTemplateProperty requireTemplate(String templateName) {
        SmsTemplateProperty template = getTemplatePropertyMap().get(templateName);
        if (template != null) {
            return template;
        }
        throw new BadRequestException("missing template");
    }

    private String renderContent(String content, String url, Map<String, Object> params) {
        Map<String, Object> copiedParams = new HashMap<>();
        if (MapUtils.isNotEmpty(params)) {
            copiedParams.putAll(params);
        }
        if (StringUtils.isNotEmpty(url)) {
            copiedParams.put("url", TemplateEngine.renderTextTemplate(url, params));
        }
        return TemplateEngine.renderTextTemplate(content, copiedParams);
    }

    /**
     * 用系统模版发送
     */
    public boolean sendMessageByTemplate(String templateName, String templateContent, String mobile, Map<String, Object> params) {
        return sendMessageByTemplate2(templateName, templateContent, mobile, params).isSuccess();
    }

    /**
     * 用系统模版发送
     */
    public boolean sendMessageByTemplate(String templateName, String mobile, Map<String, Object> params) {
        return sendMessageByTemplate(templateName, null, mobile, params);
    }

    /**
     * 用系统模版发送
     */
    public boolean sendMessage(SmsNotifyInfo info) {
        return sendMessage(property.getVendor(), info);
    }

    /**
     * 用系统模版发送
     */
    public boolean sendMessage(String vendor, SmsNotifyInfo info) {
        return sendMessage2(vendor, info).isSuccess();
    }

    /**
     * 发送文本短信
     */
    public boolean sendMessageByText(String mobile, String content, Map<String, Object> params) {
        return sendMessage(new SmsNotifyTextInfo(mobile, content, params));
    }

    /**
     * 发送文本短信
     */
    public boolean sendMessage(SmsNotifyTextInfo info) {
        return sendMessage(property.getVendor(), info);
    }

    /**
     * 发送文本短信
     */
    public boolean sendMessage(String vendor, SmsNotifyTextInfo info) {
        return sendMessage2(vendor, info).isSuccess();
    }

    /**
     * 用系统模版发送
     */
    public MessageSendResponseInfo sendMessageByTemplate2(String templateName, String mobile, Map<String, Object> params) {
        return sendMessageByTemplate2(templateName, null, mobile, params);
    }

    /**
     * 用系统模版发送
     * 返回包含，发送内容，发送结果
     */
    public MessageSendResponseInfo sendMessageByTemplate2(String templateName, String templateContent, String mobile, Map<String, Object> params) {
        SmsTemplateProperty template = requireTemplate(templateName);
        templateContent = templateContent == null ? template.getContent() : templateContent;

        SmsNotifyInfo info = SmsNotifyInfo.builder()
                .mobile(mobile)
                .content(templateContent)
                .templateName(templateName)
                .templateId(template.getId())
                .signature(template.getSignature())
                .realSignature(template.getRealSignature())
                .params(params)
                .build();
        return sendMessage2(info);
    }

    /**
     * 用系统模版发送
     * 返回包含，发送内容，发送结果
     */
    public MessageSendResponseInfo sendMessage2(SmsNotifyInfo info) {
        return sendMessage2(property.getVendor(), info);
    }

    /**
     * 用系统模版发送
     * 返回包含，发送内容，发送结果
     */
    public MessageSendResponseInfo sendMessage2(String vendor, SmsNotifyInfo info) {
        ISmsProvider<?> provider = requireProvider(vendor);
        info.setContent(renderContent(info.getContent(), info.getUrl(), info.getParams()));
        info.setVariableValues(parseVariables(provider, info));
        SmsProviderProperty configuration = propertyMap.get(vendor);
        boolean success = provider.sendMessageByTemplate(configuration, info);
        String realSignature = info.getRealSignature();
        if (StringUtils.isEmpty(realSignature)) {
            SmsProviderProperty.SmsProviderConfig config = provider.requireConfig(configuration);
            realSignature = config.getRealSignature();
        }
        if (StringUtils.isEmpty(realSignature)) {
            realSignature = "";
        }
        return new MessageSendResponseInfo(success,
                info.getRealCost(),
                realSignature + info.getContent(),
                info.getResponse(),
                vendor,
                info.getThirdpartyMessageId());
    }

    private SmsTemplatePatternDto getSmsTemplatePattern(ISmsProvider<?> provider, SmsNotifyInfo info) {
        if (StringUtils.isNotEmpty(info.getTemplateId())) {
            XpackConfig xpackConfig = xpackConfigService.getConfigByTypeAndSubType(provider.getTemplateConfigType(), info.getTemplateId());
            if (xpackConfig != null) {
                return JsonHelper.toObject(xpackConfig.getConfig(), SmsTemplatePatternDto.class);
            }
        }
        return null;
    }

    private List<SmsNotifyInfo.TemplateVariableValue> parseVariables(ISmsProvider<?> provider, SmsNotifyInfo info) {
        List<SmsNotifyInfo.TemplateVariableValue> variableValues = new ArrayList<>();
        String pattern0 = null;
        String variables0 = null;
        if ("ORGANIZATION".equalsIgnoreCase(info.getTemplateName())) {
            SmsTemplatePatternDto patternDto = getSmsTemplatePattern(provider, info);
            if (patternDto != null) {
                pattern0 = patternDto.getPattern();
                variables0 = patternDto.getVariables();
                info.setOriginTemplate(patternDto.getOriginTemplate());
            }
        } else {
            SmsTemplateProperty template = requireTemplate(info.getTemplateName());
            pattern0 = template.getPattern();
            variables0 = template.getVariables();
            info.setOriginTemplate(template.getOriginTemplate());
        }
        if (StringUtils.isNotEmpty(pattern0) && StringUtils.isNotEmpty(variables0)) {
            Pattern pattern = Pattern.compile(pattern0);
            Matcher matcher = pattern.matcher(info.getContent());
            String[] variables = variables0.split(",");
            if (matcher.find() && matcher.groupCount() == variables.length) {
                IntStream.range(0, variables.length).forEach(i -> {
                    variableValues.add(new SmsNotifyInfo.TemplateVariableValue(variables[i], matcher.group(i + 1)));
                });
            }
        }
        return variableValues;
    }

    /**
     * 发送文本短信
     * 返回包含，发送内容，发送结果
     */
    public MessageSendResponseInfo sendMessageByText2(String mobile, String content, Map<String, Object> params) {
        return sendMessage2(new SmsNotifyTextInfo(mobile, content, params));
    }

    /**
     * 发送文本短信
     * 返回包含，发送内容，发送结果
     */
    public MessageSendResponseInfo sendMessage2(SmsNotifyTextInfo info) {
        return sendMessage2(property.getVendor(), info);
    }

    /**
     * 发送文本短信
     * 返回包含，发送内容，发送结果
     */
    public MessageSendResponseInfo sendMessage2(String vendor, SmsNotifyTextInfo info) {
        ISmsProvider<?> provider = requireProvider(vendor);
        info.setContent(renderContent(info.getContent(), null, info.getParams()));
        SmsProviderProperty configuration = propertyMap.get(vendor);
        boolean success = provider.sendMessageByText(configuration, info);
        return new MessageSendResponseInfo(success, info.getRealCost(), info.getContent(), info.getResponse());
    }

    public AccountSafetyType checkAccount(String mobile, String ip) {
        return checkAccount(property.getVendor(), mobile, ip);
    }

    public AccountSafetyType checkAccount(String vendor, String mobile, String ip) {
        ISmsProvider<?> provider = requireProvider(vendor);
        SmsProviderProperty configuration = propertyMap.get(vendor);
        return provider.checkAccount(configuration, mobile, ip);
    }

    public static class EmptySmsService extends SmsService {
        @Override
        public Set<String> supported() {
            return new HashSet<>();
        }

        @Override
        public boolean sendMessageByTemplate(String templateName, String templateContent, String mobile, Map<String, Object> params) {
            return true;
        }

        @Override
        public boolean sendMessageByTemplate(String templateName, String mobile, Map<String, Object> params) {
            return true;
        }

        @Override
        public boolean sendMessage(SmsNotifyInfo info) {
            return true;
        }

        @Override
        public boolean sendMessage(String vendor, SmsNotifyInfo info) {
            return true;
        }

        @Override
        public boolean sendMessageByText(String mobile, String content, Map<String, Object> params) {
            return true;
        }

        @Override
        public boolean sendMessage(SmsNotifyTextInfo info) {
            return true;
        }

        @Override
        public boolean sendMessage(String vendor, SmsNotifyTextInfo info) {
            return true;
        }

        @Override
        public MessageSendResponseInfo sendMessageByTemplate2(String templateName, String mobile, Map<String, Object> params) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }

        @Override
        public MessageSendResponseInfo sendMessageByTemplate2(String templateName, String templateContent, String mobile, Map<String, Object> params) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }

        @Override
        public MessageSendResponseInfo sendMessage2(SmsNotifyInfo info) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }

        @Override
        public MessageSendResponseInfo sendMessage2(String vendor, SmsNotifyInfo info) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }

        @Override
        public MessageSendResponseInfo sendMessageByText2(String mobile, String content, Map<String, Object> params) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }

        @Override
        public MessageSendResponseInfo sendMessage2(SmsNotifyTextInfo info) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }

        @Override
        public MessageSendResponseInfo sendMessage2(String vendor, SmsNotifyTextInfo info) {
            return new MessageSendResponseInfo(true, 0, null, null);
        }
    }
}
