package org.befun.extension;

public interface Extensions {

    String UPLOAD_FILE_PREFIX = "befun.extension.update-file";
    String UPLOAD_FILE_ENABLE_KEY = UPLOAD_FILE_PREFIX + ".enable";
    boolean UPLOAD_FILE_MATCH_IF_MISSING = false;

    String INBOX_MESSAGE_PREFIX = "befun.extension.inbox-message";
    String INBOX_MESSAGE_ENABLE_KEY = INBOX_MESSAGE_PREFIX + ".enable";
    boolean INBOX_MESSAGE_MATCH_IF_MISSING = false;

    String SHORT_URL_PREFIX = "befun.extension.shorturl";
    String SHORT_URL_ENABLE_KEY = SHORT_URL_PREFIX + ".enable";
    boolean SHORT_URL_MATCH_IF_MISSING = false;

    String SYSTEM_UPDATE_PREFIX = "befun.extension.system-update";
    String SYSTEM_UPDATE_ENABLE_KEY = SYSTEM_UPDATE_PREFIX + ".enable";
    boolean SYSTEM_UPDATE_MATCH_IF_MISSING = false;

    String TEMPLATE_FILE_PREFIX = "befun.extension.template-file";
    String TEMPLATE_FILE_ENABLE_KEY = TEMPLATE_FILE_PREFIX + ".enable";
    boolean TEMPLATE_FILE_MATCH_IF_MISSING = false;

    String WX_OPEN_PREFIX = "befun.extension.wechat-open";
    String WX_OPEN_ENABLE_KEY = WX_OPEN_PREFIX + ".enable";
    boolean WX_OPEN_MATCH_IF_MISSING = false;

    String WX_MP_PREFIX = "befun.extension.wechat-mp";
    String WX_MP_ENABLE_KEY = WX_MP_PREFIX + ".enable";
    boolean WX_MP_MATCH_IF_MISSING = false;

    String WX_PAY_PREFIX = "befun.extension.wechat-pay";
    String WX_PAY_ENABLE_KEY = WX_PAY_PREFIX + ".enable";
    boolean WX_PAY_MATCH_IF_MISSING = false;

    String WX_MINIPROGRAM_PREFIX = "befun.extension.wechat-miniprogram";
    String WX_MINIPROGRAM_ENABLE_KEY = WX_MINIPROGRAM_PREFIX + ".enable";
    boolean WX_MINIPROGRAM_MATCH_IF_MISSING = false;

    String MAIL_PREFIX = "befun.extension.mail";
    String MAIL_ENABLE_KEY = MAIL_PREFIX + ".enable";
    boolean MAIL_MATCH_IF_MISSING = true;

    String SMS_PREFIX = "befun.extension.sms";
    String SMS_ENABLE_KEY = SMS_PREFIX + ".enable";
    boolean SMS_MATCH_IF_MISSING = true;

    String SMART_VERIFY_PREFIX = "befun.extension.smart-verify";
    String SMART_VERIFY_ENABLE_KEY = SMART_VERIFY_PREFIX + ".enable";
    boolean SMART_VERIFY_MATCH_IF_MISSING = false;

    String TOAST_MESSAGE_PREFIX = "befun.extension.toast-message";
    String TOAST_MESSAGE_ENABLE_KEY = TOAST_MESSAGE_PREFIX + ".enable";
    boolean TOAST_MESSAGE_MATCH_IF_MISSING = true;

    String HTTP_LOG_PREFIX = "befun.extension.http-log";
    String HTTP_LOG_ENABLE_KEY = HTTP_LOG_PREFIX + ".enable";
    boolean HTTP_LOG_MATCH_IF_MISSING = false;

    String XSS_PREFIX = "befun.extension.xss";
    String XSS_ENABLE_KEY = XSS_PREFIX + ".enable";
    boolean XSS_MATCH_IF_MISSING = false;

    String OPERATE_LOG_PREFIX = "befun.extension.operate-log";
    String OPERATE_LOG_ENABLE_KEY = OPERATE_LOG_PREFIX + ".enable";
    boolean OPERATE_LOG_MATCH_IF_MISSING = false;


    String ACCESS_LIMIT_PREFIX = "befun.extension.access-limit";
    String ACCESS_LIMIT_ENABLE_KEY = ACCESS_LIMIT_PREFIX + ".enable";
    boolean ACCESS_LIMIT_MATCH_IF_MISSING = false;


    String OPEN_TOKEN_PREFIX = "befun.extension.open-token";
    String OPEN_TOKEN_ENABLE_KEY = OPEN_TOKEN_PREFIX + ".enable";
    boolean OPEN_TOKEN_MATCH_IF_MISSING = true;

    String GRAPH_CAPTCHA_PREFIX = "befun.extension.graph-captcha";
    String GRAPH_CAPTCHA_KEY = GRAPH_CAPTCHA_PREFIX + ".enable";
    boolean GRAPH_CAPTCHA_IF_MISSING = false;

    String RSERVE_PREFIX = "befun.extension.rserve";
    String RSERVE_KEY = RSERVE_PREFIX + ".enable";
    boolean RSERVE_IF_MISSING = false;

    String ALIPAY_PREFIX = "befun.extension.alipay";
    String ALIPAY_ENABLE_KEY = WX_PAY_PREFIX + ".enable";
    boolean ALIPAY_MATCH_IF_MISSING = false;
}
