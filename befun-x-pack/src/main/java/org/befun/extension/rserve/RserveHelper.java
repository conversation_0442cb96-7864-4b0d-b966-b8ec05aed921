package org.befun.extension.rserve;


import lombok.extern.slf4j.Slf4j;
import org.befun.extension.Extensions;
import org.befun.extension.property.RserveProperty;
import org.rosuda.REngine.REXP;
import org.rosuda.REngine.REXPMismatchException;
import org.rosuda.REngine.Rserve.RConnection;
import org.rosuda.REngine.Rserve.RserveException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@ConditionalOnProperty(name = Extensions.RSERVE_KEY, havingValue = "true", matchIfMissing = Extensions.RSERVE_IF_MISSING)
public class RserveHelper {

    @Autowired
    private RserveProperty rserveProperty;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    public String csvToSav(String url) {
        Future<String> future = executorService.submit(() -> _csvToSav(url));
        try {
            return future.get(rserveProperty.getTimeoutMs(), TimeUnit.MILLISECONDS);
        } catch (Throwable e) {
            log.error("csvToSav error", e);
            return url;
        }
    }

    private String _csvToSav(String originUrl) {
        try {
            log.info("originUrl: {}", originUrl);
            String evalSource = String.format("source(\"%s\")", rserveProperty.getConvertFile());
            log.info("evalSource: {}", evalSource);
            String evalConvert = String.format("csvToSav(\"%s\",\"%s\",\"%s\")", originUrl, rserveProperty.getUploadUrl(), rserveProperty.getUploadToken());
            log.info("evalConvert: {}", evalConvert);
            RConnection connection = new RConnection(rserveProperty.getRserveHost(), rserveProperty.getRservePort());
            connection.eval(evalSource);
            REXP rexp = connection.eval(evalConvert);
            String newUrl = rexp.asString();
            log.info("newUrl: {}", newUrl);
            return newUrl;
        } catch (RserveException | REXPMismatchException e) {
            log.error("_csvToSav error", e);
            return originUrl;
        }
    }

}
