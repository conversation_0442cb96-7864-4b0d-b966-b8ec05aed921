package org.befun.extension.doc;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.core.MethodParameter;
import org.springframework.web.method.HandlerMethod;

import java.util.HashMap;
import java.util.Map;

public class OverrideQueryOperation implements OperationCustomizer, OpenApiCustomiser {

    private final Map<Operation, HandlerMethod> overrideOperation1 = new HashMap<>();
    private final Map<Operation, HandlerMethod> overrideOperation2 = new HashMap<>();

    @Override
    public Operation customize(Operation operation, HandlerMethod handlerMethod) {
        // 如果method是的参数是有 ResourceQueryCustom ResourceQueryPredicate 这两个注解，需要替换默认的参数
        MethodParameter[] parameters = handlerMethod.getMethodParameters();
        if (parameters.length > 0) {
            MethodParameter parameter = parameters[0];
            if (parameter.hasParameterAnnotation(ResourceQueryCustom.class)) {
                overrideOperation1.put(operation, handlerMethod);
            } else if (parameter.hasParameterAnnotation(ResourceQueryPredicate.class)) {
                overrideOperation2.put(operation, handlerMethod);
            }
        }
        return operation;
    }

    @Override
    public void customise(OpenAPI openApi) {
        if (!overrideOperation1.isEmpty()) {
            overrideOperation1.forEach((k, v) -> {
                Parameter parameter = k.getParameters().get(0);
                k.getParameters().clear();
                String schemaName = parameter.getSchema().get$ref().replace("#/components/schemas/", "");
                Schema<?> schema = openApi.getComponents().getSchemas().get(schemaName);
                schema.getProperties().forEach((x, y) -> {
                    Parameter override = new Parameter();
                    override.setName(y.getName());
                    override.setIn("query");
                    override.setSchema(y);
                    override.setDescription(y.getDescription());
                    k.getParameters().add(override);
                });
            });

            overrideOperation1.clear();
        }
        if (!overrideOperation2.isEmpty()) {
            overrideOperation2.forEach((k, v) -> {
                Parameter parameter = k.getParameters().get(0);
                k.getParameters().clear();
                String schemaName = parameter.getSchema().get$ref().replace("#/components/schemas/", "");
                Schema<?> schema = openApi.getComponents().getSchemas().get(schemaName);
                schema.getProperties().forEach((x, y) -> {
                    if (x.equals("supportProperty")) {
                        String schemaName2 = y.get$ref().replace("#/components/schemas/", "");
                        Schema<?> schema2 = openApi.getComponents().getSchemas().get(schemaName2);
                        schema2.getProperties().forEach((x2, y2) -> {
                            Parameter override = new Parameter();
                            override.setName(y2.getName());
                            override.setIn("query");
                            override.setSchema(y2);
                            override.setDescription(y2.getDescription());
                            k.getParameters().add(override);
                        });
                    } else {
                        Parameter override = new Parameter();
                        override.setName(y.getName());
                        override.setIn("query");
                        override.setSchema(y);
                        override.setDescription(y.getDescription());
                        k.getParameters().add(override);
                    }

                });
            });
            overrideOperation2.clear();
        }
    }
}
