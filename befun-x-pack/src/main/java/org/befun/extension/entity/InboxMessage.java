package org.befun.extension.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.befun.extension.constant.InboxMessageType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "inbox_message")
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER, enableAdmin = false)
@DtoClass
public class InboxMessage extends EnterpriseOwnerEntity {

    @Column(name = "type")
    @Enumerated
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private InboxMessageType type;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "from_user_id")
    private Long fromUserId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "from_user_name")
    private String fromUserName;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "title")
    private String title;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "description")
    private String description;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "target_url")
    private String targetUrl;

    @Column(name = "read_status", columnDefinition = "bit(1) default 0")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private Boolean readStatus = false;

    @JsonIgnore
    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;


}