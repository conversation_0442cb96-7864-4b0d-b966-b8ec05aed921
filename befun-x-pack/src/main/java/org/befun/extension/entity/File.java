package org.befun.extension.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "file")
public class File extends BaseEntity {

    /* 文件访问地址 */
    @Column(name = "url", columnDefinition = "varchar(255)")
    @JsonView(ResourceViews.Basic.class)
    private String url;

    /* w文件大小 */
    @Column(name = "size", columnDefinition = "bigint(20)")
    @JsonView(ResourceViews.Detail.class)
    private Long size;

    /* 文件名称 */
    @Column(name = "filename", columnDefinition = "varchar(255)")
    @JsonView(ResourceViews.Basic.class)
    private String filename;

    /* 原始文件名 */
    @Column(name = "original_filename", columnDefinition = "varchar(255)")
    @JsonView(ResourceViews.Detail.class)
    private String originalFileName;

    /* 基础路径 */
    @Column(name = "base_path", columnDefinition = "varchar(255)")
    @JsonView(ResourceViews.Basic.class)
    private String basePath;

    /* 路径 */
    @Column(name = "path", columnDefinition = "varchar(255)")
    @JsonView(ResourceViews.Basic.class)
    private String path;

    /* 文件扩展名 */
    @Column(name = "ext", columnDefinition = "varchar(32)")
    @JsonView(ResourceViews.Basic.class)
    private String extension;

    /* 存储平台 */
    @Column(name = "platform", columnDefinition = "varchar(32)")
    @JsonView(ResourceViews.Detail.class)
    private String platform;

    /* 缩略图访问路径 */
    @Column(name = "th_url", columnDefinition = "varchar(256)")
    @JsonView(ResourceViews.Detail.class)
    private String thumbnailUrl;

    /* 缩略图名称 */
    @Column(name = "th_filename", columnDefinition = "varchar(256)")
    @JsonView(ResourceViews.Detail.class)
    private String thumbnailFilename;

    /* 缩略图大小 */
    @Column(name = "th_size", columnDefinition = "bigint(20)")
    @JsonView(ResourceViews.Detail.class)
    private Long thumbnailSize;

}