package org.befun.extension.entity;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Entity
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class LinkExist extends BaseEntity {
    @Column(name = "link_id")
    private Long linkId;

    @Column(name = "hash")
    private String hash;


    public LinkExist(Link link) {
        this.linkId = link.getId();
        this.hash = link.getHash();
    }

    public String getStrHash(String str) {
        // 计算surveyId:params:source:surveyClientPrefix的hash值
//        String str = String.format("%d:%s:%d:%s", surveyId, params, source, surveyClientPrefix);
        try {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(str.getBytes());
        byte[] digest = md.digest();
        BigInteger bi = new BigInteger(1, digest);
        return String.format("%0" + (digest.length << 1) + "x", bi);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
}
