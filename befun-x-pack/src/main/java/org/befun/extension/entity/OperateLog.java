package org.befun.extension.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


@Entity
@Getter
@Setter
@Table(name = "operate_log")
@EntityScopeStrategy(value = EntityScopeStrategyType.ORGANIZATION)
@DtoClass
public class OperateLog extends EnterpriseOwnerEntity {

    @Column(name = "department_ids")
    @Schema(description = "部门id")
    private String departmentIds;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "ip")
    @Schema(description = "ip")
    private String ip;

    @Column(name = "country")
    @Schema(description = "国家")
    private String country;

    @Column(name = "province")
    @Schema(description = "省份")
    private String province;

    @Column(name = "city")
    @Schema(description = "城市")
    private String city;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "module")
    @Schema(description = "操作模块")
    private String module;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "action")
    @Schema(description = "操作内容")
    private String action;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "result")
    @Schema(description = "结果成功/失败")
    private Boolean result;

    @Column(name = "url")
    @Schema(description = "url")
    private String url;

    @Column(name = "params")
    @Schema(description = "请求参数")
    private String params;


    @Column(name = "content")
    @Schema(description = "响应内容")
    private String content;

    @Column(name = "app")
    @Schema(description = "请求来源")
    private String app;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "部门名称")
    private String departmentName;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "地理位置")
    private String location;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "操作人")
    private String userName;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "操作人头像")
    private String avatar;
}