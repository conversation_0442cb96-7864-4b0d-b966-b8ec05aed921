package org.befun.extension.property;


import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = Extensions.RSERVE_PREFIX)
public class RserveProperty {

    private boolean enable;
    private String rserveHost = "rserve-svc";
    private int rservePort = 6311;
    private String convertFile = "/csvToSav.R";
    private String uploadToken = "a146efda-f7f3-4a65-8607-dd7fb58263e5";
    private String uploadUrl = "http://surveylite-backend-svc:5000/api/survey/files-token";
    private int timeoutMs = 30000;
}

