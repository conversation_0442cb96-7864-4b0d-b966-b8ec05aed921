package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.WX_OPEN_PREFIX)
public class WeChatOpenProperty {

    private boolean enable = false;

    private String componentAppId;

    /**
     * 设置微信三方平台的app secret
     */

    private String componentSecret;

    /**
     * 设置微信三方平台的token
     */

    private String componentToken;

    /**
     * 设置微信三方平台的EncodingAESKey
     */

    private String componentAesKey;

    /**
     * 设置微信三方平台的授权回调地址
     */

    private String authorizeCallbackUrl;

    /**
     * 全网发布时的，测试公众号信息
     */
    private Map<String, String> publishTestAccount = new HashMap<>();

    /**
     * 代理服务器信息
     */
    private boolean httpProxyEnable;
    private String httpProxyHost;
    private int httpProxyPort;
    private String httpProxyUsername;
    private String httpProxyPassword;

}