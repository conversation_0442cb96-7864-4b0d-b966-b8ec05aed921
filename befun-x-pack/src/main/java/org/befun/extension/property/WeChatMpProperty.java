package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.WX_MP_PREFIX)
public class WeChatMpProperty {

    private boolean enable = false;

    private String prefix = "befun";

    /* auth source appId */
    private String appId;

    /* client secret */
    private String appSecret;

    /* client token */
    private String token;

    /* redirect aesKey */
    private String aesKey;

}