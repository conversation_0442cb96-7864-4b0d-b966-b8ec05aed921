package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.WX_PAY_PREFIX)
public class WeChatPayProperty {

    private boolean enable = false;
    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户密钥
     */
    private String v2MchKey;

    /**
     * 微信支付商户密钥
     */
    private String v3MchKey;
    /**
     * p12证书路径
     */
    private String certP12Path;
    /**
     * 私钥路径
     */
    private String privateKeyPath;

    /**
     * 公钥路径
     */
    private String certPemPath;

    /**
     * 是否使用沙盒模式
     */
    private boolean useSandbox;

    /**
     * 支付回调地址
     */
    private String payNotifyUrl;

    /**
     * 支付退款回调地址
     */
    private String payRefundNotifyUrl;
}