package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AlipayConfig {

    private String alipayServerUrl = "https://openapi.alipay.com/gateway.do";
    private String alipayPublicKey;

    private String appId;
    private String appPrivateKey;
    private String appPublicKey;
    private String signType = "RSA2";

    private String format = "json";
    private String charset = "utf-8";

    private String encryptKey;
    private String encryptType = "AES";

    private String payMethod = "get";
    /**
     * 支付回调地址
     */
    private String payNotifyUrl;

}

