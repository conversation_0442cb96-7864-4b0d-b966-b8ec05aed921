package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = Extensions.MAIL_PREFIX)
@Getter
@Setter
public class MailProperty {

    private boolean enable = true;

    /* protocol */
    private String protocol = "smtp";

    /* smtp host */
    private String host;

    /* smtp port */
    private int port;

    /* smtp username */
    private String username;

    /* from user */
    private String from;

    /* smtp password */
    private String password;

    /* enable debug */
    private Boolean enableDebug = false;

    /* enable debug */
    private Boolean enableAuth = true;

    /* enable debug */
    private Boolean enableStartTls = true;

    @NestedConfigurationProperty
    private List<MailConfigProperty> configs = new ArrayList<>();

    @NestedConfigurationProperty
    private List<MailTemplateProperty> templates = new ArrayList<>();
}