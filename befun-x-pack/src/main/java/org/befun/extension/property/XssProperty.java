package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.extension.Extensions;
import org.befun.extension.constant.SafeListType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.befun.extension.constant.SafeListType.basicWithImages;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.XSS_PREFIX)
public class XssProperty {

    private SafeListType type = basicWithImages;
    private Map<String, Map<String, List<String>>> extraTags = new HashMap<>();

    private String whiteList;
    private String blackList;

    public ArrayList<String> getWhiteList() {
        if (StringUtils.isEmpty(whiteList)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(List.of(whiteList.split(",")));
    }

    public ArrayList<String> getBlackList() {
        if (StringUtils.isEmpty(blackList)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(List.of(blackList.split(",")));
    }

}
