package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = Extensions.SHORT_URL_PREFIX)
@Getter
@Setter
public class LinkProperty {
    private String root;
    private String surveyClientPrefix;
}
