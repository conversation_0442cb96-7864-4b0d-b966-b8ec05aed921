package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.ALIPAY_PREFIX)
public class AlipayProperty extends AlipayConfig {

    private boolean enable = false;

    /**
     * 是否使用沙盒模式
     */
    private boolean useSandbox;
    /**
     * 沙盒模式的配置
     */
    private AlipayConfig sandbox;

}

