package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = Extensions.INBOX_MESSAGE_PREFIX)
@Getter
@Setter
public class InboxMesasgeProperty {
    private boolean enable = true;

    @NestedConfigurationProperty
    private Map<String, InboxMesasgeTemplateProperty> templates = new HashMap<>();

    @Getter
    @Setter
    public static class InboxMesasgeTemplateProperty {
        private String name;
        private InboxMessageType type;
        private String title;
        private String url;
    }
}