package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.SMART_VERIFY_PREFIX)
public class SmartVerifyProperty {
    private String regionId;
    private String accessKeyId;
    private String accessKeySecret;
    private String product;
    private String domain;
    private String appKey;
}
