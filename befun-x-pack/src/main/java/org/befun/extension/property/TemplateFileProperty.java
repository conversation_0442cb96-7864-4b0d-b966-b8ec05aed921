package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@Getter
@Setter
@ConfigurationProperties(prefix = Extensions.TEMPLATE_FILE_PREFIX)
public class TemplateFileProperty {

    private boolean enable = false;

    @NestedConfigurationProperty
    private List<TemplateFileItem> items = new ArrayList<>();

    @Getter
    @Setter
    public static class TemplateFileItem {
        private String type;
        private String fileUrl;
    }
}