package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = Extensions.SMS_PREFIX)
@Getter
@Setter
public class SmsProperty {
    private boolean enable = true;
    private String vendor = "feige";
    private boolean enableFeige = true;
    private boolean enableAliyun = false;
    private boolean enableSocket = false;
    private boolean enableActivemq = false;
    private boolean enableChuanglan = false;

    @NestedConfigurationProperty
    private List<SmsProviderProperty> providers = new ArrayList<>();
}