package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SmsProviderProperty {
    /* provider name，指定ISmsProvider实现类 */
    private String name;

    // 飞鸽专用参数，重写get方法，保证之前的配置生效
    private SmsFeige feige;

    // 飞鸽专用参数，重写get方法，保证之前的配置生效
    private SmsChuanglan chuanglan;

    // 阿里云专用参数
    private SmsAliyun aliyun;

    // socket 专用参数
    private SmsSocket socket;

    // http 专用参数
    private SmsHttp http;

    // activemq 专用参数
    private SmsActivemq activemq;

    private List<SmsTemplateProperty> templates = new ArrayList<>();

    public String globalRealSignature() {
        if ("feige".equals(name)) {
            return feige != null ? feige.getRealSignature() : null;
        } else if ("aliyun".equals(name)) {
            return aliyun != null ? aliyun.getRealSignature() : null;
        } else if ("socket".equals(name)) {
            return socket != null ? socket.getRealSignature() : null;
        } else if ("http".equals(name)) {
            return http != null ? http.getRealSignature() : null;
        } else if ("activemq".equals(name)) {
            return activemq != null ? activemq.getRealSignature() : null;
        } else if ("chuanglan".equals(name)) {
            return chuanglan != null ? chuanglan.getRealSignature() : null;
        } else {
            return null;
        }
    }

    @Getter
    @Setter
    public static abstract class SmsProviderConfig {
        private String realSignature;
    }

    @Getter
    @Setter
    public static class SmsFeige extends SmsProviderConfig {
        private String appId;
        private String appSecret;
        private String signature;
    }

    @Getter
    @Setter
    public static class SmsChuanglan extends SmsProviderConfig {
        private String appId;
        private String appSecret;
        private String signature;
        private String accountCheckAppId;
        private String accountCheckAppKey;
    }

    @Getter
    @Setter
    public static class SmsAliyun extends SmsProviderConfig {
        private String accessKey;
        private String secret;
        private String signature;
        private String region;

        @Override
        public String getRealSignature() {
            return StringUtils.isEmpty(super.getRealSignature()) ? signature : super.getRealSignature();
        }
    }

    @Getter
    @Setter
    public static class SmsHttp extends SmsProviderConfig {
        private String url;
        // 额外的参数
        private Map<String, String> extParams = new HashMap<>();
    }

    @Getter
    @Setter
    public static class SmsActivemq extends SmsProviderConfig {
        private String queue;
        private boolean sendAndReceive = false;
        private String dateTimeFormatter = "yyyyMMddHHmmss";
        private String sendText = "${content}";
        // 额外的参数
        private Map<String, String> extParams = new HashMap<>();
    }

    @Getter
    @Setter
    public static class SmsSocket extends SmsProviderConfig {
        private String host = "127.0.0.1";
        private int port = 9999;
        private int maxConnects = 10;
        private int connectWaitMs = 10000;
        private String dateFormatter = "yyyyMMdd";
        private String timeFormatter = "HHmmss";
        private String senderName = "OnceSocketSmsSender";
        private String sendText = "${serverCode}${date}${time}${id}${mobile}${content}";

        // 额外的参数
        private Map<String, String> extParams = new HashMap<>();
    }

    public void replacePlaceholder() {
        if (socket != null) {
            socket.sendText = replacePlaceholder(socket.sendText);
        }
        if (activemq != null) {
            activemq.sendText = replacePlaceholder(activemq.sendText);
        }
    }

    private String replacePlaceholder(String s) {
        if (StringUtils.isNotEmpty(s)) {
            return s.replace("#{", "${");
        }
        return s;
    }

}
