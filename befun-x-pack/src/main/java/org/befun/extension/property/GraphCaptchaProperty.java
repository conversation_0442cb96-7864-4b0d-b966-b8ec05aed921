package org.befun.extension.property;


import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = Extensions.GRAPH_CAPTCHA_PREFIX)
public class GraphCaptchaProperty {

    private boolean enable;
    private long expireSeconds = 300;
    private Simple simple = new Simple();

    @Getter
    @Setter
    public static class Simple {
        // 默认不忽略大小写
        private boolean ignoreCase = false;
        // 验证码支持的字符
        private String chars = "";
        // 字符数量
        private int size = 4;
        // 默认干扰线数量
        private int lines = 10;
        // 默认宽度
        private int width = 80;
        // 默认高度
        private int height = 35;
        // 默认字体大小
        private int fontSize = 25;
        // 默认字体倾斜
        private boolean tilt = true;
        // 背景色
        private String backgroundColor = "#c0c0c0";
    }
}

