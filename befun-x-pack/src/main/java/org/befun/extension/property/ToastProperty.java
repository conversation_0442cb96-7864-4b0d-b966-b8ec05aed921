package org.befun.extension.property;


import lombok.Getter;
import lombok.Setter;
import org.befun.extension.Extensions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.TOAST_MESSAGE_PREFIX)
public class ToastProperty {

    private boolean enable = true;
    private Map<String, String> messages = new HashMap<>();
}
