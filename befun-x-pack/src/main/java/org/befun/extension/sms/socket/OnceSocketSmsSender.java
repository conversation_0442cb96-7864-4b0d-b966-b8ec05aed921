package org.befun.extension.sms.socket;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.extension.property.SmsProviderProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable","enable-socket"}, havingValue = "true")
public class OnceSocketSmsSender implements SocketSmsSender {

    private EventLoopGroup workerGroup;
    private SmsProviderProperty.SmsSocket config;
    private Semaphore semaphore;

    @Override
    public void init(SmsProviderProperty.SmsSocket config) {
        this.config = config;
        workerGroup = new NioEventLoopGroup();
        semaphore = new Semaphore(config.getMaxConnects());
    }

    @Override
    public void destroy() {
        workerGroup.shutdownGracefully();
    }

    @Override
    public String send(String mobile, String content) {
        try {
            if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(content)) {
                return null;
            }
            if (!semaphore.tryAcquire(config.getConnectWaitMs(), TimeUnit.MILLISECONDS)) {
                return null;
            }
            OnceSendHandler handler = new OnceSendHandler(mobile, content);

            Bootstrap b = new Bootstrap(); // (1)
            b.group(workerGroup); // (2)
            b.channel(NioSocketChannel.class); // (3)
            b.handler(new ChannelInitializer<SocketChannel>() {
                @Override
                public void initChannel(SocketChannel ch) throws Exception {
                    ch.pipeline()
                            .addLast(new StringDecoder())
                            .addLast(new StringEncoder())
                            .addLast(handler);
                }
            });

            // Start the client.
            ChannelFuture f = b.connect(config.getHost(), config.getPort()).sync(); // (5)

            // Wait until the connection is closed.
            f.channel().closeFuture().sync();
            return handler.response;
        } catch (InterruptedException e) {
            log.error("socket 短信发送失败", e);
        } finally {
            semaphore.release();
        }
        return null;
    }

    public static class OnceSendHandler extends ChannelInboundHandlerAdapter {

        private final String mobile;
        private final String content;
        private String response;

        public OnceSendHandler(String mobile, String content) {
            this.mobile = mobile;
            this.content = content;
        }

        @Override
        public void channelActive(ChannelHandlerContext ctx) {
            log.debug("开始发送socket短信：mobile={}, content={}", mobile, content);
            ctx.writeAndFlush(content);
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) {
            this.response = (String) msg;
            log.debug("socket短信返回信息：mobile={}, content={}, response={}", mobile, content, response);
        }

        @Override
        public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
            ctx.close();//关闭连接
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            log.error("socket短信发送失败, mobile={}, content={}", mobile, content, cause);
            ctx.close();
        }
    }
}
