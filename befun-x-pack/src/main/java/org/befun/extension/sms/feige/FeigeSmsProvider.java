package org.befun.extension.sms.feige;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.sms.BaseSmsProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Component("feige")
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-feige"}, havingValue = "true", matchIfMissing = true)
public class FeigeSmsProvider extends BaseSmsProvider<SmsProviderProperty.SmsFeige> {

    private final static String URL = "http://api.feige.ee/SmsService";
    private final static String URL_TEMPLATE = "https://api.4321.sh/sms/template";
    private final static String URL_TEXT = "https://api.4321.sh/sms/send";

    @Override
    public void init(SmsProviderProperty config) {
        SmsProviderProperty.SmsFeige feige = requireConfig(config);
        String globalSignature = feige.getSignature();
        String globalRealSignature = feige.getRealSignature();
        config.getTemplates().forEach(t -> {
            if (StringUtils.isEmpty(t.getSignature())) {
                t.setSignature(globalSignature);
            }
            if (StringUtils.isEmpty(t.getRealSignature())) {
                t.setRealSignature(globalRealSignature);
            }
        });
        super.init(config);
    }

    @Override
    public SmsProviderProperty.SmsFeige requireConfig(SmsProviderProperty config) {
        Assert.notNull(config, "飞鸽短信服务配置不能为空");
        Assert.notNull(config.getFeige(), "飞鸽短信服务配置不能为空");
        return config.getFeige();
    }

    @Override
    public XPackAppType getTemplateConfigType() {
        return XPackAppType.SMS_TEMPLATE_FEIGE;
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        SmsProviderProperty.SmsFeige feige = requireConfig(config);
        if (!NumberUtils.isDigits(info.getTemplateId()) || CollectionUtils.isEmpty(info.getVariableValues())) {
            SmsNotifyTextInfo textInfo = new SmsNotifyTextInfo(info.getMobile(), info.getContent(), info.getParams());
            boolean success = sendMessageByText(config, textInfo);
            info.setResponse(textInfo.getResponse());
            info.setRealCost(textInfo.getRealCost());
            return success;
        }
        log.info("send to feige {} template:{}", info.getMobile(), info.getTemplateId());
        try {
            int signId = NumberUtils.isDigits(info.getSignature()) ? Integer.parseInt(info.getSignature()) : NumberUtils.isDigits(feige.getSignature()) ? Integer.parseInt(feige.getSignature()) : 0;
            int templateId = NumberUtils.isDigits(info.getTemplateId()) ? Integer.parseInt(info.getTemplateId()) : 0;
            if (signId == 0 || templateId == 0) {
                log.error("failed to send feige signId:{}, templateId:{}", signId, templateId);
                return false;
            }

            Map<String, Object> data = new HashMap<>();
            data.put("apikey", feige.getAppId());
            data.put("secret", feige.getAppSecret());
            data.put("sign_id", signId);
            data.put("template_id", templateId);
            data.put("content", info.getVariableValues().stream().map(SmsNotifyInfo.TemplateVariableValue::getValue).filter(Objects::nonNull).collect(Collectors.joining("||")));
            data.put("mobile", info.getMobile());
            String body = JsonHelper.toJson(data);
            log.debug(body);
            String response = Request.Post(URL_TEMPLATE)
                    .connectTimeout(1000 * 30)
                    .socketTimeout(1000 * 30)
                    .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                    .execute().returnContent().asString();
            info.setResponse(response);
            log.debug(response);
            FeigeResponse2 feigeResponse = JsonHelper.toObject(response, FeigeResponse2.class);
            if (feigeResponse.getCode() == 0) {
                info.setRealCost(feigeResponse.getCount());
                info.setThirdpartyMessageId(feigeResponse.getMsgNo());
                log.info("has sent to feige {}", feigeResponse.getMsgNo());
                return true;
            } else {
                log.error("failed to send feige code:{} message:{}", feigeResponse.getCode(), feigeResponse.getMsg());
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean sendMessageByText(SmsProviderProperty config, SmsNotifyTextInfo info) {
        SmsProviderProperty.SmsFeige feige = requireConfig(config);
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("apikey", feige.getAppId());
            data.put("secret", feige.getAppSecret());
            data.put("sign_id", NumberUtils.isDigits(feige.getSignature()) ? Integer.parseInt(feige.getSignature()) : 0);
            data.put("content", info.getContent());
            data.put("mobile", info.getMobile());
            String body = JsonHelper.toJson(data);
            log.debug(body);
            String response = Request.Post(URL_TEXT)
                    .connectTimeout(1000 * 30)
                    .socketTimeout(1000 * 30)
                    .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                    .execute().returnContent().asString();
            info.setResponse(response);
            log.debug(response);
            FeigeResponse2 feigeResponse = JsonHelper.toObject(response, FeigeResponse2.class);
            if (feigeResponse.getCode() == 0) {
                info.setRealCost(feigeResponse.getCount());
                log.info("has sent to feige {}", feigeResponse.getMsgNo());
                return true;
            } else {
                log.error("failed to send feige code:{} message:{}", feigeResponse.getCode(), feigeResponse.getMsg());
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }
}

