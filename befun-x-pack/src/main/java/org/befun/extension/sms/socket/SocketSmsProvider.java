package org.befun.extension.sms.socket;

import lombok.extern.slf4j.Slf4j;
import org.befun.extension.dto.SmsNotifyBaseInfo;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.sms.BaseSmsProvider;
import org.befun.extension.sms.ISmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PreDestroy;
import java.util.*;

/**
 * 如果需要自定义发送内容，则需要实现 {@link SocketSmsParser}接口，并注册到spring容器
 * <p>
 * 使用 socket 发送短信需要以下配置
 * befun:
 * extension:
 * sms:
 * vendor: socket
 * enable-socket: true
 * providers:
 * - name: socket
 * socket:
 * host: 127.0.0.1
 * port: 9999
 * max-connects: 10
 * connect-wait-ms: 10000
 * sender-name: OnceSocketSmsSender
 * templates:
 * - name: default
 * id: 116175
 * signature: ${FEIGE_SIGNATURE:349733}
 * content: 尊敬的用户，已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！
 */
@Slf4j
@Component("socket")
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-socket"}, havingValue = "true")
public class SocketSmsProvider extends BaseSmsProvider<SmsProviderProperty.SmsSocket> {

    @Autowired(required = false)
    private SocketSmsParser socketSmsParser;
    @Autowired(required = false)
    private List<SocketSmsSender> socketSmsSenders;
    private SocketSmsSender socketSmsSender;

    public void init(SmsProviderProperty config) {
        SmsProviderProperty.SmsSocket socket = requireConfig(config);
        socketSmsSender = Optional.ofNullable(socketSmsSenders)
                .stream()
                .flatMap(Collection::stream)
                .filter(i -> socket.getSenderName().equals(i.name()))
                .findFirst()
                .orElse(null);
        if (socketSmsSender == null) {
            throw new RuntimeException("socket短信服务配置错误");
        }
        socketSmsSender.init(socket);
        if (socketSmsParser == null) {
            socketSmsParser = new DefaultSocketSmsParser(socket);
        }
        super.init(config);
    }

    @Override
    public SmsProviderProperty.SmsSocket requireConfig(SmsProviderProperty config) {
        Assert.notNull(config, "socket短信服务配置不能为空");
        Assert.notNull(config.getSocket(), "socket短信服务配置不能为空");
        return config.getSocket();
    }

    @Override
    public boolean sendMessageByText(SmsProviderProperty config, SmsNotifyTextInfo info) {
        return send0(config, info.getMobile(), info.getContent(), info.getParams(), info);
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        return send0(config, info.getMobile(), info.getContent(), info.getParams(), info);
    }

    private boolean send0(SmsProviderProperty config, String mobile, String content, Map<String, Object> params, SmsNotifyBaseInfo info) {
        String id = UUID.randomUUID().toString().replace("-", "");
        SmsProviderProperty.SmsSocket socket = requireConfig(config);
        String response = socketSmsSender.send(mobile, socketSmsParser.buildContent(socket, id, mobile, content, params));
        info.setResponse(response);
        return socketSmsParser.parseResponse(response);
    }

    @PreDestroy
    public void destroy() {
        if (socketSmsSender != null) {
            socketSmsSender.destroy();
        }
    }

}

