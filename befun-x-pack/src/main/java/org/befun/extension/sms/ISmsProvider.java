package org.befun.extension.sms;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.extension.constant.AccountSafetyType;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.property.SmsTemplateProperty;

import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
public interface ISmsProvider<CONFIG extends SmsProviderProperty.SmsProviderConfig> {

    Map<String, SmsTemplateProperty> getTemplatePropertyMap();

    void init(SmsProviderProperty config);

    CONFIG requireConfig(SmsProviderProperty config);

    default XPackAppType getTemplateConfigType() {
        throw new NotImplementedException();
    }

    /**
     * 使用短信模板 发送短信
     */
    default boolean sendMessageByTemplate(SmsProviderProperty providerConfiguration, SmsNotifyInfo info) {
        throw new NotImplementedException();
    }

    /**
     * 直接发送短信内容
     */
    default boolean sendMessageByText(SmsProviderProperty providerConfiguration, SmsNotifyTextInfo info) {
        throw new NotImplementedException();
    }

    /**
     * 检测手机号信用等级
     */
    default AccountSafetyType checkAccount(SmsProviderProperty config, String mobile, String ip) {
        throw new NotImplementedException();
    }

    public static String key1(String thirdpartyMessageType, String thirdPartyMessageId) {
        return String.format("sms-status:%s:%s", thirdpartyMessageType, thirdPartyMessageId);
    }

    public static String value1(String fromType, Long fromId) {
        return String.format("%s:%d", fromType, fromId);
    }

    public static String key2(String fromType, Long fromId) {
        return String.format("sms-status:%s:%d", fromType, fromId);
    }

    public static String value2(String thirdpartyMessageType, String thirdPartyMessageId) {
        return String.format("%s:%s", thirdpartyMessageType, thirdPartyMessageId);
    }


}