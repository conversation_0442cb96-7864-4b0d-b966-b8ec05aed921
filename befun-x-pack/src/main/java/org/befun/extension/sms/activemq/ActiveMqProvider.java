package org.befun.extension.sms.activemq;

import lombok.extern.slf4j.Slf4j;
import org.befun.extension.dto.SmsNotifyBaseInfo;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.sms.BaseSmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.jms.JMSException;
import javax.jms.Message;
import java.util.Map;
import java.util.UUID;

/**
 * 1 如果需要自定义发送内容，则需要实现 {@link ActivemqSmsParser}接口，并注册到spring容器
 * <p>
 * 2 添加以下依赖，当前使用的<scope>provided</scope>不会传递到依赖的项目中
 * <dependency>
 * <groupId>org.springframework.boot</groupId>
 * <artifactId> spring-boot-starter-activemq</artifactId>
 * </dependency>
 * <p>
 * 3 使用 activemq 发送短信需要以下配置
 * spring:
 * activemq:
 * broker-url: "tcp://127.0.0.1:61616"
 * user: "admin"
 * password: "admin"
 * send-timeout: 10000
 * befun:
 * extension:
 * sms:
 * vendor: activemq
 * enable-activemq: true
 * providers:
 * - name: activemq
 * activemq:
 * queue: test-sms
 * send-and-receive: false
 * templates:
 * - name: default
 * id: 116175
 * signature: ${FEIGE_SIGNATURE:349733}
 * content: 尊敬的用户，已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！
 */
@Slf4j
@Component("activemq")
@ConditionalOnClass(JmsTemplate.class)
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-activemq"}, havingValue = "true")
public class ActiveMqProvider extends BaseSmsProvider<SmsProviderProperty.SmsActivemq> {

    @Autowired(required = false)
    private JmsTemplate jmsTemplate;
    @Autowired(required = false)
    private ActivemqSmsParser activemqSmsParser;

    @Override
    public void init(SmsProviderProperty config) {
        SmsProviderProperty.SmsActivemq activemq = requireConfig(config);
        Assert.notNull(jmsTemplate, "ActiveMQ短信服务未配置");
        if (activemqSmsParser == null) {
            activemqSmsParser = new DefaultActivemqSmsParser(activemq);
        }
        super.init(config);
    }

    @Override
    public SmsProviderProperty.SmsActivemq requireConfig(SmsProviderProperty config) {
        Assert.notNull(config, "ActiveMQ短信服务配置不能为空");
        Assert.notNull(config.getActivemq(), "ActiveMQ短信服务配置不能为空");
        return config.getActivemq();
    }

    @Override
    public boolean sendMessageByText(SmsProviderProperty config, SmsNotifyTextInfo info) {
        return send0(config, info.getMobile(), info.getContent(), info.getParams(), info);
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        return send0(config, info.getMobile(), info.getContent(), info.getParams(), info);
    }

    private boolean send0(SmsProviderProperty config, String mobile, String content, Map<String, Object> params, SmsNotifyBaseInfo info) {
        String id = UUID.randomUUID().toString().replace("-", "");
        SmsProviderProperty.SmsActivemq activemq = requireConfig(config);
        String smsContent = activemqSmsParser.buildContent(activemq, id, mobile, content, params);
        log.debug("开始发送activemq短信：mobile={}, content={}", mobile, smsContent);
        MessageCreator messageCreator = session -> session.createTextMessage(smsContent);
        if (activemq.isSendAndReceive()) {
            Message message = jmsTemplate.sendAndReceive(activemq.getQueue(), messageCreator);
            String responseId;
            try {
                responseId = message != null ? message.getJMSMessageID() : null;
            } catch (JMSException e) {
                responseId = null;
            }
            info.setResponse(responseId);
            log.debug("activemq短信返回信息：mobile={}, content={}, response={}", mobile, content, responseId);
            return activemqSmsParser.parseResponse(message);
        } else {
            jmsTemplate.send(activemq.getQueue(), messageCreator);
            log.debug("activemq短信返回信息：mobile={}, content={}, response=[未查询发送结果]", mobile, content);
            // 不查询发送结果，如果发送成功则直接返回成功
            info.setResponse("发送完毕，未查询发送结果");
            return true;
        }
    }
}
