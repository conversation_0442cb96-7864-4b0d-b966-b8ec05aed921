package org.befun.extension.sms.feige;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class FeigeResponse2 {

    @JsonProperty("code")
    private int code;

    @JsonProperty("msg")
    private String msg;

    @JsonProperty("count")
    private int count;

    @JsonProperty("msg_no")
    private String msgNo;

}