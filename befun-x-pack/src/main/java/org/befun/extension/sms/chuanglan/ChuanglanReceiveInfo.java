package org.befun.extension.sms.chuanglan;

import lombok.Getter;
import lombok.Setter;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@Getter
@Setter
public class ChuanglanReceiveInfo {
    private String receiver;    //接收验证的用户名，配置时不填写则为空
    private String pswd;        //接收验证的密码，配置时不填则为空
    private String msgId;       //消息id
    private String reportTime;  //运营商返回的状态更新时间，格式YYMMddHHmm，其中YY=年份的最后两位（00-99）
    private String mobile;      //接收短信的手机号码
    private String status;      //运营商返回的状态（详情请前往 code.253.com 查看）
    private String statusDesc;  //状态说明，内容经过URLEncode编码(UTF-8)
    private String notifyTime;  //253平台收到运营商回复状态报告的时间，格式yyMMddHHmmss
    private String uid;         //该条短信在您业务系统内的ID，如订单号或者短信发送记录流水号
    private Integer length;     //下发短信计费条数

    public boolean isSuccess() {
        return "DELIVRD".equalsIgnoreCase(status);
    }

    public String message() {
        if (statusDesc != null) {
            return URLDecoder.decode(statusDesc, StandardCharsets.UTF_8);
        }
        return null;
    }
}
