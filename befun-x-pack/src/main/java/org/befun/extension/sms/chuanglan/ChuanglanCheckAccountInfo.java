package org.befun.extension.sms.chuanglan;

import lombok.Getter;
import lombok.Setter;
import org.befun.extension.constant.AccountSafetyType;

@Getter
@Setter
public class ChuanglanCheckAccountInfo {
    private String code;
    private String message;
    private String chargeStatus;
    private ChuanglanCheckAccountDataInfo data;

    public AccountSafetyType mapTo() {
        if (data != null) {
            switch (data.status) {
                case "W1" -> {
                    return AccountSafetyType.WHITELIST;
                }
                case "B1" -> {
                    return AccountSafetyType.BLACKLIST;
                }
                case "B2" -> {
                    return AccountSafetyType.DUBIOUS;
                }
                case "N" -> {
                    return AccountSafetyType.NONE;
                }
            }
        }
        return null;
    }

    @Getter
    @Setter
    public static class ChuanglanCheckAccountDataInfo {
        private String mobile;
        private String tradeNo;
        private String status;
        private String tag;
    }

}
