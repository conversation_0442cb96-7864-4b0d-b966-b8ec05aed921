package org.befun.extension.sms.activemq;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.befun.core.template.TemplateEngine;
import org.befun.extension.property.SmsProviderProperty;

import javax.jms.Message;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DefaultActivemqSmsParser implements ActivemqSmsParser {

    private final DateTimeFormatter dateTimeFormatter;

    public DefaultActivemqSmsParser(SmsProviderProperty.SmsActivemq activemq) {
        dateTimeFormatter = DateTimeFormatter.ofPattern(activemq.getDateTimeFormatter());
    }

    @Override
    public String buildContent(SmsProviderProperty.SmsActivemq config, String id, String mobile, String content, Map<String, Object> params) {
        Map<String, Object> p = new HashMap<>();
        if (MapUtils.isNotEmpty(config.getExtParams())) {
            p.putAll(config.getExtParams());
        }
        if (MapUtils.isNotEmpty(params)) {
            p.putAll(params);
        }
        p.put("datetime", dateTimeFormatter.format(LocalDateTime.now().atZone(ZoneId.systemDefault())));
        p.put("mobile", mobile);
        p.put("id", id);
        p.put("content", content);
        String value = TemplateEngine.renderTextTemplateSimple(config.getSendText(), p);
        log.debug("activemq sms content={}", value);
        return value;
    }

    @Override
    public boolean parseResponse(Message response) {
        return response != null; // todo
    }
}
