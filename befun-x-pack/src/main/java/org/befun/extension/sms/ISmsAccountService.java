package org.befun.extension.sms;

import lombok.Getter;

import java.util.Arrays;
import java.util.function.Consumer;

public interface ISmsAccountService {

    /**
     * 充值
     */
    default int recharge(Long orgId, int amount) {
        return Integer.MAX_VALUE;
    }

    /**
     * 通过字数计算短信条数
     */
    default int calcNumberByText(String text) {
        return 0;
    }

    /**
     * 通过长度计算短信条数
     */
    default int calcNumberByLength(int smsLength) {
        return 0;
    }

    /**
     * 查询真实余额
     */
    default int balance(Long orgId) {
        return Integer.MAX_VALUE;
    }

    /**
     * 是否有额度
     */
    default boolean hasBalance(Long orgId, Integer cost) {
        return true;
    }

    /**
     * 使用额度，可能会导致余额小于0，
     */
    default int consumer(Long orgId, Integer cost) {
        return Integer.MAX_VALUE;
    }

    default int syncToDb(Long orgId) {
        return Integer.MAX_VALUE;
    }

    default boolean hasBalance(Long orgId, Long taskProgressId, int cost) {
        return true;
    }

    /**
     * zset
     * key                          value       score
     * sms.task.{orgId}.{taskId}    {jobId}     {status.i}+{planCost*100}+{realCost}
     */
    default void buildSmsTask(Long orgId, Long userId, Long taskProgressId, String templateContent, Consumer<Integer> progress) {
        progress.accept(0);
    }

    /**
     * 添加短信发送任务
     *
     * @param planSmsCost 预计消费的短信数量，通过模板计算出来的
     */
    default void addSmsTask(Long orgId, Long taskProgressId, Long jobId, int planSmsCost) {
    }

    /**
     * 短信发送成功，修改状态和真实消费短信数量
     *
     * @param realSmsCost 真实消费的短信数量，发送时计算出来的
     */
    default void successSmsTask(Long orgId, Long userId, Long taskProgressId, Long jobId, int realSmsCost) {
    }

    /**
     * 短信发送失败，修改状态和真实消费短信数量0
     */
    default void failureSmsTask(Long orgId, Long userId, Long taskProgressId, Long jobId) {
    }

    /**
     * 短信发送取消，修改状态
     */
    default void cancelSmsTask(Long orgId, Long userId, Long taskProgressId) {
    }

    @Getter
    enum SmsTaskStatus {
        init(10000, 10000, 19999), success(20000, 20000, 29999), failure(30000, 30000, 39999), cancel(40000, 40000, 49999);
        private final int i;
        private final int min;
        private final int max;

        SmsTaskStatus(int i, int min, int max) {
            this.i = i;
            this.min = min;
            this.max = max;
        }

        public static SmsTaskStatus parseByI(int i) {
            final int ii = i / 10000 * 10000;
            return Arrays.stream(values()).filter(o -> o.i == ii).findFirst().orElse(null);
        }
    }

    default void addSmsRecordBySource(Long orgId, Long userId, String source, String content, Integer cost,Integer balance){

    }
}
