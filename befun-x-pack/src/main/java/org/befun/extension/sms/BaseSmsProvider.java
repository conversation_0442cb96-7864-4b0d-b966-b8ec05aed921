package org.befun.extension.sms;

import lombok.Getter;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.property.SmsTemplateProperty;

import java.util.HashMap;
import java.util.Map;


public abstract class BaseSmsProvider<CONFIG extends SmsProviderProperty.SmsProviderConfig> implements ISmsProvider<CONFIG> {

    @Getter
    private Map<String, SmsTemplateProperty> templatePropertyMap = new HashMap<>();

    @Override
    public void init(SmsProviderProperty config) {
        config.getTemplates().forEach(i -> templatePropertyMap.put(i.getName(), i));
    }
}