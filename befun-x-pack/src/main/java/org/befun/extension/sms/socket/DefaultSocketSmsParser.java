package org.befun.extension.sms.socket;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.befun.core.template.TemplateEngine;
import org.befun.extension.property.SmsProviderProperty;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DefaultSocketSmsParser implements SocketSmsParser {

    private final DateTimeFormatter dateFormatter;
    private final DateTimeFormatter timeFormatter;

    public DefaultSocketSmsParser(SmsProviderProperty.SmsSocket socket) {
        dateFormatter = DateTimeFormatter.ofPattern(socket.getDateFormatter());
        timeFormatter = DateTimeFormatter.ofPattern(socket.getTimeFormatter());
    }

    @Override
    public String buildContent(SmsProviderProperty.SmsSocket config, String id, String mobile, String content, Map<String, Object> params) {
//        LocalDateTime now = LocalDateTime.now();
//        Map<String, String> extParams = config.getExtParams();
//        String length;                                      // 4
//        String serverCode = extParams.get("serverCode");    // 10
//        String date = dateFormatter.format(now);            // 8
//        String time = timeFormatter.format(now);            // 6
////        id                                                // 36
////        mobile;                                           // 11
////        content;                                          // x
//        String value = serverCode + date + time + id + mobile + content;
        Map<String, Object> p = new HashMap<>();
        if (MapUtils.isNotEmpty(config.getExtParams())) {
            p.putAll(config.getExtParams());
        }
        if (MapUtils.isNotEmpty(params)) {
            p.putAll(params);
        }
        LocalDateTime now = LocalDateTime.now();
        p.put("date", dateFormatter.format(now));
        p.put("time", timeFormatter.format(now));
        p.put("mobile", mobile);
        p.put("id", id);
        p.put("content", content);
        String value = TemplateEngine.renderTextTemplateSimple(config.getSendText(), p);
        String length = String.format("%04d", value.getBytes().length + 4);
        value = length + value;
        log.debug("socket sms content={}", value);
        return value;
    }

    @Override
    public boolean parseResponse(String response) {
        return response != null; // todo
    }
}
