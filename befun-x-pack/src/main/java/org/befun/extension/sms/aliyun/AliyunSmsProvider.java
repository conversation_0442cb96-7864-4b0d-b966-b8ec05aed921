package org.befun.extension.sms.aliyun;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.profile.DefaultProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.sms.BaseSmsProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;


@Slf4j
@Component("aliyun")
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-aliyun"}, havingValue = "true")
public class AliyunSmsProvider extends BaseSmsProvider<SmsProviderProperty.SmsAliyun> {

    private IAcsClient client;

    public void init(SmsProviderProperty config) {
        SmsProviderProperty.SmsAliyun aliyun = requireConfig(config);
        DefaultProfile profile = DefaultProfile.getProfile(aliyun.getRegion(), aliyun.getAccessKey(), aliyun.getSignature());
        client = new DefaultAcsClient(profile);
        super.init(config);
    }

    @Override
    public SmsProviderProperty.SmsAliyun requireConfig(SmsProviderProperty config) {
        Assert.notNull(config, "阿里云短信服务配置不能为空");
        Assert.notNull(config.getAliyun(), "阿里云短信服务配置不能为空");
        return config.getAliyun();
    }

    @Override
    public XPackAppType getTemplateConfigType() {
        return XPackAppType.SMS_TEMPLATE_ALIYUN;
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        try {
            SmsProviderProperty.SmsAliyun aliyun = requireConfig(config);
            SendSmsRequest request = new SendSmsRequest();
            request.setSignName(StringUtils.isNotEmpty(info.getSignature()) ? info.getSignature() : aliyun.getSignature());
            request.setPhoneNumbers(info.getMobile());
            request.setTemplateCode(info.getTemplateId());
            request.setTemplateParam(JsonHelper.toJson(info.convertVariablesToMap()));
            SendSmsResponse response = client.getAcsResponse(request);
            info.setResponse(JsonHelper.toJson(response));
            log.debug(info.getResponse());
            if (StringUtils.isNotEmpty(response.getCode()) && response.getCode().equalsIgnoreCase("OK")) {
                info.setThirdpartyMessageId(response.getBizId());
                log.info("has sent to aliyun {}", response.getBizId());
                return true;
            } else {
                log.error("failed to send aliyun code:{} message:{}", response.getCode(), response.getMessage());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return false;
    }
}

