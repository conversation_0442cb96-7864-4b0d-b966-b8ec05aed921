package org.befun.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Consumer;


@Slf4j
public abstract class CustomEmbeddedService<EE extends BaseEntity, ED extends BaseEntityDTO<EE>, R extends ResourceRepository<EE, Long>> extends AbstractService<EE, ED, R> {

    @Deprecated
    protected Object requireParent(long entityId){
        return entityId;
    }

    public <S extends ResourceCustomQueryDto> Page<ED> findAllEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            S params) {
        throw new NotImplementedException();
    }

    protected Consumer<GenericSpecification<EE>> appendParentId(Long rootId, String rootFieldNameInEmbedded) {
        return s -> s.add(new ResourceQueryCriteria(rootFieldNameInEmbedded, rootId));
    }

    public Page<ED> findAllEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            ResourceEntityQueryDto<ED> queryDto) {
        return super.findAll(queryDto, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public CountDto countEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            ResourceEntityQueryDto<ED> queryDto) {
        return super.count(queryDto, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public ED createEmbeddedMany(
            Long rootId,
            EmbeddedFieldContext fieldContext,
            ED data) {
        return super.create(data, entity -> {
            Object parent = EntityUtility.requireEntityOrEntityId(crudService, rootId, fieldContext.rootFieldInEmbedded.getType());
            EntityUtility.setEmbeddedProperty(entity, parent, fieldContext);
        });
    }

    public <SD extends BaseEntityDTO<EE>> ED createEmbeddedMany2(
            Long rootId,
            EmbeddedFieldContext fieldContext,
            SD data) {
        return super.create(data, entity -> {
            Object parent = EntityUtility.requireEntityOrEntityId(crudService, rootId, fieldContext.rootFieldInEmbedded.getType());
            EntityUtility.setEmbeddedProperty(entity, parent, fieldContext);
        });
    }

    public ED findOneEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId) {
        return super.findOne(embeddedId, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public ED updateOneEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId, ED change) {
        return super.updateOne(embeddedId, change, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public <SD extends BaseEntityDTO<EE>> ED updateOneEmbeddedMany2(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId, SD change) {
        return super.updateOne(embeddedId, change, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public Boolean deleteOneEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId) {
        return super.deleteOne(embeddedId, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public List<ED> batchUpdateEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            ResourceBatchUpdateRequestDto<ED> batchChangeDto) {
        return super.batchUpdate(batchChangeDto);
    }
}
