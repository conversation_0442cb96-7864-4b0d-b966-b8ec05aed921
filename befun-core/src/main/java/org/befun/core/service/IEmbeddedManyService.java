package org.befun.core.service;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Consumer;

public interface IEmbeddedManyService {

    AbstractEmbeddedService<?, ?, ?>.EmbeddedWrap getWrap();

    private Class<? extends BaseEntity> getEntityClass() {
        return getWrap().getEntityClass();
    }

    private CrudService getCrudService() {
        return getWrap().getCrudService();
    }

    private EmbeddedFieldContext requireEmbeddedManyFieldContext(String fieldNameInRoot) {
        return getWrap().requireEmbeddedManyFieldContext(fieldNameInRoot);
    }

    private <AE extends BaseEntity, ID> ResourceRepository<AE, ID> getRepository(Class<AE> entityClass) {
        return getCrudService().getRepository(entityClass);
    }

    private <EE extends BaseEntity> Consumer<GenericSpecification<EE>> appendParentId(Long id, String mapperBy) {
        return s -> s.add(new ResourceQueryCriteria(mapperBy, id));
    }

    private BaseEntity requireEntity(Long embeddedId, Class<? extends BaseEntity> eeClass) {
        return getRepository(eeClass).getOne(embeddedId);
    }

    private void checkExistsEntity(Long id) {
        getCrudService().checkExistsEntity(id, getEntityClass());
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>, S extends ResourceCustomQueryDto> Page<ED> findAllEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            S params) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            return service.findAllEmbeddedMany(rootId, fieldContext.rootFieldNameInEmbedded, params);
        } else {
            throw new NotImplementedException();
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> Page<ED> findAllEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            ResourceEntityQueryDto<ED> queryDto) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            return service.findAllEmbeddedMany(rootId, fieldContext.rootFieldNameInEmbedded, queryDto);
        } else {
            return getCrudService().findAll(queryDto, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> CountDto countEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            ResourceEntityQueryDto<ED> queryDto) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            return service.countEmbeddedMany(rootId, fieldContext.rootFieldNameInEmbedded, queryDto);
        } else {
            return getCrudService().count(queryDto, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded));
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> ED findOneEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            Class<EE> eeClass,
            Class<ED> edClass) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            return service.findOneEmbeddedMany(rootId, fieldContext.rootFieldNameInEmbedded, embeddedId);
        } else {
            return getCrudService().findOne(embeddedId, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>, SD extends BaseEntityDTO<EE>> ED createEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            SD change) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            if (change != null && !edClass.equals(change.getClass())) {
                return service.createEmbeddedMany2(rootId, fieldContext, change);
            } else {
                return service.createEmbeddedMany(rootId, fieldContext, (ED) change);
            }
        } else {
            return getCrudService().create(change, eeClass, edClass, getRepository(eeClass), entity -> {
                BaseEntity parent = requireEntity(rootId, getEntityClass());
                EntityUtility.setEmbeddedProperty(entity, parent, fieldContext);
            }, null, null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>, SD extends BaseEntityDTO<EE>> ED updateOneEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            Class<EE> eeClass,
            Class<ED> edClass,
            SD change) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            if (change != null && !edClass.equals(change.getClass())) {
                return service.updateOneEmbeddedMany2(rootId, fieldContext.rootFieldNameInEmbedded, embeddedId, change);
            } else {
                return service.updateOneEmbeddedMany(rootId, fieldContext.rootFieldNameInEmbedded, embeddedId, (ED) change);
            }
        } else {
            return getCrudService().updateOne(embeddedId, change, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null, null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> Boolean deleteOneEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            Class<EE> eeClass,
            Class<ED> edClass) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            return service.deleteOneEmbeddedMany(rootId, fieldContext.rootFieldNameInEmbedded, embeddedId);
        } else {
            return getCrudService().deleteOne(embeddedId, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> List<ED> batchUpdateEmbeddedMany(
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            ResourceBatchUpdateRequestDto<ED> batchChangeDto) {
        EmbeddedFieldContext fieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
        CustomEmbeddedService<EE, ED, ?> service = getCrudService().getCustomEmbeddedService(eeClass);
        if (service != null) {
            return service.batchUpdateEmbeddedMany(null/*todo*/, fieldContext.rootFieldNameInEmbedded, batchChangeDto);
        } else {
            return getCrudService().batchUpdate(batchChangeDto, eeClass, edClass, getRepository(eeClass), null, null);
        }
    }
}
