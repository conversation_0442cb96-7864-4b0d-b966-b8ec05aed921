package org.befun.core.service;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

/**
 * The class description
 * 输出的必须都是D，也就是DTO层的数据，隐藏Entity细节
 *
 * <AUTHOR>
 */
public abstract class BaseNoPageService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> extends AbstractService<E, D, R> {

    public <S extends ResourceCustomQueryDto> List<D> findAll(S params) {
        throw new NotImplementedException();
    }

    public List<D> findAll(ResourceEntityQueryDto<D> queryDto) {
        return super.findAllNoPage(queryDto, null);
    }

    public CountDto count(ResourceEntityQueryDto<D> queryDto) {
        return super.count(queryDto, null);
    }

    public <S extends BaseEntityDTO<E>> D create(S data) {
        return super.create(data, null);
    }

    public D findOne(long id) {
        return super.findOne(id, null);
    }

    public <S extends BaseEntityDTO<E>> D updateOne(long id, S change) {
        return super.updateOne(id, change, null);
    }

    public Boolean deleteOne(long id) {
        return super.deleteOne(id, null);
    }

    public <S extends BaseEntityDTO<E>> List<D> batchUpdate(ResourceBatchUpdateRequestDto<S> batchChangeDto) {
        return super.batchUpdate(batchChangeDto);
    }
}
