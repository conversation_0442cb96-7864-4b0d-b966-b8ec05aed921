package org.befun.core.service;

import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;

import java.util.function.Consumer;

public interface IEmbeddedOneService {

    AbstractEmbeddedService<?, ?, ?>.EmbeddedWrap getWrap();

    private Class<? extends BaseEntity> getEntityClass() {
        return getWrap().getEntityClass();
    }

    private CrudService getCrudService() {
        return getWrap().getCrudService();
    }

    private EmbeddedFieldContext requireEmbeddedOneFieldContext(String fieldNameInRoot) {
        return getWrap().requireEmbeddedOneFieldContext(fieldNameInRoot);
    }

    private <AE extends BaseEntity, ID> ResourceRepository<AE, ID> getRepository(Class<AE> entityClass) {
        return getCrudService().getRepository(entityClass);
    }

    private <EE extends BaseEntity> Consumer<GenericSpecification<EE>> appendParentId(Long id, String mapperBy) {
        return s -> s.add(new ResourceQueryCriteria(mapperBy, id));
    }

    private BaseEntity requireEntity(Long embeddedId, Class<? extends BaseEntity> eeClass) {
        return getRepository(eeClass).getOne(embeddedId);
    }

    private void checkExistsEntity(Long id) {
        getCrudService().checkExistsEntity(id, getEntityClass());
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> ED findAllEmbeddedOne(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedOneFieldContext(fieldNameInRoot);
        CustomEmbeddedOneService<EE, ED, ?> service = getCrudService().getCustomEmbeddedOneService(eeClass);
        if (service != null) {
            return service.findAllEmbeddedOne(rootId, fieldContext.rootFieldNameInEmbedded);
        } else {
            return getCrudService().findAllSingle(eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>, SD extends BaseEntityDTO<EE>> ED createEmbeddedOne(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            SD data) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedOneFieldContext(fieldNameInRoot);
        CustomEmbeddedOneService<EE, ED, ?> service = getCrudService().getCustomEmbeddedOneService(eeClass);
        if (service != null) {
            if (data != null && !edClass.equals(data.getClass())) {
                return service.createEmbeddedOne2(rootId, fieldContext, data);
            } else {
                return service.createEmbeddedOne(rootId, fieldContext, (ED) data);
            }
        } else {
            return getCrudService().createSingle(data, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), entity -> {
                BaseEntity parent = requireEntity(rootId, getEntityClass());
                EntityUtility.setEmbeddedProperty(entity, parent, fieldContext);
            }, null, null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>, SD extends BaseEntityDTO<EE>> ED updateOneEmbeddedOne(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass,
            SD change) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedOneFieldContext(fieldNameInRoot);
        CustomEmbeddedOneService<EE, ED, ?> service = getCrudService().getCustomEmbeddedOneService(eeClass);
        if (service != null) {
            if (change != null && !edClass.equals(change.getClass())) {
                return service.updateOneEmbeddedOne2(rootId, fieldContext.rootFieldNameInEmbedded, change);
            } else {
                return service.updateOneEmbeddedOne(rootId, fieldContext.rootFieldNameInEmbedded, (ED) change);
            }
        } else {
            return getCrudService().updateOneSingle(change, eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null, null, null);
        }
    }

    default <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> Boolean deleteOneEmbeddedOne(
            Long rootId,
            String fieldNameInRoot,
            Class<EE> eeClass,
            Class<ED> edClass) {
        checkExistsEntity(rootId);
        EmbeddedFieldContext fieldContext = requireEmbeddedOneFieldContext(fieldNameInRoot);
        CustomEmbeddedOneService<EE, ED, ?> service = getCrudService().getCustomEmbeddedOneService(eeClass);
        if (service != null) {
            return service.deleteOneEmbeddedOne(rootId, fieldContext.rootFieldNameInEmbedded);
        } else {
            return getCrudService().deleteOneSingle(eeClass, edClass, getRepository(eeClass), appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), null);
        }
    }
}
