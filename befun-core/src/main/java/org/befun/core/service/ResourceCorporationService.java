package org.befun.core.service;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.ResourcePermissionRelationTypes;
import org.befun.core.constant.ResourcePermissionTypes;
import org.befun.core.constant.ResourceShareFlag;
import org.befun.core.dto.*;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.ResourcePermission;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.exception.BadRequestException;
import org.befun.core.repo.ResourcePermissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.befun.core.constant.ResourceShareFlag.*;

/**
 * 基于0。5版本重构的resource scope permission service
 *
 * <AUTHOR>
 */
@Service
public class ResourceCorporationService {
    @Autowired
    private ResourcePermissionRepository permissionRepository;
    @Autowired(required = false)
    private IResourceInfoService resourceInfoService;

    private <T extends BaseEntity> String resourceName(T instance) {
        EntityScopeStrategy strategy = instance.getClass().getAnnotation(EntityScopeStrategy.class);
        if (strategy != null && StringUtils.isNotEmpty(strategy.resource())) {
            return strategy.resource();
        }
        return instance.getClass().getSimpleName().toUpperCase();
    }

    /**
     * 删除分享
     */
    public <T extends EnterpriseEntity> void stopShareToUser(T instance, Long userId) {
        stopShareToUser(instance.getId(), resourceName(instance), userId);
    }

    /**
     * 删除分享
     */
    public void stopShareToUser(Long resourceId, String resourceName, Long userId) {
        stopShareToUser(resourceId, resourceName, userId, null);
    }

    /**
     * 删除分享
     */
    public void stopShareToUser(Long resourceId, ResourcePermissionTypes resourceType, Long userId, ResourceShareFlag stopFlag) {
        stopShareToUser(resourceId, resourceType.name(), userId, stopFlag);
    }

    /**
     * 删除分享
     */
    public void stopShareToUser(Long resourceId, String resourceName, Long userId, ResourceShareFlag stopFlag) {
        List<ResourcePermission> permission = permissionRepository.findAllByResourceTypeAndResourceIdAndUserId(resourceName, resourceId, userId);
        if (CollectionUtils.isNotEmpty(permission)) {
            permissionRepository.deleteAll(permission);
        } else {
            if (stopFlag == null || stopFlag == STOP_NOT_FOUND_THROWABLE) {
                throw new BadRequestException("没有共享");
            }
        }
    }

    /**
     * 删除分享
     */
    public <T extends EnterpriseEntity> void stopShareToRole(T instance, Long roleId) {
        stopShareToRole(instance.getId(), resourceName(instance), roleId);
    }

    /**
     * 删除分享
     */
    public void stopShareToRole(Long resourceId, String resourceName, Long roleId) {
        List<ResourcePermission> permission = permissionRepository.findAllByResourceTypeAndResourceIdAndRoleId(resourceName, resourceId, roleId);
        if (CollectionUtils.isEmpty(permission)) {
            throw new BadRequestException("没有共享");
        }
        permissionRepository.deleteAll(permission);
    }

    /**
     * 添加分享
     */
    public <T extends EnterpriseEntity> ResourcePermissionDto shareToUser(T instance, Long userId) {
        return shareToUser(instance.getId(), resourceName(instance), null, instance.getOrgId(), userId);
    }

    /**
     * 添加分享
     */
    public ResourcePermissionDto shareToUser(Long resourceId,
                                             ResourcePermissionTypes resourceType,
                                             ResourcePermissionRelationTypes relationType,
                                             Long orgId,
                                             Long userId) {
        return shareToUser(resourceId, resourceType.name(), relationType.name(), orgId, userId);
    }


    /**
     * 添加分享
     */
    public ResourcePermissionDto shareToUser(Long resourceId,
                                             ResourcePermissionTypes resourceType,
                                             ResourcePermissionRelationTypes relationType,
                                             Long orgId,
                                             Long userId,
                                             ResourceShareFlag shareFlag) {
        return shareToUser(resourceId, resourceType.name(), relationType.name(), orgId, userId, shareFlag);
    }

    /**
     * 添加分享
     */
    public ResourcePermissionDto shareToUser(Long resourceId, String resourceName, String relationType, Long orgId, Long userId) {
        return shareToUser(resourceId, resourceName, relationType, orgId, userId, null);
    }


    /**
     * 添加分享
     */
    public ResourcePermissionDto shareToUser(Long resourceId, String resourceName, String relationType, Long orgId, Long userId, ResourceShareFlag shareFlag) {
        List<ResourcePermission> permission = permissionRepository.findAllByResourceTypeAndResourceIdAndUserId(resourceName, resourceId, userId);
        if (CollectionUtils.isNotEmpty(permission)) {
            ResourcePermission found = permission.get(0);
            if (shareFlag == null || shareFlag == SHARE_FOUND_THROWABLE) {
                throw new BadRequestException("已经共享");
            } else if (shareFlag == SHARE_FOUND_UPDATE_RELATION) {
                if (relationType != null && !relationType.equals(found.getRelationType())) {
                    found.setRelationType(relationType);
                    permissionRepository.save(found);
                }
            }
            return new ResourcePermissionDto(found);
        }
        ResourcePermission newPermission = new ResourcePermission();
        newPermission.setResourceType(resourceName);
        newPermission.setResourceId(resourceId);
        newPermission.setRelationType(relationType);
        newPermission.setOrgId(orgId);
        newPermission.setUserId(userId);
        permissionRepository.save(newPermission);
        return new ResourcePermissionDto(newPermission);
    }

    /**
     * 添加分享
     */
    public <T extends EnterpriseEntity> ResourcePermissionDto shareToRole(T instance, Long roleId) {
        return shareToRole(instance.getId(), resourceName(instance), instance.getOrgId(), roleId);
    }

    /**
     * 添加分享
     */
    public ResourcePermissionDto shareToRole(Long resourceId, String resourceName, Long orgId, Long roleId) {
        List<ResourcePermission> permission = permissionRepository.findAllByResourceTypeAndResourceIdAndRoleId(resourceName, resourceId, roleId);
        if (CollectionUtils.isNotEmpty(permission)) {
            throw new BadRequestException("已经共享");
        }
        ResourcePermission newPermission = new ResourcePermission();
        newPermission.setResourceId(resourceId);
        newPermission.setResourceType(resourceName);
        newPermission.setOrgId(orgId);
        newPermission.setRoleId(roleId);
        newPermission = permissionRepository.save(newPermission);
        return new ResourcePermissionDto(newPermission);
    }

    /**
     * 获取已经邀请列表
     */
    public <T extends EnterpriseEntity> List<ResourcePermissionDto> getAllShares(T instance) {
        return getAllShares(instance.getId(), resourceName(instance));
    }

    /**
     * 获取已经邀请列表
     */
    public List<ResourcePermissionDto> getAllShares(Long resourceId, String resourceName) {
        return getAllShares0(resourceId, resourceName).stream()
                .map(ResourcePermissionDto::new)
                .collect(Collectors.toList());
    }

    /**
     * 获取已经邀请列表
     */
    public List<ResourcePermission> getAllShares0(Long resourceId, String resourceName) {
        return permissionRepository.findAllByResourceTypeAndResourceId(resourceName, resourceId);
    }

    /**
     * 获取已经邀请列表
     */
    public List<ResourcePermission> getAllShares0(List<Long> resourceIds, String resourceName) {
        return permissionRepository.findAllByResourceTypeAndResourceIdIn(resourceName, resourceIds);
    }

    /**
     * 设置返回列表中的数据权限信息
     */
    public <D extends BaseResourcePermissionEntityDto<?>> void fillResourcePermissionInfo(List<D> resources) {
        String resourceName = resourceName(resources.get(0).getEntity());
        fillResourcePermissionInfo(resources, resourceName, i -> i.getId());
    }

    /**
     * 设置返回列表中的数据权限信息
     */
    public <D extends BaseResourcePermissionEntityDto<?>> void fillResourcePermissionInfo(List<D> resources, String resourceName, Function<D, Long> getResourceId) {
        if (CollectionUtils.isNotEmpty(resources)) {
            Map<Long, List<ResourcePermission>> resourceMap = new HashMap<>();
            Map<Long, ResourcePermissionUserDto> userMap = new HashMap<>();
            Map<Long, ResourcePermissionRoleDto> roleMap = new HashMap<>();
            Map<Long, ResourcePermissionDepartmentDto> departmentMap = new HashMap<>();
            if (resourceInfoService != null) {
                List<Long> resourceIds = resources.stream().map(getResourceId).collect(Collectors.toList());
                List<ResourcePermission> permissions = getAllShares0(resourceIds, resourceName);
                if (CollectionUtils.isNotEmpty(permissions)) {
                    Set<Long> userIds = new HashSet<>();
                    Set<Long> roleIds = new HashSet<>();
                    Set<Long> departmentIds = new HashSet<>();
                    permissions.forEach(i -> {
                        if (i.getUserId() != null && i.getUserId() > 0) {
                            userIds.add(i.getUserId());
                        }
                        if (i.getRoleId() != null && i.getRoleId() > 0) {
                            roleIds.add(i.getRoleId());
                        }
                        if (i.getDepartmentId() != null && i.getDepartmentId() > 0) {
                            departmentIds.add(i.getDepartmentId());
                        }
                        if (i.getResourceId() != null && i.getResourceId() > 0) {
                            resourceMap.computeIfAbsent(i.getResourceId(), k -> new ArrayList<>()).add(i);
                        }
                    });
                    if (!userIds.isEmpty()) {
                        Optional.ofNullable(resourceInfoService.getPermissionUsers(userIds)).ifPresent(s -> {
                            s.forEach(i -> userMap.put(i.getId(), i));
                        });
                    }
                    if (!roleIds.isEmpty()) {
                        Optional.ofNullable(resourceInfoService.getPermissionRoles(roleIds)).ifPresent(s -> {
                            s.forEach(i -> roleMap.put(i.getId(), i));
                        });
                    }
                    if (!departmentIds.isEmpty()) {
                        Optional.ofNullable(resourceInfoService.getPermissionDepartments(departmentIds)).ifPresent(s -> {
                            s.forEach(i -> departmentMap.put(i.getId(), i));
                        });
                    }
                }
            }
            resources.forEach(r -> {
                List<ResourcePermission> resourcePermissions = resourceMap.get(getResourceId.apply(r));
                List<ResourcePermissionUserDto> users = new ArrayList<>();
                List<ResourcePermissionRoleDto> roles = new ArrayList<>();
                List<ResourcePermissionDepartmentDto> departments = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(resourcePermissions)) {
                    resourcePermissions.forEach(i -> {
                        if (i.getUserId() != null && i.getUserId() > 0) {
                            Optional.ofNullable(userMap.get(i.getUserId()))
                                    .flatMap(dto -> Optional.of(dto.copy()))
                                    .ifPresent(d -> {
                                        d.setType(i.getRelationType());
                                        users.add(d);
                                    });
                        }
                        if (i.getRoleId() != null && i.getRoleId() > 0) {
                            Optional.ofNullable(roleMap.get(i.getRoleId()))
                                    .flatMap(dto -> Optional.of(dto.copy()))
                                    .ifPresent(d -> {
                                        d.setType(i.getRelationType());
                                        roles.add(d);
                                    });
                        }
                        if (i.getDepartmentId() != null && i.getDepartmentId() > 0) {
                            Optional.ofNullable(departmentMap.get(i.getDepartmentId())).ifPresent(departments::add);
                        }
                    });
                }
                r.setPermissionUsers(users);
                r.setPermissionRoles(roles);
                r.setPermissionDepartments(departments);
            });
        }
    }

}

