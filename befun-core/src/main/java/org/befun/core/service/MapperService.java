package org.befun.core.service;

import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
public class MapperService {
    @Autowired
    protected ModelMapper modelMapper;

    public <D> D map(Object source, Class<D> destinationType) {
        D dto =  modelMapper.map(source, destinationType);
        if (dto instanceof BaseEntityDTO){
            ((BaseEntityDTO)dto).setEntity(source);
        }
        return dto;
    }

    public void map(Object source, Object destination) {
        modelMapper.map(source, destination);
    }

    /**
     * map list of entity to dto
     *
     * @param items
     * @param dtoClass
     * @param <E>
     * @param <D>
     * @return
     */
    public <E extends BaseEntity, D> List<D> mapListEntityToDto(Iterable<E> items, Class<D> dtoClass) {
        if (items == null) {
            return null;
        }
        List<D> result = new ArrayList<>();
        for (E item : items) {
            result.add(map(item, dtoClass));
        }
        return result;
    }
}
