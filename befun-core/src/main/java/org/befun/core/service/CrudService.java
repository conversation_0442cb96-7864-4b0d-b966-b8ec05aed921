package org.befun.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.*;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.entity.condition.DeleteLimit;
import org.befun.core.entity.condition.UpdateLimit;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.rest.query.PageResult;
import org.befun.core.utils.TypeUtility;
import org.hibernate.annotations.SQLDelete;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.support.Repositories;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("unchecked")
public class CrudService {

    @Lazy
    @Autowired
    private MapperService mapperService;
    @Autowired
    private ListableBeanFactory beanFactory;
    @Autowired
    private WebApplicationContext appContext;
    private Repositories repositories;
    private final Map<Class<? extends BaseEntity>, SoftDeleteDto> softDeleteMap = new HashMap<>();
    private final Map<Class<? extends BaseEntity>, CustomEmbeddedOneService<?, ?, ?>> embeddedOneServiceMap = new HashMap<>();
    private final Map<Class<? extends BaseEntity>, CustomEmbeddedService<?, ?, ?>> embeddedServiceMap = new HashMap<>();
    private final Map<Class<? extends BaseEntity>, CustomDeepEmbeddedService<?, ?, ?>> deepEmbeddedServiceMap = new HashMap<>();

    @PostConstruct
    @SuppressWarnings("rawtypes")
    public void setup() {
        Map<String, CustomEmbeddedOneService> mapEmbeddedOne = beanFactory.getBeansOfType(CustomEmbeddedOneService.class);
        if (MapUtils.isNotEmpty(mapEmbeddedOne)) {
            mapEmbeddedOne.values().forEach(i -> {
                Class<? extends BaseEntity> clazz = (Class<? extends BaseEntity>) TypeUtility.getTypeParameterType(i.getClass(), CustomEmbeddedOneService.class, 0);
                embeddedOneServiceMap.put(clazz, i);
            });
        }
        Map<String, CustomEmbeddedService> mapEmbedded = beanFactory.getBeansOfType(CustomEmbeddedService.class);
        if (MapUtils.isNotEmpty(mapEmbedded)) {
            mapEmbedded.values().forEach(i -> {
                Class<? extends BaseEntity> clazz = (Class<? extends BaseEntity>) TypeUtility.getTypeParameterType(i.getClass(), CustomEmbeddedService.class, 0);
                embeddedServiceMap.put(clazz, i);
            });
        }
        Map<String, CustomDeepEmbeddedService> mapEmbeddedMany = beanFactory.getBeansOfType(CustomDeepEmbeddedService.class);
        if (MapUtils.isNotEmpty(mapEmbeddedMany)) {
            mapEmbeddedMany.values().forEach(i -> {
                Class<? extends BaseEntity> clazz = (Class<? extends BaseEntity>) TypeUtility.getTypeParameterType(i.getClass(), CustomDeepEmbeddedService.class, 0);
                deepEmbeddedServiceMap.put(clazz, i);
            });
        }
    }

    public <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> CustomEmbeddedOneService<EE, ED, ?> getCustomEmbeddedOneService(Class<EE> eeClass) {
        return (CustomEmbeddedOneService<EE, ED, ?>) embeddedOneServiceMap.get(eeClass);
    }

    public <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> CustomEmbeddedService<EE, ED, ?> getCustomEmbeddedService(Class<EE> eeClass) {
        return (CustomEmbeddedService<EE, ED, ?>) embeddedServiceMap.get(eeClass);
    }

    public <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> CustomDeepEmbeddedService<EE, ED, ?> getCustomDeepEmbeddedService(Class<EE> eeClass) {
        return (CustomDeepEmbeddedService<EE, ED, ?>) deepEmbeddedServiceMap.get(eeClass);
    }

    public <AE extends BaseEntity, ID> ResourceRepository<AE, ID> getRepository(Class<AE> entityClass) {
        if (repositories == null) {
            Assert.notNull(appContext, "generic repository should be access in mvc context");
            repositories = new Repositories(appContext);
        }
        Assert.notNull(repositories, "missing repositories injected");
        Optional<Object> a = repositories.getRepositoryFor(entityClass);
        if (a.isPresent()) {
            return (ResourceRepository<AE, ID>) a.get();
        }
        throw new RuntimeException("Missing Repository for " + entityClass.getName());
    }

    public <BE extends BaseEntity> SoftDeleteDto isSoftDelete(Class<BE> eClass) {
        SoftDeleteDto isSoftDelete = softDeleteMap.get(eClass);
        if (isSoftDelete == null) {
            SoftDelete softDelete = eClass.getAnnotation(SoftDelete.class);
            if (softDelete != null) {
                isSoftDelete = SoftDeleteDto.createByAnnotation(softDelete);
                softDeleteMap.put(eClass, isSoftDelete);
            } else {
                SQLDelete[] deleteAnnotation = eClass.getAnnotationsByType(SQLDelete.class);
                boolean hasAnnotation = deleteAnnotation.length > 0;
                if (hasAnnotation) {
                    isSoftDelete = SoftDeleteDto.createByDefault();
                    softDeleteMap.put(eClass, isSoftDelete);
                }
            }
        }
        if (isSoftDelete == null) {
            isSoftDelete = SoftDeleteDto.createDisable();
            softDeleteMap.put(eClass, isSoftDelete);
        }
        return isSoftDelete;
    }

    public void checkExistsEntity(Long id, Class<? extends BaseEntity> entityClass) {
        if (!Optional.ofNullable(getRepository(entityClass)).map(r -> r.existsById(id)).orElse(false)) {
            throw new EntityNotFoundException(entityClass);
        }
    }

    public <BE extends BaseEntity, BD extends BaseEntityDTO<BE>> List<BD> mapToDto(List<BE> entity, Class<BD> edClass) {
        return mapperService.mapListEntityToDto(entity, edClass);
    }

    public <BE extends BaseEntity, BD extends BaseEntityDTO<BE>> BD mapToDto(BE entity, Class<BD> edClass) {
        return Optional.ofNullable(entity).map(e -> mapperService.map(e, edClass)).orElse(null);
    }

    public <BE extends BaseEntity, PP extends BaseEntityDTO<BE>> void mapToEntity(PP dto, BE entity) {
        mapperService.map(dto, entity);
    }

    private <E extends BaseEntity, D extends BaseEntityDTO<E>> D mapToDto(E entity, Class<D> dtoClass, Function<E, D> customMapToDto) {
        if (customMapToDto == null) {
            // 如果没有使用自定义的映射方法，则使用默认的映射方法
            return mapToDto(entity, dtoClass);
        }
        return customMapToDto.apply(entity);
    }

    private <E extends BaseEntity, D extends BaseEntityDTO<E>> List<D> mapToDto(List<E> entity, Class<D> dtoClass, Function<List<E>, List<D>> customMapToDto) {
        if (customMapToDto == null) {
            // 如果没有使用自定义的映射方法，则使用默认的映射方法
            return mapToDto(entity, dtoClass);
        }
        return customMapToDto.apply(entity);
    }

    private <E extends BaseEntity, S extends BaseEntityDTO<E>> void mapToEntity(S change, E entity, BiFunction<S, E, Boolean> customMapToEntity) {
        if (customMapToEntity == null || !Optional.ofNullable(customMapToEntity.apply(change, entity)).orElse(false)) {
            // 如果没有使用自定义的映射方法，则使用默认的映射方法
            mapToEntity(change, entity);
        }
    }

    //************************************************************
    //******************* collection crud ************************
    //************************************************************
    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> Page<D> findAll(
            ResourceEntityQueryDto<D> queryDto,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            Function<List<E>, List<D>> customMapToDto,
            BiConsumer<List<E>, List<D>> afterMapToDto) {
        PageRequest pageRequest = PageRequest.of(queryDto.getPage() - 1, queryDto.getLimit(), queryDto.getSorts());
        GenericSpecification<E> specification = new GenericSpecification<>(queryDto);
        Optional.ofNullable(appendCondition).ifPresent(i -> i.accept(specification));
        Page<E> result = repository.findAll(specification, pageRequest);
        List<E> entityList = result.toList();
        List<D> dtoList = baseFindAll(entityList, dClass, customMapToDto, afterMapToDto);
        return new PageResult<>(dtoList, pageRequest, result.getTotalElements());
    }

    <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> List<D> baseFindAll(
            List<E> entityList,
            Class<D> dClass,
            Function<List<E>, List<D>> customMapToDto,
            BiConsumer<List<E>, List<D>> afterMapToDto
    ) {
        List<D> dtoList = mapToDto(entityList, dClass, customMapToDto);
        if (dtoList == null) {
            dtoList = new ArrayList<>();
        }
        if (afterMapToDto != null) {
            afterMapToDto.accept(entityList, dtoList);
        }
        return dtoList;
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> List<D> findAllNoPage(
            ResourceEntityQueryDto<D> queryDto,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            Function<List<E>, List<D>> customMapToDto,
            BiConsumer<List<E>, List<D>> afterMapToDto) {
        Sort sort = queryDto.getSorts();
        GenericSpecification<E> specification = new GenericSpecification<>(queryDto);
        Optional.ofNullable(appendCondition).ifPresent(i -> i.accept(specification));
        List<E> entityList = repository.findAll(specification, sort);
        return baseFindAll(entityList, dClass, customMapToDto, afterMapToDto);
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> CountDto count(
            ResourceEntityQueryDto<D> queryDto,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition) {
        GenericSpecification<E> specification = new GenericSpecification<>(queryDto);
        Optional.ofNullable(appendCondition).ifPresent(i -> i.accept(specification));
        long total = repository.count(specification);
        CountDto countDto = new CountDto(total);
        if (StringUtils.isNotEmpty(queryDto.getBy())) {
            List<CountItemDto> countItemDtoList = repository.countBy(
                    queryDto.getBy(),
                    specification);
            countDto.setItems(countItemDtoList);
        }
        return countDto;
    }

    <E extends BaseEntity, R extends ResourceRepository<E, Long>> E requireEntity(
            long id,
            Class<E> eClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition) {
        GenericSpecification<E> specification = new GenericSpecification<>();
        specification.add(new ResourceQueryCriteria("id", id));
        Optional.ofNullable(appendCondition).ifPresent(i -> i.accept(specification));
        return repository.findOne(specification).orElseThrow(() -> new EntityNotFoundException(eClass));
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> D findOne(
            long id,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterMapToDto) {
        E entity = requireEntity(id, eClass, repository, appendCondition);
        D dto = mapToDto(entity, dClass, customMapToDto);
        if (afterMapToDto != null) {
            afterMapToDto.accept(entity, dto);
        }
        return dto;
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> D create(
            S data,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<E> setProperty,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterExecuteMethod) {
        E entity;
        try {
            entity = eClass.getConstructor().newInstance();
            mapToEntity(data, entity, customMapToEntity);
            Optional.ofNullable(setProperty).ifPresent(i -> i.accept(entity));
            E saved = repository.save(entity);
            D dto = mapToDto(saved, dClass, customMapToDto);
            if (afterExecuteMethod != null) {
                afterExecuteMethod.accept(saved, dto);
            }
            return dto;
        } catch (DataIntegrityViolationException ex) {
            log.error("violation error relation incorrect ", ex);
            throw new BadRequestException("请求数据检验失败");
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> D baseCreate(
            E entity,
            S data,
            Class<D> dClass,
            R repository,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterExecuteMethod) {
        mapToEntity(data, entity, customMapToEntity);
        E saved = repository.save(entity);
        D dto = mapToDto(saved, dClass, customMapToDto);
        if (afterExecuteMethod != null) {
            afterExecuteMethod.accept(saved, dto);
        }
        return dto;
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> D updateOne(
            long id,
            S change,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterExecuteMethod) {
        E entity = requireEntity(id, eClass, repository, appendCondition);
        return baseUpdate(entity, change, dClass, repository, customMapToEntity, customMapToDto, afterExecuteMethod);
    }

    <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> D baseUpdate(
            E entity,
            S change,
            Class<D> dClass,
            R repository,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterExecuteMethod) {
        long id = entity.getId();
        if (entity instanceof UpdateLimit) {
            ((UpdateLimit) entity).checkUpdate();
        }
        mapToEntity(change, entity, customMapToEntity);
        if (entity.getId() == null || entity.getId() != id) {
            // 如果修改的字段中，有id，则需要把id还原为之前的entity的id，禁止在此处修改id
            entity.setId(id);
        }
        E saved = repository.save(entity);
        D dto = mapToDto(saved, dClass, customMapToDto);
        if (afterExecuteMethod != null) {
            afterExecuteMethod.accept(saved, dto);
        }
        return dto;
    }


    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> Boolean deleteOne(
            long id,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            BiConsumer<E, D> afterExecuteMethod) {
        E entity = requireEntity(id, eClass, repository, appendCondition);
        return baseDelete(entity, repository, afterExecuteMethod);
    }

    <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> boolean baseDelete(
            E entity,
            R repository,
            BiConsumer<E, D> afterExecuteMethod) {
        if (entity instanceof DeleteLimit) {
            ((DeleteLimit) entity).checkDelete();
        }
        SoftDeleteDto isSoftDelete = isSoftDelete(entity.getClass());
        if (isSoftDelete != null && isSoftDelete.isEnable()) {
            try {
                PropertyUtils.setProperty(entity, isSoftDelete.getProperty(), isSoftDelete.getDeleteValue());
            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                throw new BadRequestException("未知的软删除属性");
            }
            repository.save(entity);
        } else {
            repository.delete(entity);
        }
        if (afterExecuteMethod != null) {
            afterExecuteMethod.accept(entity, null);
        }
        return true;
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> List<D> batchUpdate(
            ResourceBatchUpdateRequestDto<S> batchChangeDto,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<List<E>, List<D>> customMapToDto
    ) {
        List<Long> idList = batchChangeDto.getChanges().stream()
                .map(ResourceUpdateItemRequestDto::getId)
                .collect(Collectors.toList());

        Map<Long, E> entityMap = repository.findAllById(idList).stream()
                .collect(Collectors.toMap(E::getId, Function.identity()));

        if (batchChangeDto.getChanges().size() != entityMap.values().size()) {
            throw new BadRequestException("missing entity for batch update");
        }

        Set<E> changedEntities = new HashSet<>();
        for (ResourceUpdateItemRequestDto<S> change : batchChangeDto.getChanges()) {
            E entity = entityMap.get(change.getId());
            if (entity instanceof UpdateLimit) {
                ((UpdateLimit) entity).checkUpdate();
            }
            mapToEntity(change.getData(), entity, customMapToEntity);
            changedEntities.add(entity);
        }
        List<E> savedEntities = repository.saveAll(changedEntities);
        return mapToDto(savedEntities, dClass, customMapToDto);
    }

    //************************************************************
    //******************** single crud ***************************
    //************************************************************

    <E extends BaseEntity, R extends ResourceRepository<E, Long>> E findOneSingle(
            R repository,
            Consumer<GenericSpecification<E>> appendCondition) {
        GenericSpecification<E> specification = new GenericSpecification<>();
        Optional.ofNullable(appendCondition).ifPresent(i -> i.accept(specification));
        List<E> entityList = repository.findAll(specification);
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.get(0);
        }
        return null;
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> D findAllSingle(
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterMapToDto) {
        E entity = findOneSingle(repository, appendCondition);
        D dto = null;
        if (entity != null) {
            dto = mapToDto(entity, dClass, customMapToDto);
            if (afterMapToDto != null && dto != null) {
                afterMapToDto.accept(entity, dto);
            }
        }
        return dto;
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> D createSingle(
            S data,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            Consumer<E> setProperty,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterExecuteMethod) {
        E entity = findOneSingle(repository, appendCondition);
        if (entity != null) {
            throw new BadRequestException("已存在，不能重复创建");
        }
        return create(data, eClass, dClass, repository, setProperty, customMapToEntity, customMapToDto, afterExecuteMethod);
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>, S extends BaseEntityDTO<E>> D updateOneSingle(
            S change,
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            BiFunction<S, E, Boolean> customMapToEntity,
            Function<E, D> customMapToDto,
            BiConsumer<E, D> afterExecuteMethod) {
        E entity = findOneSingle(repository, appendCondition);
        if (entity == null) {
            throw new EntityNotFoundException(eClass);
        }
        return baseUpdate(entity, change, dClass, repository, customMapToEntity, customMapToDto, afterExecuteMethod);
    }

    public final <E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> Boolean deleteOneSingle(
            Class<E> eClass,
            Class<D> dClass,
            R repository,
            Consumer<GenericSpecification<E>> appendCondition,
            BiConsumer<E, D> afterExecuteMethod) {
        E entity = findOneSingle(repository, appendCondition);
        if (entity == null) {
            throw new EntityNotFoundException(eClass);
        }
        return baseDelete(entity, repository, afterExecuteMethod);
    }
}
