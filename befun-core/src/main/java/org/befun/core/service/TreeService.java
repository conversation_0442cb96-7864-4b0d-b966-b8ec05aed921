package org.befun.core.service;

import org.befun.core.collection.Forest;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.repository.ResourceRepository;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TreeService<E extends EnterpriseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E,Long>> extends BaseService<E, D, R> {
    /**
     * findAllTree
     * @return
     */
    @SuppressWarnings("unused")
    public Forest<D> findAllTree() {
        List<D> entities = mapToDto(repository.findAll());
        return new Forest<>(entities);
    }
}
