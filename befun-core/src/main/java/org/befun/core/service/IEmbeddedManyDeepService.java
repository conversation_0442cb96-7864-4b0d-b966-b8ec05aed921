package org.befun.core.service;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Consumer;

public interface IEmbeddedManyDeepService {

    AbstractEmbeddedService<?, ?, ?>.EmbeddedWrap getWrap();

    private Class<? extends BaseEntity> getEntityClass() {
        return getWrap().getEntityClass();
    }

    private CrudService getCrudService() {
        return getWrap().getCrudService();
    }

    private DeepEmbeddedFieldContext requireDeepEmbeddedFieldContext(String fieldNameInRoot, String fieldNameInEmbedded) {
        return getWrap().requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
    }

    private <AE extends BaseEntity, ID> ResourceRepository<AE, ID> getRepository(Class<AE> entityClass) {
        return getCrudService().getRepository(entityClass);
    }

    private <EE extends BaseEntity> Consumer<GenericSpecification<EE>> appendParentId(Long id, String mapperBy) {
        return s -> s.add(new ResourceQueryCriteria(mapperBy, id));
    }

    private BaseEntity requireEntity(Long embeddedId, Class<? extends BaseEntity> eeClass) {
        return getRepository(eeClass).getOne(embeddedId);
    }

    private void checkExistsEntity(Long rootId, Long embeddedId, Class<? extends BaseEntity> eeClass) {
        getCrudService().checkExistsEntity(rootId, getEntityClass());
        getCrudService().checkExistsEntity(embeddedId, eeClass);
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>, S extends ResourceCustomQueryDto> Page<DD> findAllDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass,
            S params) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            return service.findAllDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, params);
        } else {
            throw new NotImplementedException();
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>> Page<DD> findAllDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass,
            ResourceEntityQueryDto<DD> queryDto) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            return service.findAllDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, queryDto);
        } else {
            return getCrudService().findAll(queryDto, deClass, ddClass, getRepository(deClass), appendParentId(embeddedId, fieldContext.embeddedFieldNameInDeep), null, null);
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>> CountDto countDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass,
            ResourceEntityQueryDto<DD> queryDto) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            return service.countDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, queryDto);
        } else {
            return getCrudService().count(queryDto, deClass, ddClass, getRepository(deClass), appendParentId(embeddedId, fieldContext.embeddedFieldNameInDeep));
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>> DD findOneDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Long deepId,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            return service.findOneDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, deepId);
        } else {
            return getCrudService().findOne(deepId, deClass, ddClass, getRepository(deClass), appendParentId(embeddedId, fieldContext.embeddedFieldNameInDeep), null, null);
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>, SD extends BaseEntityDTO<DE>> DD updateOneDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Long deepId,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass,
            SD change) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            if (change != null && !ddClass.equals(change.getClass())) {
                return service.updateOneDeepEmbeddedMany2(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, deepId, change);
            } else {
                return service.updateOneDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, deepId, (DD) change);
            }
        } else {
            return getCrudService().updateOne(deepId, change, deClass, ddClass, getRepository(deClass), appendParentId(embeddedId, fieldContext.embeddedFieldNameInDeep), null, null, null);
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>, SD extends BaseEntityDTO<DE>> DD createDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass,
            SD change) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            if (change != null && !ddClass.equals(change.getClass())) {
                return service.createDeepEmbeddedMany2(rootId, embeddedId, fieldContext, change);
            } else {
                return service.createDeepEmbeddedMany(rootId, embeddedId, fieldContext, (DD) change);
            }
        } else {
            return getCrudService().create(change, deClass, ddClass, getRepository(deClass), entity -> {
                BaseEntity parent = requireEntity(embeddedId, eeClass);
                EntityUtility.setDeepEmbeddedProperty(entity, parent, fieldContext);
            }, null, null, null);
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>> Boolean deleteOneDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Long deepId,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            return service.deleteOneDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, deepId);
        } else {
            return getCrudService().deleteOne(deepId, deClass, ddClass, getRepository(deClass), appendParentId(embeddedId, fieldContext.embeddedFieldNameInDeep), null);
        }
    }

    default <EE extends BaseEntity, DE extends BaseEntity, DD extends BaseEntityDTO<DE>> List<DD> batchUpdateDeepEmbeddedMany(
            Long rootId,
            String fieldNameInRoot,
            Long embeddedId,
            String fieldNameInEmbedded,
            Class<EE> eeClass,
            Class<DE> deClass,
            Class<DD> ddClass,
            ResourceBatchUpdateRequestDto<DD> batchChangeDto) {
        checkExistsEntity(rootId, embeddedId, eeClass);
        DeepEmbeddedFieldContext fieldContext = requireDeepEmbeddedFieldContext(fieldNameInRoot, fieldNameInEmbedded);
        CustomDeepEmbeddedService<DE, DD, ?> service = getCrudService().getCustomDeepEmbeddedService(deClass);
        if (service != null) {
            return service.batchUpdateDeepEmbeddedMany(rootId, fieldContext.parent.rootFieldNameInEmbedded, embeddedId, fieldContext.embeddedFieldNameInDeep, batchChangeDto);
        } else {
            return getCrudService().batchUpdate(batchChangeDto, deClass, ddClass, getRepository(deClass), null, null);
        }
    }

}
