package org.befun.core.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;

import java.util.function.Consumer;


@Slf4j
public abstract class CustomEmbeddedOneService<EE extends BaseEntity, ED extends BaseEntityDTO<EE>, R extends ResourceRepository<EE, Long>> extends AbstractService<EE, ED, R> {

    @Deprecated
    protected Object requireParent(long entityId) {
        return entityId;
    }

    protected Consumer<GenericSpecification<EE>> appendParentId(Long rootId, String rootFieldNameInEmbedded) {
        return s -> s.add(new ResourceQueryCriteria(rootFieldNameInEmbedded, rootId));
    }

    public ED findAllEmbeddedOne(
            Long rootId,
            String rootFieldNameInEmbedded) {
        return super.findAllSingle(appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public ED createEmbeddedOne(
            Long rootId,
            EmbeddedFieldContext fieldContext,
            ED data) {
        return super.createSingle(data, appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), entity -> {
            Object parent = EntityUtility.requireEntityOrEntityId(crudService, rootId, fieldContext.rootFieldInEmbedded.getType());
            EntityUtility.setEmbeddedProperty(entity, parent, fieldContext);
        });
    }

    public <SD extends BaseEntityDTO<EE>> ED createEmbeddedOne2(
            Long rootId,
            EmbeddedFieldContext fieldContext,
            SD data) {
        return super.createSingle(data, appendParentId(rootId, fieldContext.rootFieldNameInEmbedded), entity -> {
            Object parent = EntityUtility.requireEntityOrEntityId(crudService, rootId, fieldContext.rootFieldInEmbedded.getType());
            EntityUtility.setEmbeddedProperty(entity, parent, fieldContext);
        });
    }

    public ED updateOneEmbeddedOne(
            Long rootId,
            String rootFieldNameInEmbedded,
            ED change) {
        return super.updateOneSingle(change, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public <SD extends BaseEntityDTO<EE>> ED updateOneEmbeddedOne2(
            Long rootId,
            String rootFieldNameInEmbedded,
            SD change) {
        return super.updateOneSingle(change, appendParentId(rootId, rootFieldNameInEmbedded));
    }

    public Boolean deleteOneEmbeddedOne(
            Long rootId,
            String rootFieldNameInEmbedded) {
        return super.deleteOneSingle(appendParentId(rootId, rootFieldNameInEmbedded));
    }

}
