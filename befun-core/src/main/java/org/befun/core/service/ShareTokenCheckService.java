package org.befun.core.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.constant.ErrorCode;
import org.befun.core.dto.UserDto;
import org.befun.core.exception.BusinessException;
import org.befun.core.generator.SysConvert;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.security.UserPrincipal;
import org.befun.core.utils.ListHelper;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Optional;

@Component("shareTokenCheck")
@Slf4j
public class ShareTokenCheckService {

    private static final String HEADER = "X-SHARE-TOKEN";

    private static final String sql = "SELECT " +
            "u.id AS user_id," +
            "u.org_id AS org_id," +
            "u.truename AS truename," +
            "u.department_ids AS department_ids " +
            "FROM user u WHERE u.id = :id";

    @Autowired
    private EntityManager entityManager;

    public boolean checkToken() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        if (ra != null) {
            HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
            String shareToken = request.getHeader(HEADER);
            if (shareToken != null) {
                UserDto user = fetchUserByShareToken(shareToken);
                if (user != null) {
                    TenantContext.setCurrentUserId(user.getId());
                    log.debug("fetch user info userId:{} orgId:{}", user.getId(), user.getOrgId());
                    UserPrincipal principal = new UserPrincipal(user);
                    Collection<? extends GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(
                            new String[]{
                                    "EXECUTE_SHARETOKEN"
                            }
                    );
                    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, shareToken, authorities);
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    return true;
                }
            }
        }
        throw new BusinessException(ErrorCode.ENTITY_NOT_FOUND, "");
    }

    /**
     *
     * @param shareToken
     * @return
     */
    private UserDto fetchUserByShareToken(String shareToken) {
        try {
            Long userId = SysConvert.toDecimal(shareToken);
            Session session = entityManager.unwrap(Session.class);
            NativeQuery query = session.createSQLQuery(sql);
            log.info(sql);
            query.setParameter("id", userId);
            Object result = query.getSingleResult();
            if (result != null) {
                Object[] attrs = (Object[]) result;
                UserDto userDto = new UserDto();
                userDto.setId(Long.valueOf(attrs[0].toString()));
                userDto.setOrgId(Long.valueOf(attrs[1].toString()));
                userDto.setUsername(Optional.ofNullable(attrs[2]).map(Object::toString).orElse(""));
                if (attrs[3] != null) {
                    userDto.setDepartmentIds(ListHelper.parseDepartmentIds(attrs[3].toString()));
                }
                userDto.setActiveSession(true);
                return userDto;
            }
        } catch (NoResultException ex) {
            log.warn("share token not found {}", shareToken);
        }
        return null;
    }


}
