package org.befun.core.service;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.fillvalue.FillValue;
import org.befun.core.dto.fillvalue.FillValueList;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 通过（id或者属性id）查询对应的数据，然后用（id或者属性id）分组作为key存入map中
 */
public interface IGroupMapService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> {

    D mapToDto(E entity);

    R getRepository();

    default List<Long> convertToIds(List<D> list) {
        List<Long> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> ids.add(i.getId()));
        }
        return ids;
    }

    default Map<Long, D> convertToMap(List<D> list) {
        Map<Long, D> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> map.put(i.getId(), i));
        }
        return map;
    }


    //************************************************************
    //********************* fill value ***************************
    //************************************************************

    default <O> void fillDtoById(List<O> originList, FillValue<O, D> fillValue) {
        fillValueById(originList, this::mapToDto, List.of(fillValue));
    }

    default <O, V> void fillValueById(List<O> originList, Function<E, V> mapToValue, FillValue<O, V> fillValue) {
        fillValueById(originList, mapToValue, List.of(fillValue));
    }

    default <O, V> void fillValueById(List<O> originList, Function<E, V> mapToValue, FillValue<O, V> fillValue1, FillValue<O, V> fillValue2) {
        fillValueById(originList, mapToValue, List.of(fillValue1, fillValue2));
    }

    default <O, V> void fillValueById(List<O> originList, Function<E, V> mapToValue, List<FillValue<O, V>> fillValues) {
        if (CollectionUtils.isEmpty(originList) || CollectionUtils.isEmpty(fillValues)) {
            return;
        }
        Set<Long> ids = new HashSet<>();
        originList.forEach(o -> fillValues.forEach(m -> Optional.ofNullable(m.getRelationId().apply(o)).ifPresent(ids::add)));
        if (!ids.isEmpty()) {
            Map<Long, V> map = getGroupMapByIds(ids, BaseEntity::getId, mapToValue);
            if (MapUtils.isNotEmpty(map)) {
                originList.forEach(o -> {
                    fillValues.forEach(m -> {
                        Optional.ofNullable(m.getRelationId().apply(o))
                                .flatMap(id -> Optional.ofNullable(map.get(id)))
                                .ifPresent(v -> m.setRelationValue().accept(o, v));
                    });
                });
            }
        }
    }

    default <O extends BaseEntityDTO<?>> void fillDtoListByPropertyId(List<O> originList, FillValueList<O, E, D> fillValueList) {
        fillValueListByPropertyId(originList, this::mapToDto, fillValueList);
    }

    default <O extends BaseEntityDTO<?>, V> void fillValueListByPropertyId(List<O> originList, Function<E, V> mapToValue, FillValueList<O, E, V> fillValueList) {
        if (CollectionUtils.isEmpty(originList)) {
            return;
        }
        Set<Long> propertyIds = new HashSet<>();
        originList.forEach(o -> Optional.ofNullable(o.getId()).ifPresent(propertyIds::add));
        if (!propertyIds.isEmpty()) {
            Map<Long, List<V>> map = getGroupMapListByPropertyIds(fillValueList.getGroupPropertyName(), propertyIds, fillValueList.getGroupPropertyId(), mapToValue, ArrayList::new);
            if (MapUtils.isNotEmpty(map)) {
                originList.forEach(o -> {
                    Optional.ofNullable(map.get(o.getId())).ifPresent(l -> fillValueList.setRelationValueList().accept(o, l));
                });
            }
        }
    }

    //************************************************************
    //************************ group *****************************
    //************************************************************

    /**
     * 通过主键查询指定数据
     * 然后通过主键分组存入map中
     * map的key为主键，value为D
     */
    default Map<Long, D> getGroupMapByIds(Collection<Long> ids, Function<E, Long> getGroupProperty) {
        return getGroupMapByIds(ids, getGroupProperty, this::mapToDto);
    }

    /**
     * 通过主键查询指定数据
     * 然后通过主键分组存入map中
     * map的key为主键，value为V
     */
    default <V> Map<Long, V> getGroupMapByIds(Collection<Long> ids, Function<E, Long> getGroupProperty, Function<E, V> mapToV) {
        return getGroupMapByPropertyIds("id", ids, getGroupProperty, mapToV);
    }

    /**
     * 通过指定的属性id查询指定数据
     * 然后通过属性id分组存入map中
     * map的key为属性id，value为D
     */
    default Map<Long, D> getGroupMapByPropertyIds(String groupPropertyName, Collection<Long> ids, Function<E, Long> getGroupProperty) {
        return getGroupMapByPropertyIds(groupPropertyName, ids, getGroupProperty, this::mapToDto);
    }

    /**
     * 通过指定的属性id查询指定数据
     * 然后通过属性id分组存入map中
     * map的key为属性id，value为V
     */
    default <V> Map<Long, V> getGroupMapByPropertyIds(String groupPropertyName, Collection<Long> ids, Function<E, Long> getGroupProperty, Function<E, V> mapToV) {
        return Optional.of(new HashMap<Long, V>())
                .map(map ->
                        buildGroupMap(
                                map,
                                ids,
                                () -> {
                                    if ("id".equals(groupPropertyName)) {
                                        return getRepository().findAllById(ids);
                                    } else {
                                        return getRepository().findAll((a, b, c) -> a.get(groupPropertyName).in(ids));
                                    }
                                },
                                getGroupProperty,
                                mapToV,
                                map::put
                        ))
                .get();
    }

    /**
     * 通过主键查询指定数据
     * 然后通过主键分组存入map中
     * map的key为主键，value为List<D>
     */
    default Map<Long, List<D>> getGroupMapListByIds(Collection<Long> ids, Function<E, Long> getGroupProperty) {
        return getGroupMapListByIds(ids, getGroupProperty, this::mapToDto, ArrayList::new);
    }

    /**
     * 通过主键查询指定数据
     * 然后通过主键分组存入map中
     * map的key为主键，value为List<D>
     */
    default <V> Map<Long, List<V>> getGroupMapListByIds(Collection<Long> ids, Function<E, Long> getGroupProperty, Function<E, V> mapToV) {
        return getGroupMapListByIds(ids, getGroupProperty, mapToV, ArrayList::new);
    }

    /**
     * 通过主键查询指定数据
     * 然后通过主键分组存入map中
     * map的key为主键，value为List<D>
     */
    default <C extends Collection<D>> Map<Long, C> getGroupMapListByIds(Collection<Long> ids, Function<E, Long> getGroupProperty, Supplier<C> getCollection) {
        return getGroupMapListByIds(ids, getGroupProperty, this::mapToDto, getCollection);
    }

    /**
     * 通过主键查询指定数据
     * 然后通过主键分组存入map中
     * map的key为主键，List<V>
     */
    default <V, C extends Collection<V>> Map<Long, C> getGroupMapListByIds(Collection<Long> ids, Function<E, Long> getGroupProperty, Function<E, V> mapToV, Supplier<C> getCollection) {
        return getGroupMapListByPropertyIds("id", ids, getGroupProperty, mapToV, getCollection);
    }

    /**
     * 通过指定的属性id查询指定数据
     * 然后通过属性id分组存入map中
     * map的key为属性id，value为List<D>
     */
    default Map<Long, List<D>> getGroupMapListByPropertyIds(String groupPropertyName, Collection<Long> ids, Function<E, Long> getGroupProperty) {
        return getGroupMapListByPropertyIds(groupPropertyName, ids, getGroupProperty, this::mapToDto, ArrayList::new);
    }

    /**
     * 通过指定的属性id查询指定数据
     * 然后通过属性id分组存入map中
     * map的key为属性id，value为List<V>
     */
    default <V, C extends Collection<V>> Map<Long, C> getGroupMapListByPropertyIds(
            String groupPropertyName,
            Collection<Long> ids,
            Function<E, Long> getGroupProperty,
            Function<E, V> mapToV,
            Supplier<C> getCollection
    ) {
        return Optional.of(new HashMap<Long, C>())
                .map(map ->
                        buildGroupMap(
                                map,
                                ids,
                                () -> {
                                    if ("id".equals(groupPropertyName)) {
                                        return getRepository().findAllById(ids);
                                    } else {
                                        return getRepository().findAll((a, b, c) -> a.get(groupPropertyName).in(ids));
                                    }
                                },
                                getGroupProperty,
                                mapToV,
                                (property, dto) -> map.computeIfAbsent(property, k -> getCollection.get()).add(dto)
                        ))
                .get();
    }


    private <T, V> Map<Long, T> buildGroupMap(Map<Long, T> map,
                                              Collection<Long> ids,
                                              Supplier<List<E>> getEntityList,
                                              Function<E, Long> getGroupProperty,
                                              Function<E, V> mapToR,
                                              BiConsumer<Long, V> putMap) {
        if (CollectionUtils.isNotEmpty(ids)) {
            Optional.ofNullable(getEntityList.get()).ifPresent(l -> l.forEach(i -> {
                V dto = mapToR.apply(i);
                Long property = getGroupProperty.apply(i);
                if (property != null && dto != null) {
                    putMap.accept(property, dto);
                }
            }));
        }
        return map;
    }
}
