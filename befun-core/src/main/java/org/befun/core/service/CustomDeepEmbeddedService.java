package org.befun.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Consumer;

@Slf4j
public abstract class CustomDeepEmbeddedService<DE extends BaseEntity, DD extends BaseEntityDTO<DE>, R extends ResourceRepository<DE, Long>> extends AbstractService<DE, DD, R> {

    @Deprecated
    protected Object requireParent(long entityId) {
        return entityId;
    }

    public <S extends ResourceCustomQueryDto> Page<DD> findAllDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            S params) {
        throw new NotImplementedException();
    }

    protected Consumer<GenericSpecification<DE>> appendParentId(Long embeddedId, String deepEmbeddedMapperBy) {
        return s -> s.add(new ResourceQueryCriteria(deepEmbeddedMapperBy, embeddedId));
    }

    public Page<DD> findAllDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            ResourceEntityQueryDto<DD> queryDto) {
        return super.findAll(queryDto, appendParentId(embeddedId, embeddedFieldNameInDeep));
    }


    public CountDto countDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            ResourceEntityQueryDto<DD> queryDto) {
        return super.count(queryDto, appendParentId(embeddedId, embeddedFieldNameInDeep));
    }

    public DD findOneDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            Long deepId) {
        return super.findOne(deepId, appendParentId(embeddedId, embeddedFieldNameInDeep));
    }

    public DD createDeepEmbeddedMany(
            Long rootId,
            Long embeddedId,
            DeepEmbeddedFieldContext deepEmbeddedFieldContext,
            DD data) {
        return super.create(data, entity -> {
            Object parent = EntityUtility.requireEntityOrEntityId(crudService, embeddedId, deepEmbeddedFieldContext.embeddedFieldInDeep.getType());
            EntityUtility.setDeepEmbeddedProperty(entity, parent, deepEmbeddedFieldContext);
        });
    }

    public <SD extends BaseEntityDTO<DE>> DD createDeepEmbeddedMany2(
            Long rootId,
            Long embeddedId,
            DeepEmbeddedFieldContext deepEmbeddedFieldContext,
            SD data) {
        return super.create(data, entity -> {
            Object parent = EntityUtility.requireEntityOrEntityId(crudService, embeddedId, deepEmbeddedFieldContext.embeddedFieldInDeep.getType());
            EntityUtility.setDeepEmbeddedProperty(entity, parent, deepEmbeddedFieldContext);
        });
    }

    public DD updateOneDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            Long deepId,
            DD change) {
        return super.updateOne(deepId, change, appendParentId(embeddedId, embeddedFieldNameInDeep));
    }


    public <SD extends BaseEntityDTO<DE>> DD updateOneDeepEmbeddedMany2(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            Long deepId,
            SD change) {
        return super.updateOne(deepId, change, appendParentId(embeddedId, embeddedFieldNameInDeep));
    }

    public Boolean deleteOneDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            Long deepId
    ) {
        return super.deleteOne(deepId, appendParentId(embeddedId, embeddedFieldNameInDeep));
    }

    public List<DD> batchUpdateDeepEmbeddedMany(
            Long rootId,
            String rootFieldNameInEmbedded,
            Long embeddedId,
            String embeddedFieldNameInDeep,
            ResourceBatchUpdateRequestDto<DD> batchChangeDto) {
        return super.batchUpdate(batchChangeDto);
    }
}
