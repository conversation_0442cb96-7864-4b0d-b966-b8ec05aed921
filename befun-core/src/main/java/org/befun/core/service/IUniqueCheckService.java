package org.befun.core.service;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.EntityScopeStrategyTypes;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.TenantContext;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Predicate;
import java.util.function.Supplier;

import static org.befun.core.constant.EntityScopeStrategyType.NONE;

/**
 * 唯一性检测
 * 企业内唯一
 * 所有企业唯一
 */
public interface IUniqueCheckService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> {

    Class<E> getEntityClass();

    R getRepository();


    /**
     * 所有企业唯一
     *
     * @param excludeEntity 修改的时候，需要排除自己
     */
    default boolean isUniqueInAllOrg(String property, Object value, E excludeEntity) {
        if (StringUtils.isEmpty(property) || value == null) {
            return false;
        }
        Long id = excludeEntity != null && excludeEntity.getId() != null ? excludeEntity.getId() : null;
        Specification<E> specification = (root, query, builder) -> {
            Predicate p2 = builder.equal(root.get(property), value); // 属性相等
            if (id != null) {
                Predicate p3 = builder.notEqual(root.get("id"), id); // 排除指定id
                return builder.and(p2, p3);
            } else {
                return builder.and(p2);
            }
        };
        return scopeQuery(NONE, () -> getRepository().count(specification) <= 0);
    }

    /**
     * 企业内唯一
     *
     * @param excludeEntity 修改的时候，需要排除自己
     */
    default boolean isUniqueInOrg(Long orgId, String property, Object value, E excludeEntity) {
        if (orgId == null || StringUtils.isEmpty(property) || value == null) {
            return false;
        }
        Long id = excludeEntity != null && excludeEntity.getId() != null ? excludeEntity.getId() : null;
        Specification<E> specification = (root, query, builder) -> {
            Predicate p1 = builder.equal(root.get("orgId"), orgId); // 企业id相等
            Predicate p2 = builder.equal(root.get(property), value); // 属性相等
            if (id != null) {
                Predicate p3 = builder.notEqual(root.get("id"), id); // 排除指定id
                return builder.and(p1, p2, p3);
            } else {
                return builder.and(p1, p2);
            }
        };
        return scopeQuery(NONE, () -> getRepository().count(specification) <= 0);
    }

    default <Q> Q scopeQuery(EntityScopeStrategyTypes scope, Supplier<Q> query) {
        TenantContext.addCustomEntityScopeStrategy(getEntityClass(), scope);
        Q result = query.get();
        TenantContext.clearCustomEntityScopeStrategy(getEntityClass());
        return result;
    }
}
