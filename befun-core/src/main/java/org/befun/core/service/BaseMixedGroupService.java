package org.befun.core.service;

import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.EntityWithGroupDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.beans.factory.annotation.Autowired;


public abstract class BaseMixedGroupService<
        GE extends BaseEntity,
        GD extends BaseEntityDTO<GE>,
        GR extends ResourceRepository<GE, Long>,
        RE extends BaseEntity,
        RD extends BaseEntityDTO<RE> & EntityWithGroupDTO<GD>,
        RS extends BaseMixedResourceService<RE, RD, ?, GE, GD, ?>>
        extends BaseNoPageService<GE, GD, GR> {

    @Autowired
    private RS mixedDataService;

    @Override
    protected void afterExecuteMethod(ResourceMethod method, GE entity, GD dto) {
        if (method == ResourceMethod.DELETE_ONE) {
            mixedDataService.updateGroupNullByGroupId(entity.getId());
        }
    }


}
