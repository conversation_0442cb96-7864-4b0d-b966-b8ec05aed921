package org.befun.core.service;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.constant.CustomQueryType;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.constant.EntityScopeStrategyTypes;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountItemDto;
import org.befun.core.dto.EntityWithGroupDTO;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceCustomQueryWithGroupDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.GroupEntityHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaUpdate;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.stream.Collectors;

import static org.befun.core.constant.CustomQueryType.*;

/**
 * 自动查询聚合了目录和数据的列表
 * 1 数据service继承这个类，实现 groupPropertyName()，返回目录的属性名
 * 2 目录service继承BaseMixedGroupService
 * 3 findAll使用自定义参数的实现方法，自定义参数继承ResourceCustomQueryWithGroupDto
 */
public abstract class BaseMixedResourceService<
        RE extends BaseEntity,
        RD extends BaseEntityDTO<RE> & EntityWithGroupDTO<GD>,
        RR extends ResourceRepository<RE, Long>,
        GE extends BaseEntity,
        GD extends BaseEntityDTO<GE>,
        GS extends BaseMixedGroupService<GE, GD, ?, RE, RD, ?>>
        extends BaseService<RE, RD, RR> {

    @Autowired
    private GS mixedGroupService;
    @Autowired
    private EntityManager entityManager;

    public String groupPropertyName() {
        return "groupId";
    }

    @Override
    @SuppressWarnings("unchecked")
    public <S extends ResourceCustomQueryDto> Page<RD> findAll(S params) {
        ResourceCustomQueryWithGroupDto<RE, RD, GE, GD> dto = (ResourceCustomQueryWithGroupDto<RE, RD, GE, GD>) params;
        CustomQueryType queryType = dto.customQueryType();
        if (queryType == ALL) {
            if (currentIsAdmin()) {
                return findAllByEntityScope(EntityScopeStrategyType.ORGANIZATION, dto.transformQueryResource(Set.of()));
            } else {
                queryType = OWNER_SHARE;
            }
        } else if (queryType == ALL_WITH_GROUP) {
            if (currentIsAdmin()) {
                return findAllWithGroup(EntityScopeStrategyType.ORGANIZATION, EntityScopeStrategyType.ORGANIZATION, dto);
            } else {
                queryType = OWNER_SHARE_WITH_GROUP;
            }
        }
        switch (queryType) {
            case OWNER -> {
                // 创建人的所有数据
                return findAllByEntityScope(EntityScopeStrategyType.OWNER, dto.transformQueryResource(Set.of()));
            }
            case OWNER_WITH_GROUP -> {
                // 创建人的所有目录 + 创建人的所有数据
                return findAllWithGroup(EntityScopeStrategyType.OWNER, EntityScopeStrategyType.OWNER, dto);
            }
            case SHARE -> {
                // 分享给我的所有数据 + 分享给我的目录里面的数据
                return findAllByEntityScope(EntityScopeStrategyType.GROUP_CORPORATION, dto.transformQueryResource(Set.of()));
            }
            case SHARE_WITH_GROUP -> {
                // 分享给我的目录 + 分享给我的所有数据（不再分享给我的目录中）
                return findAllWithGroup(EntityScopeStrategyType.GROUP_CORPORATION, EntityScopeStrategyType.CORPORATION, dto);
            }
            case OWNER_SHARE -> {
                // 创建人的所有数据 + 分享给我的所有数据 + 分享给我的目录里面的数据
                return findAllByEntityScope(EntityScopeStrategyType.OWNER_GROUP_CORPORATION, dto.transformQueryResource(Set.of()));
            }
            case OWNER_SHARE_WITH_GROUP -> {
                // 创建人的所有目录 + 创建人的所有数据 + 分享给我的目录 + 分享给我的所有数据（不再分享给我的目录中）
                return findAllWithGroup(EntityScopeStrategyType.OWNER_GROUP_CORPORATION, EntityScopeStrategyType.OWNER_CORPORATION, dto);
            }
        }
        return Page.empty();
    }

    private Page<RD> findAllByEntityScope(EntityScopeStrategyTypes scope, ResourceEntityQueryDto<RD> queryDto) {
        return scopeQuery(scope, () -> findAll(queryDto));
    }

    private Page<RD> findAllWithGroup(EntityScopeStrategyTypes dataScope, EntityScopeStrategyTypes groupScope, ResourceCustomQueryWithGroupDto<RE, RD, GE, GD> dto) {
        Set<Long> excludeGroupIds = new HashSet<>();
        List<GE> groupList = mixedGroupService.scopeQuery(groupScope, () -> mixedGroupService.findAllEntities(dto.transformQueryGroup()));
        if (CollectionUtils.isNotEmpty(groupList)) {
            groupList.forEach(i -> excludeGroupIds.add(i.getId()));
        }
        return GroupEntityHelper.findAllWithGroup(dto.getPage(), dto.getLimit(), groupList,
                (pageStartOffset, pageNumber, pageSize) -> {
                    ResourceEntityQueryDto<RD> queryDto = dto.transformQueryResource(excludeGroupIds);
                    queryDto.setPageStartOffset(pageStartOffset);
                    queryDto.setPage(pageNumber);
                    queryDto.setLimit(pageSize);
                    return findAllByEntityScope(dataScope, queryDto);
                }, this::mockGroupToData);
    }

    private List<RD> mockGroupToData(List<GE> entityList) {
        List<GD> dtoList = mixedGroupService.mapToDto(entityList);
        if (CollectionUtils.isNotEmpty(dtoList)) {
            mixedGroupService.afterMapToDto(entityList, dtoList);
            return dtoList.stream().map(i -> {
                try {
                    RD rd = dtoClass.getConstructor().newInstance();
                    rd.setGroup(i);
                    rd.setItemType(2);
                    return rd;
                } catch (Throwable e) {
                    throw new RuntimeException(dtoClass.getName() + "缺少无参构造函数");
                }
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 查询指定的目录下的数据的条数
     */
    public Map<Long, Integer> getGroupCountMap(Set<Long> groupIds) {
        Map<Long, Integer> countMap = new HashMap<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return countMap;
        }
        GenericSpecification<RE> specification = new GenericSpecification<>();
        specification.add(new ResourceQueryCriteria(groupPropertyName(), groupIds, QueryOperator.IN));
        List<CountItemDto> countItems = repository.countBy(groupPropertyName(), specification);
        if (CollectionUtils.isNotEmpty(countItems)) {
            countItems.forEach(i -> {
                if (i.getCount() != null) {
                    Long key = NumberUtils.isDigits(i.getName()) ? Long.parseLong(i.getName()) : null;
                    countMap.put(key, i.getCount().intValue());
                }
            });
        }
        return countMap;
    }

    /**
     * 修改指定的数据的目录，支持修改为null，targetGroupId == 0 会转换为null
     */
    @Transactional
    public boolean updateGroup(Long targetGroupId, Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        if (targetGroupId != null) {
            if (targetGroupId == 0) {
                targetGroupId = null;
            } else {
                mixedGroupService.checkIsCurrentOrgAndUser(targetGroupId);
            }
        }
        Long orgId = TenantContext.requireCurrentTenant();
        Long userId = TenantContext.requireCurrentUserId();
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaUpdate<RE> criteriaUpdate = builder.createCriteriaUpdate(entityClass);
        Root<RE> root = criteriaUpdate.from(entityClass);
        Path<Long> orgIdProperty = root.get("orgId");
        Path<Long> userIdProperty = root.get("userId");
        Path<Long> groupProperty = root.get(groupPropertyName());
        criteriaUpdate.set(groupProperty, targetGroupId).where(
                builder.equal(orgIdProperty, orgId),
                builder.equal(userIdProperty, userId),
                root.get("id").in(ids));
        entityManager.createQuery(criteriaUpdate).executeUpdate();
        return true;
    }

    /**
     * 删除分类之后，需要清除数据的分类关系id
     */
    @Transactional
    public void updateGroupNullByGroupId(Long groupId) {
        Long orgId = TenantContext.requireCurrentTenant();
        Long userId = TenantContext.requireCurrentUserId();
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaUpdate<RE> criteriaUpdate = builder.createCriteriaUpdate(entityClass);
        Root<RE> root = criteriaUpdate.from(entityClass);
        Path<Long> orgIdProperty = root.get("orgId");
        Path<Long> userIdProperty = root.get("userId");
        Path<Long> groupProperty = root.get(groupPropertyName());
        criteriaUpdate.set(groupProperty, (Long) null).where(
                builder.equal(orgIdProperty, orgId),
                builder.equal(userIdProperty, userId),
                builder.equal(groupProperty, groupId));
        entityManager.createQuery(criteriaUpdate).executeUpdate();
    }

}
