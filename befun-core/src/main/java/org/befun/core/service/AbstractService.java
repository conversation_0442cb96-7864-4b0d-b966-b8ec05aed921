package org.befun.core.service;

import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.*;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.rest.query.PageResult;
import org.befun.core.utils.TypeUtility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;

import static org.befun.core.constant.EntityScopeStrategyType.OWNER_CORPORATION;
import static org.befun.core.constant.EntityScopeStrategyType.OWNER_FOLDER_CORPORATION;
import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@SuppressWarnings("unchecked")
public abstract class AbstractService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>>
        implements IGroupMapService<E, D, R>, IUniqueCheckService<E, D, R> {

    @Lazy
    @Autowired
    protected MapperService mapperService;
    @Autowired
    protected R repository;
    @Autowired
    protected CrudService crudService;
    @Autowired
    protected ResourceCorporationService resourceCorporationService;
    protected Class<E> entityClass;
    protected Class<D> dtoClass;
    protected boolean isCorpResource = false;

    @Override
    public R getRepository() {
        return repository;
    }

    @Override
    public Class<E> getEntityClass() {
        return entityClass;
    }

    public AbstractService() {
        entityClass = (Class<E>) TypeUtility.getTypeParameterType(this.getClass(), AbstractService.class, 0);
        dtoClass = (Class<D>) TypeUtility.getTypeParameterType(this.getClass(), AbstractService.class, 1);
        setup();
    }

    public void setup() {
        EntityScopeStrategy strategy = entityClass.getAnnotation(EntityScopeStrategy.class);
        this.isCorpResource = strategy != null && Arrays.stream(strategy.value()).anyMatch(i -> i == OWNER_CORPORATION || i == OWNER_FOLDER_CORPORATION);
    }

    public E get(Long id) {
        if (id == null || id == 0) {
            return null;
        }
        return repository.findById(id).orElse(null);
    }

    public E require(Long id) {
        E entity = get(id);
        if (entity == null) {
            throw new EntityNotFoundException(entityClass);
        }
        return entity;
    }

    public E getWithFilter(Long id) {
        if (id == null || id == 0) {
            return null;
        }
        return repository.findOne((root, query, builder) -> builder.equal(root.get("id"), id)).orElse(null);
    }

    public E requireWithFilter(Long id) {
        E entity = getWithFilter(id);
        if (entity == null) {
            throw new EntityNotFoundException(entityClass);
        }
        return entity;
    }

    public void checkExistsEntity(Long id) {
        if (!repository.existsById(id)) {
            throw new EntityNotFoundException(entityClass);
        }
    }

    public void checkIsCurrentOrg(E entity) {
        Long orgId;
        if ((orgId = TenantContext.getCurrentTenant()) != null
                && entity instanceof EnterpriseEntity
                && orgId.equals(((EnterpriseEntity) entity).getOrgId())) {
            return;
        }
        throw new EntityNotFoundException(entityClass);
    }
    public void checkIsCurrentOrg(E entity, Long publicOrgId) {
        Long orgId;
        if ((orgId = TenantContext.getCurrentTenant()) != null
                && entity instanceof EnterpriseEntity
                && (orgId.equals(((EnterpriseEntity) entity).getOrgId()) || publicOrgId.equals(((EnterpriseEntity) entity).getOrgId())) ) {
            return;
        }
        throw new EntityNotFoundException(entityClass);
    }

    public void checkIsCurrentOrgAndUser(E entity) {
        Long orgId, userId;
        if ((orgId = TenantContext.getCurrentTenant()) != null
                && (userId = TenantContext.getCurrentUserId()) != null
                && entity instanceof EnterpriseOwnerEntity
                && orgId.equals(((EnterpriseEntity) entity).getOrgId())
                && userId.equals(((EnterpriseOwnerEntity) entity).getUserId())
        ) {
            return;
        }
        throw new EntityNotFoundException(entityClass);
    }

    public E checkIsCurrentOrg(Long id) {
        E entity = require(id);
        checkIsCurrentOrg(entity);
        return entity;
    }

    public E checkIsCurrentOrgAndUser(Long id) {
        E entity = require(id);
        checkIsCurrentOrgAndUser(entity);
        return entity;
    }

    public boolean currentIsAdmin() {
        return Optional.ofNullable(TenantContext.getCurrentIsAdmin()).orElse(false);
    }

    public void checkCurrentIsAdmin() {
        if (!currentIsAdmin()) {
            throw new BadRequestException("无权限");
        }
    }

    public boolean currentHasPermissions(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return false;
        }
        return Optional.ofNullable(TenantContext.getCurrentPermissions())
                .map(UserPermissions::getAction)
                .map(l -> Arrays.stream(permissions).allMatch(l::contains))
                .orElse(false);
    }

    public void checkCurrentHasPermissions(String... permissions) {
        if (!currentHasPermissions(permissions)) {
            throw new BadRequestException("无权限");
        }
    }

    public List<E> findAllEntities() {
        return repository.findAll();
    }

    public List<E> findAllEntities(ResourceEntityQueryDto<D> queryDto) {
        return repository.findAll(new GenericSpecification<>(queryDto), queryDto.getSorts() == null ? Sort.unsorted() : queryDto.getSorts());
    }

    public E save(E entity) {
        return repository.save(entity);
    }

    public List<E> saveAll(Iterable<E> entities) {
        return repository.saveAll(entities);
    }

    public void delete(E entity) {
        repository.delete(entity);
    }

    public void deleteAll(Iterable<E> entities) {
        repository.deleteAll(entities);
    }

    //************************************************************
    //********************** callback ****************************
    //************************************************************

    /**
     * 实体转换为DTO
     * {@link ResourceMethod#FIND_ONE}
     * {@link ResourceMethod#CREATE}
     * {@link ResourceMethod#UPDATE_ONE}
     */
    @Override
    public D mapToDto(E entity) {
        return crudService.mapToDto(entity, dtoClass);
    }

    /**
     * 实体转换为DTO
     * {@link ResourceMethod#FIND_ALL}
     * {@link ResourceMethod#BATCH_UPDATE}
     */
    public List<D> mapToDto(List<E> entity) {
        return crudService.mapToDto(entity, dtoClass);
    }

    /**
     * callback method
     * {@link ResourceMethod#FIND_ONE}
     */
    public void afterMapToDto(E entity, D dto) {
        if (entity != null && dto != null) {
            afterMapToDto(List.of(entity), List.of(dto));
        }
    }

    /**
     * callback method
     * {@link ResourceMethod#FIND_ALL}
     */
    public void afterMapToDto(List<E> entity, List<D> dto) {
        fillResourcePermissions(dto);
    }

    /**
     * callback method
     * {@link ResourceMethod#CREATE}
     * {@link ResourceMethod#UPDATE_ONE}
     * {@link ResourceMethod#DELETE_ONE} dto 为 null
     */
    protected void afterExecuteMethod(ResourceMethod method, E entity, @Nullable D dto) {
        // nothing
    }

    /**
     * callback method
     * {@link ResourceMethod#CREATE}
     * {@link ResourceMethod#UPDATE_ONE}
     * {@link ResourceMethod#BATCH_UPDATE}
     *
     * @return true 不会调用默认的 {@link CrudService#mapToEntity(BaseEntityDTO, BaseEntity)} ; false 会继续调用
     */
    protected <S extends BaseEntityDTO<E>> boolean beforeMapToEntity(ResourceMethod method, S data, E entity) {
        // nothing
        return false;
    }

    public D afterCustomCreateOrUpdateOne(E entity) {
        D dto = mapToDto(entity);
        afterMapToDto(entity, dto);
        return dto;
    }

    //************************************************************
    //****************** collection crud *************************
    //************************************************************

    protected Page<D> findAll(Specification<E> specification, PageRequest pageRequest) {
        Page<E> result = repository.findAll(specification, pageRequest);
        List<E> entityList = result.toList();
        List<D> dtoList = crudService.baseFindAll(entityList, dtoClass, this::mapToDto, this::afterMapToDto);
        return new PageResult<>(dtoList, pageRequest, result.getTotalElements());
    }

    protected List<D> findAllNoPage(Specification<E> specification, Sort sort) {
        List<E> entityList = repository.findAll(specification, sort);
        return crudService.baseFindAll(entityList, dtoClass, this::mapToDto, this::afterMapToDto);
    }

    Page<D> findAll(ResourceEntityQueryDto<D> queryDto, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.findAll(queryDto, entityClass, dtoClass, repository, appendCondition, this::mapToDto, this::afterMapToDto);
    }

    List<D> findAllNoPage(ResourceEntityQueryDto<D> queryDto, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.findAllNoPage(queryDto, entityClass, dtoClass, repository, appendCondition, this::mapToDto, this::afterMapToDto);
    }

    CountDto count(ResourceEntityQueryDto<D> queryDto, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.count(queryDto, entityClass, dtoClass, repository, appendCondition);
    }

    D findOne(long id, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.findOne(id, entityClass, dtoClass, repository, appendCondition, this::mapToDto, this::afterMapToDto);
    }

    private BiConsumer<E, D> afterExecuteMethodFun(ResourceMethod method) {
        return (e, d) -> afterExecuteMethod(method, e, d);
    }

    private <S extends BaseEntityDTO<E>> BiFunction<S, E, Boolean> beforeMapToEntityFun(ResourceMethod method) {
        return (s, e) -> beforeMapToEntity(method, s, e);
    }

    <S extends BaseEntityDTO<E>> D create(S data, Consumer<E> setProperty) {
        return crudService.create(data, entityClass, dtoClass, repository, setProperty, beforeMapToEntityFun(CREATE), this::mapToDto, afterExecuteMethodFun(CREATE));
    }

    <S extends BaseEntityDTO<E>> D updateOne(long id, S change, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.updateOne(id, change, entityClass, dtoClass, repository, appendCondition, beforeMapToEntityFun(UPDATE_ONE), this::mapToDto, afterExecuteMethodFun(UPDATE_ONE));
    }

    Boolean deleteOne(long id, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.deleteOne(id, entityClass, dtoClass, repository, appendCondition, afterExecuteMethodFun(DELETE_ONE));
    }

    <S extends BaseEntityDTO<E>> List<D> batchUpdate(ResourceBatchUpdateRequestDto<S> batchChangeDto) {
        return crudService.batchUpdate(batchChangeDto, entityClass, dtoClass, repository, beforeMapToEntityFun(BATCH_UPDATE), this::mapToDto);
    }

    //************************************************************
    //******************** single crud ***************************
    //************************************************************

    D findAllSingle(Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.findAllSingle(entityClass, dtoClass, repository, appendCondition, this::mapToDto, this::afterMapToDto);
    }

    <S extends BaseEntityDTO<E>> D createSingle(S data, Consumer<GenericSpecification<E>> appendCondition, Consumer<E> setProperty) {
        return crudService.createSingle(data, entityClass, dtoClass, repository, appendCondition, setProperty, beforeMapToEntityFun(CREATE), this::mapToDto, afterExecuteMethodFun(CREATE));
    }

    <S extends BaseEntityDTO<E>> D updateOneSingle(S change, Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.updateOneSingle(change, entityClass, dtoClass, repository, appendCondition, beforeMapToEntityFun(UPDATE_ONE), this::mapToDto, afterExecuteMethodFun(UPDATE_ONE));
    }

    Boolean deleteOneSingle(Consumer<GenericSpecification<E>> appendCondition) {
        return crudService.deleteOneSingle(entityClass, dtoClass, repository, appendCondition, afterExecuteMethodFun(DELETE_ONE));
    }

    //************************************************************
    //******************** permissions ***************************
    //************************************************************

    /**
     * 设置返回列表中的数据权限信息
     * {@link IResourceInfoService} 需要存在此接口的实现类
     * auth中的ResourcePermissionService已实现此接口
     */
    public void fillResourcePermissions(List<D> dto) {
        if (CollectionUtils.isNotEmpty(dto)
                && isCorpResource
                && BaseResourcePermissionEntityDto.class.isAssignableFrom(dtoClass)) {
            List<? extends BaseResourcePermissionEntityDto<?>> dtoList = (List<? extends BaseResourcePermissionEntityDto<?>>) dto;
            resourceCorporationService.fillResourcePermissionInfo(dtoList);
        }
    }

}
