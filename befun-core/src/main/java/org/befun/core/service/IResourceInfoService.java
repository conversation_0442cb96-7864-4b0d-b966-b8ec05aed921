package org.befun.core.service;


import org.befun.core.dto.ResourcePermissionDepartmentDto;
import org.befun.core.dto.ResourcePermissionRoleDto;
import org.befun.core.dto.ResourcePermissionUserDto;

import java.util.List;
import java.util.Set;

public interface IResourceInfoService {

    List<ResourcePermissionUserDto> getPermissionUsers(Set<Long> userIds);

    List<ResourcePermissionRoleDto> getPermissionRoles(Set<Long> roleIds);

    List<ResourcePermissionDepartmentDto> getPermissionDepartments(Set<Long> departmentIds);
}

