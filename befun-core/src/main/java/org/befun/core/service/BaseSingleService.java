package org.befun.core.service;

import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;

import java.util.List;

/**
 * The class description
 * 输出的必须都是D，也就是DTO层的数据，隐藏Entity细节
 *
 * <AUTHOR>
 */
public abstract class BaseSingleService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> extends AbstractService<E, D, R> {

    public E findOne() {
        List<E> result = repository.findAll();
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    public D findAll() {
        return super.findAllSingle(null);
    }

    public <S extends BaseEntityDTO<E>> D create(S data) {
        return super.createSingle(data, null, null);
    }

    public <S extends BaseEntityDTO<E>> D updateOne(S change) {
        return super.updateOneSingle(change, null);
    }

    public Boolean deleteOne() {
        return super.deleteOneSingle(null);
    }
}
