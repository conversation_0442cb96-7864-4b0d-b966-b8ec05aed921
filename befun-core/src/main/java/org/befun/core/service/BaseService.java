package org.befun.core.service;

import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * The class description
 * 输出的必须都是D，也就是DTO层的数据，隐藏Entity细节
 *
 * <AUTHOR>
 */
public abstract class BaseService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> extends AbstractEmbeddedService<E, D, R> {

    /**
     * instance action entrypoint
     */
    public Object executeInstanceAction(Long id, Function<D, ?> function) {
        Optional<E> object = repository.findById(id);
        if (object.isEmpty()) {
            throw new EntityNotFoundException(entityClass);
        }
        D dto = mapToDto(object.get());
        dto.setEntity(object.get());
        return function.apply(dto);
    }

    /**
     * instance action entrypoint
     */
    public Object executeInstanceAction2(Long id, Function<E, ?> function) {
        Optional<E> object = repository.findById(id);
        if (object.isEmpty()) {
            throw new EntityNotFoundException(entityClass);
        }
        return function.apply(object.get());
    }

    /**
     * delegage to repository->save
     */
    public <S extends BaseEntityDTO<E>> D save(S dto) {
        Assert.notNull(dto.getEntity(), "can't save dto directly");
        E entity = dto.getEntity();
        crudService.mapToEntity(dto, entity);
        E saved = repository.save(entity);
        return mapToDto(saved);
    }

    public <S extends ResourceCustomQueryDto> Page<D> findAll(S params) {
        throw new NotImplementedException();
    }

    public Page<D> findAll(ResourceEntityQueryDto<D> queryDto) {
        return super.findAll(queryDto, null);
    }

    public CountDto count(ResourceEntityQueryDto<D> queryDto) {
        return super.count(queryDto, null);
    }

    public <S extends BaseEntityDTO<E>> D create(S data) {
        return super.create(data, setProperty());
    }

    public D findOne(long id) {
        return super.findOne(id, null);
    }

    public <S extends BaseEntityDTO<E>> D updateOne(long id, S change) {
        return super.updateOne(id, change, null);
    }

    public Boolean deleteOne(long id) {
        return super.deleteOne(id, null);
    }

    public <S extends BaseEntityDTO<E>> List<D> batchUpdate(ResourceBatchUpdateRequestDto<S> batchChangeDto) {
        return super.batchUpdate(batchChangeDto);
    }

}
