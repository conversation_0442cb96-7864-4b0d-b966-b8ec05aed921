package org.befun.core.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.UserDto;
import org.befun.core.exception.OverLimitException;
import org.befun.core.limiter.RedisBaseBucketLimiter;
import org.befun.core.security.UserPrincipal;
import org.befun.core.utils.RestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResourceLimiterService {
    private final String prefix = "limiter.";

    @Autowired
    private RedisBaseBucketLimiter limiter;

    /**
     * buildContext: for method annotation, please check @PreResourceLimiter, @PostResourceLimiter
     * @param parameterNames
     * @param args
     * @return
     */
    public StandardEvaluationContext buildContext(String[] parameterNames, Object[] args) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }

        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication instanceof UsernamePasswordAuthenticationToken) {
            UserPrincipal principal = (UserPrincipal) authentication.getPrincipal();
            context.setVariable("user", principal.getUser());
        }

        // 附加额外数据
        context.setVariable("ip", RestUtils.getClientIpAddress(request));

        return context;
    }

    /**
     * parseAsBoolean
     */
    public Boolean parseAsBool(StandardEvaluationContext context, String key) {
        ExpressionParser parser = new SpelExpressionParser();
        return parser.parseExpression(key).getValue(context, Boolean.class);
    }

    /**
     * parseAsString
     */
    public String parseAsString(StandardEvaluationContext context, String key) {
        ExpressionParser parser = new SpelExpressionParser();
        return parser.parseExpression(key).getValue(context, String.class);
    }

    /**
     * parseAsBool
     */
    public Integer parseAsInt(StandardEvaluationContext context, String key) {
        ExpressionParser parser = new SpelExpressionParser();
        return parser.parseExpression(key).getValue(context, Integer.class);
    }

    /**
     * tryConsume by check all limiter
     * @param key
     * @param number
     * @param max
     * @throws OverLimitException
     */
    public Boolean tryConsume(String key, int number, int max) throws OverLimitException {
        return limiter.tryConsume(prefix + key, number, max);
    }

    /**
     * canConsume by specified limiter.
     * @param key
     * @param number
     * @param max
     * @return
     * @throws OverLimitException
     */
    public boolean canConsume(String key, int number, int max) throws OverLimitException {
        return limiter.canConsume(prefix + key, number, max);
    }

    /**
     *
     * @param key
     * @param number
     * @return
     */
    public Integer revert(String key, int number) {
        return limiter.revert(prefix + key, number);
    }

    /**
     *
     * @param key
     * @return
     */
    public Integer balance(String key) {
        return limiter.balance(prefix + key);
    }


    /**
     * reset
     * @param key
     * @return
     */
    public void reset(String key, int number) {
        limiter.reset(prefix + key, number);
    }
}
