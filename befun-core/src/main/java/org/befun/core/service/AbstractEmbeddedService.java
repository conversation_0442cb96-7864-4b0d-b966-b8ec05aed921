package org.befun.core.service;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.utils.EntityUtility;
import org.springframework.util.Assert;

import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;
import static org.befun.core.rest.context.FieldRelationType.ONE_TO_ONE;

public class AbstractEmbeddedService<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>>
        extends AbstractService<E, D, R>
        implements IEmbeddedOneService, IEmbeddedManyService, IEmbeddedManyDeepService {

    private Map<String, EmbeddedFieldContext> relationMaps;
    private final EmbeddedWrap embeddedWrap = new EmbeddedWrap();

    public void setup() {
        super.setup();
        this.relationMaps = new HashMap<>();
        calcRelationMap();
        addCustomRelations(relationMaps);
        calcDeepRelationMap();
        addCustomDeepRelations(relationMaps);
    }

    private void calcRelationMap() {
        Field[] fields = entityClass.getDeclaredFields();
        for (Field embeddedFieldInRoot : fields) {
            OneToMany otm = embeddedFieldInRoot.getAnnotation(OneToMany.class);
            if (otm != null) {
                relationMaps.put(embeddedFieldInRoot.getName(), new EmbeddedFieldContext(embeddedFieldInRoot, otm.mappedBy(), ONE_TO_MANY));
            }
            OneToOne oto = embeddedFieldInRoot.getAnnotation(OneToOne.class);
            if (oto != null && StringUtils.isNotEmpty(oto.mappedBy())) {
                relationMaps.put(embeddedFieldInRoot.getName(), new EmbeddedFieldContext(embeddedFieldInRoot, oto.mappedBy(), ONE_TO_ONE));
            }
        }
    }

    private void calcDeepRelationMap() {
        relationMaps.values().forEach(fieldContext -> {
            if (fieldContext.embeddedFieldInRoot != null && fieldContext.relationType == ONE_TO_MANY) {
                Type type = fieldContext.embeddedFieldInRoot.getGenericType(); // List<E>
                if (type instanceof ParameterizedType) {
                    Type[] argTypes = ((ParameterizedType) type).getActualTypeArguments();
                    if (argTypes.length == 1) {
                        Field[] fields = ((Class<?>) argTypes[0]).getDeclaredFields(); // E.class.getDeclaredFields()
                        for (Field deepFieldInEmbedded : fields) {
                            OneToMany otm = deepFieldInEmbedded.getAnnotation(OneToMany.class);
                            if (otm != null) {
                                fieldContext.deepRelationMaps.put(deepFieldInEmbedded.getName(), new DeepEmbeddedFieldContext(deepFieldInEmbedded, ONE_TO_MANY, otm.mappedBy(), fieldContext));
                            }
                        }
                    }
                }
            }
        });
    }
    //************************************************************
    //******************** setProperty ***************************
    //************************************************************

    Consumer<E> setProperty() {
        return entity -> EntityUtility.setProperty(entity, relationMaps.values());
    }

    protected void addCustomRelations(Map<String, EmbeddedFieldContext> relationMaps) {
    }

    protected void addCustomDeepRelations(Map<String, EmbeddedFieldContext> relationMaps) {
    }

    @Override
    public AbstractEmbeddedService<?, ?, ?>.EmbeddedWrap getWrap() {
        return embeddedWrap;
    }

    class EmbeddedWrap {

        EmbeddedFieldContext requireEmbeddedOneFieldContext(String fieldNameInRoot) {
            EmbeddedFieldContext entityFieldContext = relationMaps.get(fieldNameInRoot);
            Assert.notNull(entityFieldContext, "invalid relation field");
            Assert.isTrue(entityFieldContext.relationType == ONE_TO_ONE, "invalid relation field");
            return entityFieldContext;
        }

        EmbeddedFieldContext requireEmbeddedManyFieldContext(String fieldNameInRoot) {
            EmbeddedFieldContext entityFieldContext = relationMaps.get(fieldNameInRoot);
            Assert.notNull(entityFieldContext, "invalid relation field");
            Assert.isTrue(entityFieldContext.relationType == ONE_TO_MANY, "invalid relation field");
            return entityFieldContext;
        }

        DeepEmbeddedFieldContext requireDeepEmbeddedFieldContext(String fieldNameInRoot, String fieldNameInEmbedded) {
            EmbeddedFieldContext entityFieldContext = requireEmbeddedManyFieldContext(fieldNameInRoot);
            DeepEmbeddedFieldContext embeddedFieldContext = entityFieldContext.deepRelationMaps.get(fieldNameInEmbedded);
            Assert.notNull(embeddedFieldContext, "invalid relation field");
            Assert.isTrue(embeddedFieldContext.relationType == ONE_TO_MANY, "invalid relation field");
            return embeddedFieldContext;
        }

        Class<? extends BaseEntity> getEntityClass() {
            return entityClass;
        }

        CrudService getCrudService() {
            return crudService;
        }

    }
}
