package org.befun.core.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;

import static java.time.format.DateTimeFormatter.ISO_OFFSET_DATE_TIME;

public class DateHelper {

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter MINUTE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    private static final DateTimeFormatter DATE_TIME_PARSER = DateTimeFormatter.ofPattern("yyyy-M-d H:m:s");
    private static final DateTimeFormatter DATE_PARSER = DateTimeFormatter.ofPattern("yyyy-M-d");

    public static void main(String[] args) {
        System.out.println(formatOffset(LocalDateTime.now()));
        System.out.println(formatOffset(LocalDate.now()));
        System.out.println(formatOffset(new Date()));
        System.out.println(parseZoneDateTime("2022-11-08T15:14:24+08:00").toString());
        System.out.println(formatMinute(new Date()));

    }

    public static <D> String formatOffset(D dateTime) {
        if (dateTime == null) {
            return null;
        }
        LocalDateTime localDateTime = null;
        ZonedDateTime zonedDateTime = null;
        if (dateTime instanceof Date) {
            localDateTime = toLocalDateTime((Date) dateTime);
        } else if (dateTime instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) dateTime;
        } else if (dateTime instanceof LocalDate) {
            localDateTime = ((LocalDate) dateTime).atStartOfDay();
        } else if (dateTime instanceof ZonedDateTime) {
            zonedDateTime = (ZonedDateTime) dateTime;
        }
        if (zonedDateTime != null) {
            zonedDateTime = zonedDateTime.withNano(0);
            return ISO_OFFSET_DATE_TIME.format(zonedDateTime);
        } else if (localDateTime != null) {
            zonedDateTime = localDateTime.withNano(0).atZone(ZoneOffset.systemDefault());
            return ISO_OFFSET_DATE_TIME.format(zonedDateTime);
        }
        return null;
    }

    public static <D> String format(D dateTime, DateTimeFormatter formatter) {
        if (dateTime == null) {
            return null;
        }
        if (dateTime instanceof Date) {
            return formatter.format(toLocalDateTime((Date) dateTime));
        } else if (dateTime instanceof LocalDateTime) {
            return formatter.format((LocalDateTime) dateTime);
        } else if (dateTime instanceof LocalDate) {
            return formatter.format((LocalDate) dateTime);
        }
        return null;
    }

    public static String formatDateTime(Date dateTime) {
        return dateTime == null ? null : DATE_TIME_FORMATTER.format(toLocalDateTime(dateTime));
    }

    public static String formatMinute(Date dateTime) {
        return dateTime == null ? null : MINUTE_FORMATTER.format(toLocalDateTime(dateTime));
    }

    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime == null ? null : DATE_TIME_FORMATTER.format(dateTime);
    }

    public static String formatDate(Date date) {
        return date == null ? null : DATE_FORMATTER.format(toLocalDate(date));
    }

    public static String formatDate(LocalDate date) {
        return date == null ? null : DATE_FORMATTER.format(date);
    }

    public static LocalDateTime parseAdjust(String s) {
        return Optional.ofNullable(parseDate(s)).map(DateHelper::toLocalDateTime).orElse(parseDateTime(s));
    }

    public static LocalDate parseDate(String s) {
        if (RegHelper.isDate(s)) {
            return LocalDate.parse(s, DATE_PARSER);
        }
        return null;
    }

    public static LocalDateTime parseDateTime(String s) {
        if (RegHelper.isDateTime(s)) {
            return LocalDateTime.parse(s, DATE_TIME_PARSER);
        }
        return null;
    }

    public static ZonedDateTime parseZoneDateTime(String s) {
        return ZonedDateTime.parse(s, ISO_OFFSET_DATE_TIME);
    }

    public static Date plusDay(Date date, int day) {
        if (date == null) {
            return null;
        }
        return toDate(toLocalDateTime(date).plusDays(day));
    }

    public static Date minusDay(Date date, int day) {
        if (date == null) {
            return null;
        }
        return toDate(toLocalDateTime(date).minusDays(day));
    }

    /**
     * 一天的最后一秒 例如：2022-03-23 23:59:59
     */
    public static Date atEndOfDay(Date date) {
        if (date == null) {
            return null;
        }
        return toDate(toLocalDate(date).atStartOfDay().plusDays(1).minusSeconds(1));
    }

    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDate.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static LocalDateTime toLocalDateTime(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.atStartOfDay();
    }

    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    public static Date toDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return toDate(localDate.atStartOfDay());
    }

    public static Date max(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        } else if (date2 == null) {
            return date1;
        } else if (date1.before(date2)) {
            return date2;
        } else {
            return date1;
        }

    }
}
