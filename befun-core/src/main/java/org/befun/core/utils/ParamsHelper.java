package org.befun.core.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.core.constant.ParameterType;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.dto.query.ResourceQueryRequestDto;
import org.befun.core.dto.query.SearchCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.SpringContextHolder;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;

import javax.persistence.criteria.JoinType;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ParamsHelper {

    protected static ConversionService getConversionService() {
        return SpringContextHolder.getBean(ConversionService.class);
    }

    public static ResourceQueryCriteria parseCriteria(String key, String value, Field field) {
        if (ParameterType.OPERATORS_LOOKUP.containsKey(key)) {
            return null;
        }

        ResourceQueryCriteria criteria = new ResourceQueryCriteria();
        if (key.contains("_")) {
            String[] keys = key.split("_");
            if (keys.length > 2) {
                throw new BadRequestException("不支持的查询参数" + key);
            }
            criteria.setKey(keys[0]);
            if (Collection.class.isAssignableFrom(field.getType())) {
                throw new BadRequestException("不支持的查询参数" + key);
            }
            QueryOperator opt = QueryOperator.valueOfAlias(keys[1]);
            if (opt == null) {
                throw new BadRequestException("不支持的查询参数" + key);
            }
            criteria.setOperator(opt);
            if (opt.getValueType() == QueryOperator.ValueType.SINGLE) {
                Object v = castSingle(field, value);
                if (v == null) {
                    return null;
                }
                criteria.setValue(v);
            } else if (opt.getValueType() == QueryOperator.ValueType.MULTI) {
                List<?> v = castMulti(field, value);
                if (CollectionUtils.isEmpty(v)) {
                    return null;
                }
                criteria.setValue(v);
            }
        } else {
            criteria.setKey(key);
            if (value != null) {
                // 增加 key-value isnull 和 notnull 的支持（status=isnull&ip=notnull）
                if (QueryOperator.IS_NULL.getAlias().equalsIgnoreCase(value)) {
                    criteria.setOperator(QueryOperator.IS_NULL);
                    criteria.setParamKey(key + "_" + QueryOperator.IS_NULL.getAlias());
                    return criteria;
                } else if (QueryOperator.NOT_NULL.getAlias().equalsIgnoreCase(value)) {
                    criteria.setOperator(QueryOperator.NOT_NULL);
                    criteria.setParamKey(key + "_" + QueryOperator.NOT_NULL.getAlias());
                    return criteria;
                }
            }
            criteria.setValue(castSingle(field, value));
        }
        criteria.setParamKey(key);
        return criteria;
    }

    private static Object castSingle(Field field, String value) {
        return castFun(field.getType()).apply(value);
    }

    private static List<?> castMulti(Field field, String value) {
        Function<Object, Object> fun = castFun(field.getType());
        return Arrays.stream(value.split(",")).filter(StringUtils::isNotEmpty).map(fun).collect(Collectors.toList());
    }

    public static Function<Object, Object> castFun(Class<?> c) {
        if (c == null) {
            return v -> v;
        } else if (c.equals(int.class) || c.equals(Integer.class)) {
            return v -> Integer.parseInt(v.toString());
        } else if (c.equals(long.class) || c.equals(Long.class)) {
            return v -> Long.parseLong(v.toString());
        } else if (c.equals(boolean.class) || c.equals(Boolean.class)) {
            return v -> Boolean.parseBoolean(v.toString());
        } else if (c.equals(double.class) || c.equals(Double.class)) {
            return v -> Double.parseDouble(v.toString());
        } else if (c.equals(Date.class)) {
            return v -> DateHelper.toDate(DateHelper.parseAdjust(v.toString()));
        } else if (Enum.class.isAssignableFrom(c)) {
            return v -> Enum.valueOf((Class<Enum>) c, v.toString());
        } else if (BaseEntityDTO.class.isAssignableFrom(c)) {
            return v -> Long.parseLong(v.toString());
        } else {
            return v -> v;
        }
    }

}
