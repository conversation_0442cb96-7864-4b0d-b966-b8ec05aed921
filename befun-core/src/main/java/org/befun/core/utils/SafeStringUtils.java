package org.befun.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 */
@Slf4j
public class SafeStringUtils {

    static Pattern pattern = Pattern.compile("(<|\\{\\{).+?(>|\\}}\\}})", Pattern.DOTALL);
    static String replaceString = "[^\\u4e00-\\u9fa5a-zA-Z0-9\\\\s]";
    static String ALGORITHM = "MD5";

    public static String replaceHtml(String html) {
        String result = (html == null) ? null : pattern.matcher(html).replaceAll("");
        return result;
    }

    public static String safeTitle(String title) {

        if (StringUtils.isEmpty(title)) {
            return "";
        }

        int lastDotIndex = title.lastIndexOf(".");
        String namePart, extensionPart;

        if (lastDotIndex != -1) {
            namePart = title.substring(0, lastDotIndex);
            extensionPart = title.substring(lastDotIndex);
        } else {
            // 如果没有找到"."，则整个字符串都是文件名，没有后缀名
            namePart = title;
            extensionPart = "";
        }

        return  replaceHtml(StringUtils.normalizeSpace(namePart)).replaceAll(replaceString, "").trim() + extensionPart;
    }



    public static void main(String[] args) {
        System.out.printf(safeTitle("()（）！@#!@#%(222123ok-中文下载-1690517104316.zip"));
    }
}
