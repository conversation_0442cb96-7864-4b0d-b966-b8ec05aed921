package org.befun.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class EnumHelper {

    public static <E extends Enum<?>> E parse(E[] values, String s) {
        return parse(values, s, null);
    }

    public static <E extends Enum<?>> E parse(E[] values, String s, E defaultEnum) {
        if (StringUtils.isEmpty(s)) {
            return defaultEnum;
        }
        return Arrays.stream(values).filter(i -> i.name().equals(s)).findFirst().orElse(defaultEnum);
    }

    public static <E extends Enum<?>> List<E> parseList(E[] values, String s) {
        if (StringUtils.isEmpty(s)) {
            return new ArrayList<>();
        }
        return Arrays.stream(s.split(",")).map(i -> parse(values, i, null)).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
