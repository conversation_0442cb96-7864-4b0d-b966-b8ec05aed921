package org.befun.core.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
public class JsonHelper {

    @Autowired
    private final ObjectMapper objectMapper;
    private final ObjectMapper objectMapperCustom;
    private static JsonHelper self;

    public JsonHelper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        this.objectMapper.enable(JsonReadFeature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER.mappedFeature());
        objectMapperCustom = objectMapper.copy();
        configCustomObjectMapper();
        self = this;
    }

    private void configCustomObjectMapper() {
        objectMapperCustom.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL); // 忽略null属性
    }

    @PreDestroy
    public void destroy() {
        self = null;
    }

    public static ObjectMapper getObjectMapper(boolean custom) {
        Assert.notNull(self, "JsonHelper need init");
        Assert.notNull(self.objectMapper, "JsonHelper need init");
        Assert.notNull(self.objectMapperCustom, "JsonHelper need init");
        return custom ? self.objectMapperCustom : self.objectMapper;
    }

    public static ObjectMapper getObjectMapper() {
        return getObjectMapper(false);
    }

    public static <T> T toObject(String s, Class<T> clazz) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                return getObjectMapper().readValue(i, clazz);
            } catch (JsonProcessingException e) {
                log.error("[json to object]失败：目标类型={}, json={}", clazz.getName(), i);
            }
            return null;
        }).orElse(null);
    }

    public static <T> T toObject(String s, TypeReference<T> clazz) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                return getObjectMapper().readValue(i, clazz);
            } catch (JsonProcessingException e) {
                log.error("[json to object]失败：目标类型={}, json={}", clazz.getType(), i);
            }
            return null;
        }).orElse(null);
    }

    public static <T> T toObject(Map<String, ?> s, Class<T> clazz) {
        return Optional.ofNullable(s).map(i -> {
            return getObjectMapper().convertValue(i, clazz);
        }).orElse(null);
    }

    public static <T> List<T> toList(String s, Class<T> clazz) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                return getObjectMapper().readValue(i, getObjectMapper().getTypeFactory().constructCollectionType(ArrayList.class, clazz));
            } catch (JsonProcessingException e) {
                log.error("[json to List]失败：元素类型={}, json={}", clazz.getName(), i);
            }
            return (List<T>) null;
        }).orElse(null);
    }

    public static List<Map<String, Object>> toListMap(String s) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                return getObjectMapper().readValue(i, getObjectMapper().getTypeFactory().constructCollectionType(ArrayList.class,
                        getObjectMapper().getTypeFactory().constructMapType(HashMap.class, String.class, Object.class)));
            } catch (JsonProcessingException e) {
                log.error("[json to ListMap]失败：json={}", i);
            }
            return (List<Map<String, Object>>) null;
        }).orElse(null);
    }

    public static <T> Set<T> toSet(String s, Class<T> clazz) {
        return Optional.ofNullable(s).filter(StringUtils::isNotEmpty).map(i -> {
            try {
                return getObjectMapper().readValue(i, getObjectMapper().getTypeFactory().constructCollectionType(HashSet.class, clazz));
            } catch (JsonProcessingException e) {
                log.error("[json to Set]失败：元素类型={}, json={}", clazz.getName(), i);
            }
            return (Set<T>) null;
        }).orElse(null);
    }

    public static Map<String, Object> toMap(String json) {
        return Optional.ofNullable(json).map(i -> {
            try {
                return getObjectMapper().readValue(i, getObjectMapper().getTypeFactory().constructMapType(HashMap.class, String.class, Object.class));
            } catch (Exception e) {
                log.error("[object to Map]失败：对象类型={}", i.getClass().getName());
            }
            return (Map<String, Object>) null;
        }).orElse(null);
    }

    public static <K, V> Map<K, V> toMap2(String json, Class<K> keyClass, Class<V> valueClass) {
        return Optional.ofNullable(json).map(i -> {
            try {
                return getObjectMapper().readValue(i, getObjectMapper().getTypeFactory().constructMapType(HashMap.class, keyClass, valueClass));
            } catch (Exception e) {
                log.error("[object to Map]失败：对象类型={}", i.getClass().getName());
            }
            return (Map<K, V>) null;
        }).orElse(null);
    }

    public static Map<String, Object> toMap(String json, Class<?> keyClass, Class<?> valueClass) {
        return Optional.ofNullable(json).map(i -> {
            try {
                return getObjectMapper().readValue(i, getObjectMapper().getTypeFactory().constructMapType(HashMap.class, keyClass, valueClass));
            } catch (Exception e) {
                log.error("[object to Map]失败：对象类型={}", i.getClass().getName());
            }
            return (Map<String, Object>) null;
        }).orElse(null);
    }

    public static Map<String, Object> toMap(Object object) {
        return Optional.ofNullable(object).map(i -> {
            try {
                return getObjectMapper().convertValue(i, getObjectMapper().getTypeFactory().constructMapType(HashMap.class, String.class, Object.class));
            } catch (Exception e) {
                log.error("[object to Map]失败：对象类型={}", i.getClass().getName());
            }
            return (Map<String, Object>) null;
        }).orElse(null);
    }

    public static String toJson(Object object) {
        return toJson(false, object);
    }

    public static String toJson(boolean custom, Object object) {
        return Optional.ofNullable(object).map(i -> {
            try {
                return getObjectMapper(custom).writeValueAsString(i);
            } catch (JsonProcessingException e) {
                log.error("[object to json]失败：对象类型={}", i.getClass().getName());
            }
            return null;
        }).orElse(null);
    }

    public static void mapStringProperty(Object v, Consumer<String> set) {
        mapProperty(v, i -> true, j -> j, set);
    }

    public static void mapLongProperty(Object v, Consumer<Long> set) {
        mapProperty(v, NumberUtils::isDigits, Long::parseLong, set);
    }

    public static void mapListLongProperty(Object v, Consumer<List<Long>> set) {
        mapListLongProperty(v, i -> i, set);
    }

    public static void mapListLongProperty(Object v, Function<String, String> replace, Consumer<List<Long>> set) {
        mapProperty(
                v,
                i -> true,
                j -> Arrays.stream(replace.apply(j).split(","))
                        .filter(NumberUtils::isDigits)
                        .map(Long::parseLong)
                        .collect(Collectors.toList()),
                set);
    }

    public static <T> void mapProperty(Object v, Predicate<String> test, Function<String, T> convert, Consumer<T> set) {
        if (v != null) {
            String vv = v.toString();
            if (test.test(vv)) {
                set.accept(convert.apply(vv));
            }
        }
    }

}
