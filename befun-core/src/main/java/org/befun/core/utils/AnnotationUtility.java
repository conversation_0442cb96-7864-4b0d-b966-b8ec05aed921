package org.befun.core.utils;

import javax.lang.model.type.MirroredTypeException;
import javax.lang.model.type.MirroredTypesException;
import javax.lang.model.type.TypeMirror;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class AnnotationUtility {
    /**
     * tricky way to get real type mirror from annotation
     * https://stackoverflow.com/questions/7687829/java-6-annotation-processing-getting-a-class-from-an-annotation
     * @param c
     * @return
     */
    public static TypeMirror getTypeMirrorFromAnnotation(GetClassValue c) {
        try {
            c.execute();
        }
        catch(MirroredTypesException ex) {
            return ex.getTypeMirrors().get(0);
        }
        return null;
    }

    /**
     *
     * @param c
     * @return
     */
    public static List<? extends TypeMirror> getGenericTypeMirrorFromAnnotation(GetClassValue c) {
        try {
            c.execute();
        }
        catch(MirroredTypesException ex) {
            return ex.getTypeMirrors();
        }
        return null;
    }


    @FunctionalInterface
    public interface GetClassValue {
        void execute() throws MirroredTypeException, MirroredTypesException;
    }
}
