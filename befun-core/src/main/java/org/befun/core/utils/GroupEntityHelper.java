package org.befun.core.utils;

import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.query.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public class GroupEntityHelper {

    public static <E extends BaseEntity,
            D extends BaseEntityDTO<E>,
            GE extends BaseEntity> Page<D> findAllWithGroup(
            int page, int limit,
            List<GE> groupList,
            Fun<Page<D>> getPageData,
            Function<List<GE>, List<D>> mockData) {
        int pageStartOffset = 0;
        if (CollectionUtils.isEmpty(groupList)) {
            return getPageData.apply(pageStartOffset, page, limit);
        } else {                                // page 1 limit 2
            int groupSize = groupList.size();   // 1
            int total = groupSize;              // 1
            int start = (page - 1) * limit;     // 0
            int end = page * limit;             // 2
            List<GE> concatGroupList = null;
            Page<D> concatResourceList = null;
            if (end <= groupSize) { // 这一页全是目录
                concatGroupList = groupList.subList(start, end);
                total += (int) getPageData.apply(pageStartOffset, 1, 1).getTotalElements();
            } else if (start < groupSize) { // 这一页有目录和数据
                concatGroupList = groupList.subList(start, groupSize);
                int groupPageMod = groupSize % limit;
                concatResourceList = getPageData.apply(pageStartOffset, 1, limit - groupPageMod);
                total += (int) concatResourceList.getTotalElements();
            } else { // 这一页全是数据
                int groupPageMod = groupSize % limit;
                int groupPage = groupPageMod == 0 ? groupSize / limit : (groupSize / limit + 1);
//                pageStartOffset = limit - groupPageMod;
                pageStartOffset = groupPageMod == 0 ? 0 : limit - groupPageMod;
                concatResourceList = getPageData.apply(pageStartOffset, page - groupPage, limit);
                total += (int) concatResourceList.getTotalElements();
            }
            return concat(total, page, limit, concatGroupList, concatResourceList, mockData);
        }
    }

    private static <E extends BaseEntity, D extends BaseEntityDTO<E>, GE extends BaseEntity> Page<D> concat(
            int total, int page, int limit,
            List<GE> groupList,
            Page<D> dataList,
            Function<List<GE>, List<D>> mockData) {
        List<D> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(groupList)) {
            result.addAll(mockData.apply(groupList));
        }
        if (dataList != null && !dataList.isEmpty()) {
            result.addAll(dataList.getContent());
        }
        return new PageResult<>(result, PageRequest.of(page - 1, limit), total);
    }

    @FunctionalInterface
    public interface Fun<T> {
        T apply(Integer pageStartOffset, Integer pageNumber, Integer pageSize);
    }
}
