package org.befun.core.utils;

import lombok.SneakyThrows;
import org.apache.tomcat.util.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;


/**
 * AES-256-ECB加密模式，AES/ECB/PKCS5Padding。
 * key 32位不足自动补0
 *
 * <AUTHOR>
 */
public class AESUtils {


    private static final int keyLength = 32;

    private static String completeKey(String sKey) throws Exception {
        if (sKey == null) {
            throw new Exception("Key为空null");
        }

        return sKey + "0".repeat(Math.max(0, keyLength - sKey.length()));
    }

    public static String encrypt(String key, String content) throws Exception {
        key = completeKey(key);
        byte[] raw = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64URLSafeString(encrypted);
    }

    public static String decrypt(String key, String content) throws Exception {
        key = completeKey(key);
        byte[] raw = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] encrypted = Base64.decodeBase64URLSafe(content);
        try {
            byte[] original = cipher.doFinal(encrypted);
            return new String(original, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw e;
        }
    }

    @SneakyThrows
    public static void main(String[] args) {
        String key = "u8BGiz1hchxKuK9tEPWsqw72Kow7rUcu";
        String data = encrypt(key, "{\n" +
                "    \"username\": \"13388320903\",\n" +
                "    \"encryptedPassword\": \"fERkLS/jOJmrmZTspa77nqIVsJmFl4FT4NQWyIxABFEq+47rZ68N7ieHT+d/HhPl5lwn9RYAq1XjIg2/7uFUME5Z7oFsJV2gJmm39dFxDsMXIk6F3IWbBlhUAzJFU9WtgH9xZrZd55tzAl+9QR9sJLaOAwwv5YCywamfob/UUXc=\"\n" +
                "}");
        System.out.printf("加密后的字符串为：%s%n", data);
        System.out.printf("解密后的字符串为：%s%n", decrypt(key, "3N1eT6rZ_oT_nugOyvX1rbPqa36WNh61qt4MZ7YutCwFguFVsSeOQaCIBX6n3E47ORhFCLT5NoWP3Vh1t21Mf_vHGLu1Vg1YLLYMKTZATaHssY6n4HvFXiUSpeHwlJbyegPvnBints9qgFcFFbYKJ9iPt9LmwZaByiDDGq8aL-1e4cUNfGaaUJ5QZe3HZCr2yYNuIE9jchO9Bjj5FxSiSehn7xaHRt7J_TFEJzYbB-yKw3INWm7CSNaJfvznoFd1VPVGoAeW5K2EbFprikrHSh4nS74zdBtlMaaNc1bRzN8"));
    }

}