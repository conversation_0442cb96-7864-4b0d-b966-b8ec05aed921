package org.befun.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegHelper {
    public static final Pattern PATTERN_MOBILE = Pattern.compile("^1[3456789]\\d{9}$");
    public static final Pattern PATTERN_EMAIL = Pattern.compile("^[\\w\\S]+@[a-zA-Z\\d_-]+(\\.[a-zA-Z\\d_-]+)+$");
    public static final Pattern PATTERN_DATE = Pattern.compile("^\\d{4}-\\d{1,2}-\\d{1,2}$");
    public static final Pattern PATTERN_DATE_TIME = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$");
    public static final Pattern PATTERN_FILE_NAME = Pattern.compile("/?([\\s\\S](?!.*/).*[\\w\\s/-]+)$");
    public static final Pattern PATTERN_FILE_PATH = Pattern.compile("(/?(?:[^?#]*?/)*)");


    public static String parseFileName(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        Matcher ma = PATTERN_FILE_NAME.matcher(url);
        while (ma.find()) {
            return ma.group(1);
        }

        return null;
    }

    public static String parseFilePath(String url, String domain) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        Matcher ma = PATTERN_FILE_PATH.matcher(StringUtils.isNotEmpty(domain) ? url.replace(domain, ""): url);
        while (ma.find()) {
            return ma.group(1);
        }

        return null;
    }


    public static boolean isMatched(String s, Pattern pattern) {
        return StringUtils.isNotEmpty(s) && pattern.matcher(s).matches();
    }

    public static boolean isMobile(String s) {
        return isMatched(s, PATTERN_MOBILE);
    }

    public static boolean isEmail(String s) {
        return isMatched(s, PATTERN_EMAIL);
    }

    public static boolean isDate(String s) {
        return isMatched(s, PATTERN_DATE);
    }

    public static boolean isDateTime(String s) {
        return isMatched(s, PATTERN_DATE_TIME);
    }

    public static void main(String[] args) {
        System.out.println(isEmail("a##&*()!-.?#$%+!@ss.com"));

        String url1 = "ui/lite/123ok-中文下载-1690517104316.zip";
        String url2 = "/lite/64c34a40e4b08c2d6ddb0481.png";
        String url3 = "lite/64c34a40e4b08c2d6ddb0481.png";
        String url4 = "lite/64c34a40e4b08c2d6ddb0481";
        String domain = "https://dev-assets.surveyplus.cn";
        String url5 = "https://dev-assets.surveyplus.cn/cem/lite/spring执行顺序1717567064273.png";
        String url6 = "https://dev-assets.surveyplus.cn/api/survey/files?url=cem/lite/spring执行顺序1717567064273.png";

        // 打印 原始url 解析的路径 解析的文件名
        System.out.println(String.format("[%s]<[%s]>[%s]", url1, parseFilePath(url1, null), parseFileName(url1)));
        System.out.println(String.format("[%s]<[%s]>[%s]", url2, parseFilePath(url2, null), parseFileName(url2)));
        System.out.println(String.format("[%s]<[%s]>[%s]", url3, parseFilePath(url3, null), parseFileName(url3)));
        System.out.println(String.format("[%s]<[%s]>[%s]", url4, parseFilePath(url4, null), parseFileName(url4)));
        System.out.println(String.format("[%s]<[%s]>[%s]", url5, parseFilePath(url5, "https://dev-assets.surveyplus.cn"), parseFileName(url5)));
        System.out.println(String.format("[%s]<[%s]>[%s]", url6, parseFilePath(url6, "https://dev-assets.surveyplus.cn/api/survey/files?url="), parseFileName(url6)));
        System.out.println(String.format("[%s]<[%s]>[%s]", url6, parseFilePath(url6, null), parseFileName(url6)));
    }
}
