package org.befun.core.utils;

import java.util.function.Function;

public class NumberHelper {

    public static int unbox(Integer i) {
        return i == null ? 0 : i;
    }

    public static long unbox(Long i) {
        return i == null ? 0 : i;
    }

    public static double division(int i1, int i2) {
        return i2 == 0 ? 0 : (i1 * 1.0 / i2);
    }

    public static <T extends Comparable<T>, O> O max(O o1, O o2, Function<O, T> getComparableValue) {
        if (o1 == null) {
            return o2;
        } else if (o2 == null) {
            return o1;
        }
        T t1 = getComparableValue.apply(o1);
        T t2 = getComparableValue.apply(o2);
        if (t1 == null) {
            return o2;
        } else if (t2 == null) {
            return o1;
        } else if (t1.compareTo(t2) > 0) {
            return o1;
        } else {
            return o2;
        }
    }
}
