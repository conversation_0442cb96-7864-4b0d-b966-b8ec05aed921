package org.befun.core.limiter.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.UserPermissions;
import org.befun.core.exception.PermissionException;
import org.befun.core.rest.context.TenantContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@NoArgsConstructor
@Order(99)
public class PermissionPointcut implements MethodInterceptor {

    private Map<String, Object> data;

    public PermissionPointcut(String data) throws JsonProcessingException {
        this.data = new ObjectMapper().readValue(data, Map.class);
    }

    @Override
    public Object invoke(@NotNull MethodInvocation invocation) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String url = request.getRequestURI();
        url = url.replaceAll("/[1-9]\\d*", "/*");
        String key = String.format("%s:%s", request.getMethod().toUpperCase(), url);
        String needPermission = (String) data.get(key);
        if (StringUtils.isNotEmpty(needPermission)) {
            UserPermissions permissions = TenantContext.getCurrentPermissions();
            if (permissions == null) {
                throw new PermissionException("empty permission denied");
            }
            ArrayList<String> permissionsPaths = new ArrayList<>();
            Optional.ofNullable(permissions.getAction()).ifPresent(permissionsPaths::addAll);
            Optional.ofNullable(permissions.getDashboard()).ifPresent(permissionsPaths::addAll);
            if (!permissionsPaths.contains(needPermission)) {
                throw new PermissionException(String.format("Permission denied: %s", url));
                }
            }
        return invocation.proceed();
    }
}
