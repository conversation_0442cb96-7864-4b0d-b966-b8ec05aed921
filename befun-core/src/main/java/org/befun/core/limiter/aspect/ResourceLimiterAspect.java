package org.befun.core.limiter.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.befun.core.exception.OverLimitException;
import org.befun.core.limiter.annotation.LimiterCanConsume;
import org.befun.core.limiter.annotation.LimiterCheckConsume;
import org.befun.core.limiter.annotation.LimiterRevertConsume;
import org.befun.core.limiter.annotation.LimiterTryConsume;
import org.befun.core.service.ResourceLimiterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ResourceLimiterAspect {
    @Autowired
    ResourceLimiterService limiterService;

    @Pointcut("@annotation(org.befun.core.limiter.annotation.LimiterTryConsume)")
    public void tryConsume() {}

    @Pointcut("@annotation(org.befun.core.limiter.annotation.LimiterCanConsume)")
    public void canConsume() {}

    @Pointcut("@annotation(org.befun.core.limiter.annotation.LimiterRevertConsume)")
    public void revertConsume() {}

    @Pointcut("@annotation(org.befun.core.limiter.annotation.LimiterCheckConsume)")
    public void checkConsume() {}

    @Around("tryConsume()")
    public Object applyTryConsumeLimiter(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sig = (MethodSignature) joinPoint.getSignature();
        Method method = sig.getMethod();
        StandardEvaluationContext ctx = limiterService.buildContext(sig.getParameterNames(), joinPoint.getArgs());
        LimiterTryConsume limiter = method.getAnnotation(LimiterTryConsume.class);
        Boolean enable = limiterService.parseAsBool(ctx, limiter.condition());
        if (!enable) {
            return joinPoint.proceed();
        }

        String key = limiterService.parseAsString(ctx, limiter.key());
        Integer count = limiter.countExpression().length() > 0 ? limiterService.parseAsInt(ctx, limiter.countExpression()) : limiter.count();
        Integer max = limiter.maxExpression().length() > 0 ? limiterService.parseAsInt(ctx, limiter.maxExpression()) : limiter.max();

        log.debug("applyLimiter to method {} with limiter {}", method.getName(), key);
        Boolean allow = limiterService.tryConsume(key, count, max);

        if (!allow) {
            throw new OverLimitException(limiter.message());
        }

        // try confirm part
        try {
            Object result = joinPoint.proceed();
            return result;
        } catch (Throwable exception) {
            limiterService.revert(key, count);
            log.debug("applyLimiter to method {} with limiter {} revert {} got exception {}", method.getName(), key, count, exception.getMessage());
            exception.printStackTrace();
            throw exception;
        }
    }

    @Around("canConsume()")
    public Object applyCanConsumeLimiter(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sig = (MethodSignature) joinPoint.getSignature();
        Method method = sig.getMethod();
        StandardEvaluationContext ctx = limiterService.buildContext(sig.getParameterNames(), joinPoint.getArgs());
        LimiterCanConsume limiter = method.getAnnotation(LimiterCanConsume.class);
        Boolean enable = limiterService.parseAsBool(ctx, limiter.condition());

        if (!enable) {
            return joinPoint.proceed();
        }

        String key = limiterService.parseAsString(ctx, limiter.key());
        Integer count = limiter.countExpr().length() > 0 ? limiterService.parseAsInt(ctx, limiter.countExpr()) : limiter.count();
        Integer max = limiter.maxExpr().length() > 0 ? limiterService.parseAsInt(ctx, limiter.maxExpr()) : limiter.max();
        Boolean result = limiterService.canConsume(key, count, max);

        if (!result) {
            throw new OverLimitException(limiter.message());
        }
        return joinPoint.proceed();
    }

    @Around("revertConsume()")
    public Object applyRevertConsumeLimiter(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sig = (MethodSignature) joinPoint.getSignature();
        Method method = sig.getMethod();
        StandardEvaluationContext ctx = limiterService.buildContext(sig.getParameterNames(), joinPoint.getArgs());
        LimiterRevertConsume limiter = method.getAnnotation(LimiterRevertConsume.class);
        Boolean enable = limiterService.parseAsBool(ctx, limiter.condition());

        if (!enable) {
            return joinPoint.proceed();
        }

        String key = limiterService.parseAsString(ctx, limiter.key());
        Integer count = limiter.count();
        Integer min = limiter.min();
        Integer result = limiterService.revert(key, count);
        if (result.intValue() < min) {
            limiterService.reset(key, min);
        }
        return joinPoint.proceed();
    }

    @Around("checkConsume()")
    public Object applyCheckConsumeLimiter(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sig = (MethodSignature) joinPoint.getSignature();
        Method method = sig.getMethod();
        StandardEvaluationContext ctx = limiterService.buildContext(sig.getParameterNames(), joinPoint.getArgs());
        LimiterCheckConsume limiter = method.getAnnotation(LimiterCheckConsume.class);
        Boolean enable = limiterService.parseAsBool(ctx, limiter.condition());

        if (!enable) {
            return joinPoint.proceed();
        }

        String key = limiterService.parseAsString(ctx, limiter.key());
        Integer min = limiter.min();
        Integer result = limiterService.balance(key);
        if (result.intValue() < min) {
            throw new OverLimitException(limiter.message());
        }
        return joinPoint.proceed();
    }
}