package org.befun.core.limiter.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermissions {
    /**
     * PermissionPath.*
     */
    String[] value() default {};

    /**
     * 需要是超级管理员
     */
    boolean needSuperAdmin() default false;

    /**
     * 权限匹配规则
     */
    MatchType matchType() default MatchType.ALL_MATCH;

    enum MatchType {
        ALL_MATCH,
        ANY_MATCH,
        NONE_MATCH
    }
}
