package org.befun.core.limiter;

import org.befun.core.dto.BaseDTO;
import org.befun.core.exception.OverLimitException;

/**
 * The class description
 *
 * <AUTHOR>
 */
public interface IBucketLimiter {
    Integer balance(String key);
    Integer revert(String key, int number);
    Boolean canConsume(String key, int number, int max);
    Boolean tryConsume(String key, int number, int max) throws OverLimitException;
}
