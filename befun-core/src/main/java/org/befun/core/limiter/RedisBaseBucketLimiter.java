package org.befun.core.limiter;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.OverLimitException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 基于Redis计数器实现的简单限制器
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
@Component
public class RedisBaseBucketLimiter implements IBucketLimiter {
    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * revert, for tcc, revert the commit
     * @param key
     * @param number
     * @return
     */
    @Override
    public Integer revert(String key, int number) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Integer newCount = (valueOperations.decrement(key, number)).intValue();
        return newCount;
    }

    /**
     * canConsume 检查是否可以访问，并不修改当前计数
     * @param key
     * @param number
     * @param max
     * @return
     */
    public Boolean canConsume(String key, int number, int max) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object value = valueOperations.get(key);
        if (value == null) {
            return true;
        }
        return Integer.valueOf(value.toString()) <= max - number;
    }

    /**
     * tryConsume 访问并且尝试计数
     * @param key
     * @param number
     * @param max
     * @return
     */
    public Boolean tryConsume(String key, int number, int max) throws OverLimitException {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object value = valueOperations.get(key);
        Integer newCount = (value == null ? 0: Integer.valueOf(value.toString())) + number;
        if (newCount <= max) {
            newCount = (valueOperations.increment(key, number)).intValue();
        }
        return newCount <= max;
    }

    /**
     * reset 重置
     * @param key
     * @return
     */
    public void reset(String key ,int number) throws OverLimitException {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        valueOperations.set(key, String.valueOf(number));
    }

    /**
     * 查询数量
     * @param key
     * @return
     */
    public Integer balance(String key) throws OverLimitException {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object num = valueOperations.get(key);
        return Integer.parseInt(num == null ? "0" : num.toString());
    }
}
