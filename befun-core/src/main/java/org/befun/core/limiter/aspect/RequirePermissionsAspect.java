package org.befun.core.limiter.aspect;

import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.befun.core.dto.UserPermissions;
import org.befun.core.exception.PermissionException;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.context.TenantContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

@Aspect
@Component
public class RequirePermissionsAspect {

    @Before("@annotation(requirePermissions)")
    public void before(RequirePermissions requirePermissions) {
        if (requirePermissions.needSuperAdmin()) {
            if (!Optional.ofNullable(TenantContext.getCurrentIsAdmin()).orElse(false)) {
                throw new PermissionException("只有超级管理员可以进行操作");
            }
        }
        if (requirePermissions.value().length == 0) {
            return;
        }
        List<String> actions = Optional.ofNullable(TenantContext.getCurrentPermissions()).map(UserPermissions::getAction).orElse(null);
        if (actions != null) {
            boolean matched = false;
            Stream<String> stream = Arrays.stream(requirePermissions.value());
            switch (requirePermissions.matchType()) {
                case ALL_MATCH -> matched = stream.allMatch(actions::contains);
                case ANY_MATCH -> matched = stream.anyMatch(actions::contains);
                case NONE_MATCH -> matched = stream.noneMatch(actions::contains);
            }
            if (matched) {
                return;
            }
        }
        throw new PermissionException("权限不足");
    }
}
