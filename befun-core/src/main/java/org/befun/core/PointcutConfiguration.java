package org.befun.core;

import org.aopalliance.aop.Advice;
import org.befun.core.configuration.PointcutProperties;
import org.befun.core.dto.PointcutMethod;
import org.springframework.aop.aspectj.AspectJExpressionPointcutAdvisor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Constructor;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "pointcut.enable", havingValue = "true")
public class PointcutConfiguration {

    @Autowired
    private PointcutProperties pointcutProperties;

    @Bean
    public AspectJExpressionPointcutAdvisor pointcutAdvisor() {
        AspectJExpressionPointcutAdvisor advisor = new AspectJExpressionPointcutAdvisor();

        if(pointcutProperties.getMethods() != null) {
            Iterator<Map.Entry<String, PointcutMethod>> pointCutMethods = pointcutProperties.getMethods().entrySet().stream().iterator();

            while (pointCutMethods.hasNext()) {
                PointcutMethod method = pointCutMethods.next().getValue();
                if(method.getEnable()){
                    advisor.setExpression(method.getExpression());
                    try {
                        Class<?> clazz = Class.forName(method.getClassName());
                        Constructor<?> constructor = clazz.getConstructor(String.class);
                        Object constructorInstance = constructor.newInstance(method.getParams());
                        advisor.setAdvice((Advice) constructorInstance);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return advisor;
    }
}
