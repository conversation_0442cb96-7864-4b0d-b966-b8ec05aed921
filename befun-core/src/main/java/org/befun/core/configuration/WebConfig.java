package org.befun.core.configuration;


import org.befun.core.rest.ResourceQueryArgumentResolver;
import org.befun.core.rest.ResourceQueryCustomArgumentResolver;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new ResourceQueryArgumentResolver());
        argumentResolvers.add(new ResourceQueryCustomArgumentResolver());
    }
}
