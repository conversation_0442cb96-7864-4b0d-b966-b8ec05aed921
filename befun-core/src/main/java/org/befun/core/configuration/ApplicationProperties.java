package org.befun.core.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration()
@ConfigurationProperties(prefix = "befun.server")
@Getter
@Setter
public class ApplicationProperties {
    private Boolean enableOpenApiFilter = false;
}
