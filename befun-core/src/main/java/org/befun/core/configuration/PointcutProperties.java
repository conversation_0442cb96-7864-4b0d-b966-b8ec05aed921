package org.befun.core.configuration;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.PointcutMethod;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "pointcut")
public class PointcutProperties {
    private Boolean enable;
    private Map<String, PointcutMethod> methods;
}
