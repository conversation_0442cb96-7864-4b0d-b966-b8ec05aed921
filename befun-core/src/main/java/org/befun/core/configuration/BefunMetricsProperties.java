package org.befun.core.configuration;

import io.micrometer.core.instrument.Tag;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@Configuration
@ConfigurationProperties("befun.core.metrics")
public class BefunMetricsProperties {

    private boolean enableCommonTags = false;
    private Map<String, String> commonTags = new HashMap<>();
    private List<String> supportMetrics = new ArrayList<>();

    public Set<Tag> commonTagsAsSet() {
        return commonTags.entrySet().stream().map(i -> Tag.of(i.getKey(), i.getValue())).collect(Collectors.toSet());
    }
}
