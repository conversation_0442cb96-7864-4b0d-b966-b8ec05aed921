package org.befun.core.annotation.method;

import com.squareup.javapoet.*;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ALL;


public class CrudMethodNoPageCollectionBuilder extends CrudMethodCollectionBuilder {

    CrudMethodNoPageCollectionBuilder(ResourceGeneratorContext context) {
        super(context);
    }

    @Override
    public void initCodeMap() {
        super.initCodeMap();
        codeMaps.put(FIND_ALL, "return new ResourceListResponseDto(service.findAll(params));");
    }

    @Override
    public MethodSpec.Builder findAll() {
        ResourceMethod method = FIND_ALL;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        if (context.isOverrideFindAllDto()) {
            ParameterSpec.Builder builder = ParameterSpec.builder(context.getRequestDtoTypeName(method), "params")
                    .addAnnotation(ResourceQueryCustom.class);
            if (getContext().validMethodParam(method)) {
                builder.addAnnotation(Valid.class);
            }
            params.add(builder.build());
        } else {
            params.add(
                    ParameterSpec.builder(ParameterizedTypeName.get(
                                    ClassName.get(ResourceEntityQueryDto.class),
                                    context.getDtoTypeName()), "params")
                            .addAnnotation(
                                    AnnotationSpec.builder(ResourceQueryPredicate.class)
                                            .build()
                            )
                            .addAnnotation(Valid.class)
                            .build()
            );
        }
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path, RequestMethod.GET, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceListResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Basic.class,
                context);
    }

}
