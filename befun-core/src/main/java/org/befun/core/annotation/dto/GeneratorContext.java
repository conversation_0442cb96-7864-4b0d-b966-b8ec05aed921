package org.befun.core.annotation.dto;

import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.TypeName;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.annotation.ResourceController;

import javax.lang.model.type.MirroredTypeException;
import javax.lang.model.type.TypeMirror;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class GeneratorContext {
}
