package org.befun.core.annotation.method;

import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterSpec;
import com.squareup.javapoet.ParameterizedTypeName;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

public class CrudMethodSingleBuilder implements CrudMethodBuilder {

    private final ResourceGeneratorContext context;
    private final Map<ResourceMethod, String> codeMaps = new HashMap<>();

    public CrudMethodSingleBuilder(ResourceGeneratorContext context) {
        this.context = context;
        initCodeMap();
    }

    @Override
    public ResourceGeneratorContext getContext() {
        return context;
    }

    @Override
    public void initCodeMap() {
        codeMaps.put(FIND_ALL, "return new ResourceResponseDto(service.findAll());");
        codeMaps.put(UPDATE_ONE, "return new ResourceResponseDto(service.updateOne(data));");
        codeMaps.put(CREATE, "return new ResourceResponseDto(service.create(data));");
        codeMaps.put(DELETE_ONE, "return new ResourceResponseDto(service.deleteOne());");
    }

    protected String crudDoc(ResourceMethod method) {
        return method.formatDoc(context.getDocCrud());
    }

    protected String findAllDoc() {
        return FIND_ONE.formatDoc(context.getDocCrud());
    }

    @Override
    public MethodSpec.Builder findAll() {
        ResourceMethod method = FIND_ALL;
        List<ParameterSpec> params = new ArrayList<>();
        return buildCollectionMethod(method.getName(), findAllDoc(), "", RequestMethod.GET, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Basic.class,
                context);
    }

    @Override
    public MethodSpec.Builder count() {
        return null;
    }

    @Override
    public MethodSpec.Builder create() {
        ResourceMethod method = CREATE;
        List<ParameterSpec> params = new ArrayList<>();
        params.add(dtoParam(method));
        return buildCollectionMethod(method.getName(), crudDoc(method), "", RequestMethod.POST,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder findOne() {
        return null;
    }

    @Override
    public MethodSpec.Builder updateOne() {
        ResourceMethod method = UPDATE_ONE;
        List<ParameterSpec> params = new ArrayList<>();
        params.add(dtoParam(method));
        return buildCollectionMethod(method.getName(), crudDoc(method), "", RequestMethod.PUT, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder deleteOne() {
        ResourceMethod method = DELETE_ONE;
        List<ParameterSpec> params = new ArrayList<>();
        return buildCollectionMethod(method.getName(), crudDoc(method), "", RequestMethod.DELETE,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(BaseResponseDto.class),
                        ClassName.get(Boolean.class)
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder batchUpdate() {
        return null;
    }
}
