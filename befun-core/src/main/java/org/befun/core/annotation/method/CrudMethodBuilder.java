package org.befun.core.annotation.method;

import com.squareup.javapoet.AnnotationSpec;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterSpec;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

public interface CrudMethodBuilder extends MethodBuilder {

    ResourceGeneratorContext getContext();

    void initCodeMap();

    MethodSpec.Builder findAll();

    MethodSpec.Builder count();

    MethodSpec.Builder create();

    MethodSpec.Builder findOne();

    MethodSpec.Builder updateOne();

    MethodSpec.Builder deleteOne();

    MethodSpec.Builder batchUpdate();

    default ParameterSpec dtoParam(ResourceMethod method) {
        ParameterSpec.Builder param = ParameterSpec.builder(
                        getContext().getRequestDtoTypeName(method),
                        "data"
                )
                .addAnnotation(RequestBody.class);
        if (getContext().validMethodParam(method)) {
            param.addAnnotation(Valid.class);
        }
        return param.build();
    }

    default List<MethodSpec> buildCrudMethods(List<ResourceMethod> excludeMethods) {
        List<MethodSpec.Builder> methods = new ArrayList<>();
        for (ResourceMethod value : ResourceMethod.values()) {
            if (excludeMethods.contains(value)) {
                continue;
            }
            switch (value) {
                case FIND_ALL -> methods.add(postMethodBuilder(FIND_ALL, findAll()));
                case COUNT -> methods.add(postMethodBuilder(COUNT, count()));
                case CREATE -> methods.add(postMethodBuilder(CREATE, create()));
                case FIND_ONE -> methods.add(postMethodBuilder(FIND_ONE, findOne()));
                case UPDATE_ONE -> methods.add(postMethodBuilder(UPDATE_ONE, updateOne()));
                case DELETE_ONE -> methods.add(postMethodBuilder(DELETE_ONE, deleteOne()));
                case BATCH_UPDATE -> methods.add(postMethodBuilder(BATCH_UPDATE, batchUpdate()));
            }
        }
        return methods.stream().filter(Objects::nonNull).map(MethodSpec.Builder::build).collect(Collectors.toList());
    }

    private MethodSpec.Builder postMethodBuilder(ResourceMethod method, MethodSpec.Builder builder) {
        Optional.ofNullable(getContext().getPermissions().get(method)).ifPresent(p -> {
            builder.addAnnotation(AnnotationSpec.builder(PreAuthorize.class)
                    .addMember("value", "$S", p)
                    .build()
            );
        });
        Optional.ofNullable(getContext().getRequirePermissions().get(method)).ifPresent(p -> {
            AnnotationSpec.Builder ab = AnnotationSpec.builder(RequirePermissions.class);
            if (StringUtils.isNotEmpty(p.getRequirePermissions())) {
                ab.addMember("value", "$L", p.getRequirePermissions());
            }
            if (StringUtils.isNotEmpty(p.getNeedSuperAdmin())) {
                ab.addMember("needSuperAdmin", "$L", p.getNeedSuperAdmin());
            }
            if (StringUtils.isNotEmpty(p.getMatchType())) {
                ab.addMember("matchType", "$L", p.getMatchType());
            }
            builder.addAnnotation(ab.build());
        });

        return builder;
    }

    static List<MethodSpec> buildSingleCrudMethods(ResourceGeneratorContext context) {
        return new CrudMethodSingleBuilder(context).buildCrudMethods(context.getExcludeActions());
    }

    static List<MethodSpec> buildNoPageCollectionCrudMethods(ResourceGeneratorContext context) {
        return new CrudMethodNoPageCollectionBuilder(context).buildCrudMethods(context.getExcludeActions());
    }

    static List<MethodSpec> buildCollectionCrudMethods(ResourceGeneratorContext context) {
        return new CrudMethodCollectionBuilder(context).buildCrudMethods(context.getExcludeActions());
    }

    static List<MethodSpec> buildEmbeddedCollectionCrudMethods(ResourceGeneratorContext context, String suffix, String path) {
        return new CrudMethodCollectionEmbeddedBuilder(context, suffix, path).buildCrudMethods(context.getExcludeActions());
    }

    static List<MethodSpec> buildEmbeddedOneCollectionCrudMethods(ResourceGeneratorContext context, String suffix, String path) {
        return new CrudMethodCollectionEmbeddedOneBuilder(context, suffix, path).buildCrudMethods(context.getExcludeActions());
    }

    static List<MethodSpec> buildDeepEmbeddedCollectionCrudMethods(ResourceGeneratorContext context, String suffix, String path) {
        return new CrudMethodCollectionDeepEmbeddedBuilder(context, suffix, path).buildCrudMethods(context.getExcludeActions());
    }
}
