package org.befun.core.annotation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.squareup.javapoet.*;
import org.befun.core.annotation.dto.AnnotationClassContext;
import org.befun.core.annotation.dto.GeneratorContext;
import org.befun.core.annotation.generator.BaseGenerator;
import org.befun.core.annotation.generator.DtoGenerator;
import org.befun.core.annotation.generator.ResourceControllerGenerator;
import org.springframework.util.StringUtils;

import javax.annotation.processing.*;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.Modifier;
import javax.lang.model.element.PackageElement;
import javax.lang.model.element.TypeElement;
import javax.lang.model.util.Elements;
import javax.lang.model.util.Types;
import javax.tools.Diagnostic;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.util.*;

/**
 * 基于javapoet实现java编译动态annotation处理
 * - 支持多个Generator处理
 *
 * <AUTHOR>
 */
public class AnnotationProcessor extends AbstractProcessor {
    private Elements elementUtils;
    private Messager messager;
    private Filer filer;
    private Types types;
    private List<BaseGenerator> generators = new ArrayList<>();

    public AnnotationProcessor() {
        this.generators.add(new DtoGenerator());
        this.generators.add(new ResourceControllerGenerator());
    }

    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        super.init(processingEnv);
        elementUtils = processingEnv.getElementUtils();
        filer = processingEnv.getFiler();
        types = processingEnv.getTypeUtils();
        messager = processingEnv.getMessager();

        for (BaseGenerator generator : this.generators) {
            generator.setup(elementUtils, messager, filer, types);
        }
    }

    /**
     *
     * @param classElem
     * @return
     */
    private AnnotationClassContext getClassContext(TypeElement classElem, String suffix) {
        String qualifiedClassName = classElem.getQualifiedName().toString();
        String className = classElem.getSimpleName().toString() + StringUtils.capitalize(suffix);
        String parentPackage = qualifiedClassName.substring(0, qualifiedClassName.indexOf(classElem.getSimpleName().toString()));

        return AnnotationClassContext.builder()
                .qualifiedClassName(qualifiedClassName)
                .className(className)
                .packageName(parentPackage + suffix.toLowerCase())
                .build();
    }

    /**
     *
     * @param set
     * @param roundEnv
     * @return
     */
    @Override
    public boolean process(Set<? extends TypeElement> set, RoundEnvironment roundEnv) {
        for (BaseGenerator generator : this.generators) {
            String generatorName = generator.getClass().getSimpleName();
            messager.printMessage(Diagnostic.Kind.NOTE, String.format("Processing [%s]", generatorName));
            Class annotationClass = generator.getClassAnnotation();
            Set<? extends Element> elements = roundEnv.getElementsAnnotatedWith(annotationClass);

            for (Element element : elements) {
                TypeElement clsElement = (TypeElement) element;
                PackageElement packageElement = elementUtils.getPackageOf(element);

                Annotation clsAnnotation = element.getAnnotation(annotationClass);
                AnnotationClassContext context = getClassContext(clsElement, generator.getSuffix());
                String className = context.getClassName();
                String packageName = elementUtils.getPackageOf(element).getQualifiedName().toString();
                messager.printMessage(Diagnostic.Kind.NOTE, String.format("Generating [%s] -> %s", generatorName, className));

                TypeSpec.Builder classBuilder = buildClass(context);

                GeneratorContext resourceContext = generator.generateClass(clsElement, clsAnnotation, classBuilder);

                // build fields
                List<FieldSpec> fields = generator.buildFields(resourceContext, clsElement, clsAnnotation);
                classBuilder.addFields(fields);

                // build methods
                List<MethodSpec> methods = generator.buildMethods(resourceContext, clsElement, clsAnnotation);
                classBuilder.addMethods(methods);

                // compile and saving to java file
                try {
                    JavaFile.Builder builder = JavaFile.builder(packageName, classBuilder.build());
                    for (Class staticImport : generator.getStaticImports()) {
                        builder.addStaticImport(staticImport, "*");
                    }
                    builder.build().writeTo(filer);
                } catch (IOException e) {
                    messager.printMessage(
                            Diagnostic.Kind.ERROR,
                            String.format("Processing [%s] -> %s error %s", generatorName, className, e.getMessage()));
                }
            }
        }

        return true;
    }

    private TypeSpec.Builder buildClass(AnnotationClassContext context) {
        TypeSpec.Builder classBuilder = TypeSpec.classBuilder(context.getClassName())
                .addModifiers(Modifier.PUBLIC);
        return classBuilder;
    }

    /**
     *
     * @return
     */
    @Override
    public Set<String> getSupportedAnnotationTypes() {
        Set<String> annotationTypes = new HashSet<>();
        for (BaseGenerator generator : this.generators) {
            annotationTypes.add(generator.getClassAnnotation().getCanonicalName());
        }
        return annotationTypes;
    }

    /**
     *
     * @return
     */
    @Override
    public SourceVersion getSupportedSourceVersion() {
        return SourceVersion.latestSupported();
    }
}
