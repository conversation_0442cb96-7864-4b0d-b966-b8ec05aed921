package org.befun.core.annotation.method;

import com.squareup.javapoet.*;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.lang.model.type.TypeMirror;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

public class CrudMethodCollectionEmbeddedOneBuilder extends CrudMethodCollectionBuilder {

    CrudMethodCollectionEmbeddedOneBuilder(ResourceGeneratorContext context, String suffix, String path) {
        super(context);
        this.suffix = suffix;
        this.path = path;
        defaultParams.add(
                ParameterSpec.builder(long.class, "id")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
    }

    @Override
    protected String resourcePath(List<ParameterSpec> params) {
        return "";
    }

    protected String findAllDoc() {
        return FIND_ONE.formatDoc(context.getDocCrud());
    }

    @Override
    public void initCodeMap() {
        codeMaps.put(FIND_ALL, "return new ResourceResponseDto(service.findAllEmbeddedOne(id, \"%s\", %s.class, %s.class));");
        codeMaps.put(UPDATE_ONE, "return new ResourceResponseDto(service.updateOneEmbeddedOne(id, \"%s\", %s.class, %s.class, data));");
        codeMaps.put(CREATE, "return new ResourceResponseDto(service.createEmbeddedOne(id, \"%s\", %s.class, %s.class, data));");
        codeMaps.put(DELETE_ONE, "return new ResourceResponseDto(service.deleteOneEmbeddedOne(id, \"%s\", %s.class, %s.class));");
    }

    @Override
    public MethodSpec.Builder findAll() {
        ResourceMethod method = FIND_ALL;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        return buildCollectionMethod(method.getName() + suffix, findAllDoc(), path, RequestMethod.GET, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Basic.class,
                context);
    }

    @Override
    public MethodSpec.Builder count() {
        return null;
    }

    @Override
    public MethodSpec.Builder create() {
        ResourceMethod method = CREATE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        params.add(dtoParam(method));
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path, RequestMethod.POST,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder findOne() {
        return null;
    }

    @Override
    public MethodSpec.Builder updateOne() {
        ResourceMethod method = UPDATE_ONE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        params.add(dtoParam(method));
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path, RequestMethod.PUT, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder deleteOne() {
        ResourceMethod method = DELETE_ONE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path, RequestMethod.DELETE,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(BaseResponseDto.class),
                        ClassName.get(Boolean.class)
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder batchUpdate() {
        return null;
    }

    @Override
    public String buildCode(Map<ResourceMethod, String> codeMaps, ResourceMethod method, ResourceGeneratorContext context) {
        if (!codeMaps.containsKey(method)) {
            throw new RuntimeException("invalid method code " + method.getName());
        }

        TypeMirror entityType = context.getEntityType();
        TypeName dtoTypeName = context.getDtoTypeName();

        String code = String.format(codeMaps.get(method),
                context.getFieldNameInRoot(),
                entityType != null ? entityType.toString() : "",
                dtoTypeName != null ? dtoTypeName.toString() : "");

        return code;
    }
}
