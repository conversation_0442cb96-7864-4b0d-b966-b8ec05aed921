package org.befun.core.annotation.generator;

import com.squareup.javapoet.*;
import org.befun.core.annotation.dto.GeneratorContext;
import org.befun.core.utils.TypeUtility;

import javax.annotation.processing.Filer;
import javax.annotation.processing.Messager;
import javax.lang.model.element.*;
import javax.lang.model.type.TypeKind;
import javax.lang.model.type.TypeMirror;
import javax.lang.model.util.Elements;
import javax.lang.model.util.Types;
import javax.tools.Diagnostic;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.*;

import static com.google.auto.common.MoreTypes.asTypeElement;

/**
 * The class description
 *
 * <AUTHOR>
 */

public abstract class BaseGenerator<A extends Annotation, C extends GeneratorContext> {
    protected Elements elementUtils;
    protected Messager messager;
    protected Filer filer;
    protected Types types;
    protected Class classAnnotationKlass;
    protected String suffix;
    protected Class[] staticImports;

    private static TypeName TYPE_LIST = ClassName.get(List.class);
    private static TypeName TYPE_SET = ClassName.get(Set.class);
    private static TypeName[] TYPE_LISTS = new TypeName[] {
            TYPE_LIST,
            TYPE_SET
    };

    /**
     *
     * @param suffix
     */
    protected BaseGenerator(String suffix, Class[] staticImports) {
        this.classAnnotationKlass = TypeUtility.getTypeParameterType(this.getClass(), BaseGenerator.class, 0);
        if (this.classAnnotationKlass == null) {
            messager.printMessage(Diagnostic.Kind.ERROR, "invalid class annotation");
        }
        this.suffix = suffix;
        this.staticImports = staticImports;
    }

    protected static boolean isListType(TypeName requestType) {
        return isFromType(requestType, TYPE_LISTS);
    }

    protected static boolean isFromType(TypeName requestType, TypeName[] expectedTypes) {
        if(requestType instanceof ParameterizedTypeName) {
            TypeName typeName = ((ParameterizedTypeName) requestType).rawType;
            for (TypeName expectedType : expectedTypes) {
                if (typeName.equals(expectedType)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * AnnotationProcessor内部设置
     * @param elementUtils
     * @param messager
     * @param filer
     */
    public void setup(Elements elementUtils, Messager messager, Filer filer, Types types) {
        this.elementUtils = elementUtils;
        this.messager = messager;
        this.filer = filer;
        this.types = types;
    }

    /**
     * @return
     */
    public Class getClassAnnotation() {
        return this.classAnnotationKlass;
    }

    /**
     * 后缀名设置，比如设置成dto，那么默认情况下package会变成 ***.dto, 类名会变成 TestDto
     * @return
     */
    public String getSuffix() { return this.suffix; }

    /**
     * 后缀名设置，比如设置成dto，那么默认情况下package会变成 ***.dto, 类名会变成 TestDto
     * @return
     */
    public Class[] getStaticImports() { return this.staticImports; }

    /**
     * 构建字段
     * @param clsElement
     * @param annotation
     * @return
     */
    public List<FieldSpec> buildFields(C context, TypeElement clsElement, A annotation) {
        List<FieldSpec> fieldSpecs = new ArrayList<>();
        return fieldSpecs;
    }

    /**
     * 构建函数
     * @param annotation
     * @return
     */
    public List<MethodSpec> buildMethods(C context, TypeElement clsElement, A annotation) {
        List<MethodSpec> methodSpecs = new ArrayList<>();
        return methodSpecs;
    }

    /**
     * 补充构建Class
     *  - superclass
     *  - modifier
     *  - ctor...
     */
    public C generateClass(TypeElement clsElement, A annotation, TypeSpec.Builder builder){
        return null;
    }

    interface IFieldVisitor {
        void visit(VariableElement element);
    }

    /**
     * 递归向上访问父类结构
     * @param element
     */
    protected void visitAllField(TypeElement element, IFieldVisitor visitor) {
        // visit itself
        for(Element fieldElem : element.getEnclosedElements()) {
            if (fieldElem instanceof VariableElement) {
                VariableElement variableElement = (VariableElement) fieldElem;
                visitor.visit(variableElement);
            }
        }

        // visit parent
        TypeMirror superclass = element.getSuperclass();
        if (superclass.getKind() == TypeKind.NONE) {
            return;
        }

        TypeElement parentElement = asTypeElement(superclass);
        visitAllField(parentElement, visitor);
    }
}
