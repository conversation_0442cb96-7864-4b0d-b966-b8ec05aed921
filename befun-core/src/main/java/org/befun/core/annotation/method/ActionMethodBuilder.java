package org.befun.core.annotation.method;

import com.squareup.javapoet.*;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.annotation.ResourceCollectionAction;
import org.befun.core.rest.annotation.ResourceInstanceAction;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.processing.Messager;
import javax.lang.model.element.AnnotationMirror;
import javax.lang.model.element.ExecutableElement;
import javax.lang.model.element.TypeElement;
import javax.lang.model.element.VariableElement;
import javax.lang.model.type.TypeMirror;
import javax.tools.Diagnostic;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ActionMethodBuilder implements MethodBuilder {

    private final ResourceGeneratorContext context;
    private final Messager messager;

    public ActionMethodBuilder(ResourceGeneratorContext context, Messager messager) {
        this.context = context;
        this.messager = messager;
    }

    private TypeName confirmReturnType(TypeName typeName) {
        if (typeName instanceof ParameterizedTypeName) {
            ParameterizedTypeName typeName2 = ((ParameterizedTypeName) typeName);
            List<TypeName> dataTypeName = typeName2.typeArguments.stream().map(this::confirmReturnType).collect(Collectors.toList());
            return ParameterizedTypeName.get(typeName2.rawType, dataTypeName.toArray(new TypeName[]{}));
        } else {
            return context.confirmTypeName(typeName);
        }
    }

    public List<MethodSpec> buildCollectionActions(List<ExecutableElement> actions) {
        List<MethodSpec.Builder> methods = new ArrayList<>();
        for (ExecutableElement action : actions) {
            String methodName = action.getSimpleName().toString();
            TypeName returnType = confirmReturnType(TypeName.get(action.getReturnType()));
            //TypeName returnType = ParameterizedTypeName.get(action.getReturnType());
            List<VariableElement> parameters = (List<VariableElement>) action.getParameters();
            List<String> strParams = parameters.stream().map(x -> x.getSimpleName().toString()).collect(Collectors.toList());
            messager.printMessage(Diagnostic.Kind.NOTE, "Generating [CA] -> " + action.getSimpleName());
            ResourceCollectionAction rca = action.getAnnotation(ResourceCollectionAction.class);
            List<ParameterSpec> params = new ArrayList<>();
            for (VariableElement p : parameters) {
                params.add(copyResourceMethodParameterAnnotations(p));
            }
            String code = "" +
                    String.format("return agent.%s(%s);\n", methodName, String.join(", ", strParams)) +
                    "";
            methods.add(buildCollectionMethod(rca.action(), rca.description(), rca.path(), rca.method(), params, returnType, code, null, context));
        }
        return methods.stream().filter(Objects::nonNull).map(MethodSpec.Builder::build).collect(Collectors.toList());
    }

    public List<MethodSpec> buildInstanceMethods(List<ExecutableElement> actions) {
        List<MethodSpec.Builder> methods = new ArrayList<>();
        for (ExecutableElement action : actions) {
            List<VariableElement> parameters = (List<VariableElement>) action.getParameters();
            VariableElement instanceParam = null;
            if (parameters.size() > 2) {
                messager.printMessage(Diagnostic.Kind.ERROR, "instance action support at most 2 parameters");
            }
            if (parameters.size() > 1) {
                instanceParam = parameters.get(1);
            }
            boolean useEntity = useEntity(parameters.get(0));
            String methodName = action.getSimpleName().toString();
            TypeName returnType = confirmReturnType(TypeName.get(action.getReturnType()));

            // exclude entity parameter
            String extraParams = parameters.subList(1, parameters.size()).stream()
                    .map(p -> p.getSimpleName().toString())
                    .collect(Collectors.joining(","));

            messager.printMessage(Diagnostic.Kind.NOTE, "Generating [IA] -> " + action.getSimpleName());
            ResourceInstanceAction rca = action.getAnnotation(ResourceInstanceAction.class);
            List<ParameterSpec> params = new ArrayList<>();
            if (instanceParam != null) {
                params.add(copyResourceMethodParameterAnnotations(instanceParam));
            }
            String code = String.format("return (%s)service.executeInstanceAction%s(id, instance -> agent.%s(instance%s));\n",
                    returnType.toString(), useEntity ? "2" : "", methodName, extraParams.length() > 0 ? "," + extraParams : "");

            methods.add(buildInstanceMethod(rca.action(), rca.description(), rca.path(), rca.method(), params, returnType, code, null, context));
        }
        return methods.stream().filter(Objects::nonNull).map(MethodSpec.Builder::build).collect(Collectors.toList());
    }

    private boolean useEntity(VariableElement variable) {
        return isSubEntity(TypeName.get(variable.asType()));
    }

    private boolean isSubEntity(TypeName subType) {
        if (subType.isPrimitive()) {
            return false;
        }
        String subName = subType.toString();
        if (subName.equalsIgnoreCase("void") || subName.equalsIgnoreCase("java.lang.Object")) {
            return false;
        }
        TypeElement subTypeElement = context.getElements().getTypeElement(subType.toString());
        if (subTypeElement == null) {
            return false;
        }
        TypeMirror baseEntityMirror = context.getElements().getTypeElement(BaseEntity.class.getName()).asType();
        TypeMirror subMirror = subTypeElement.asType();
        return context.getTypes().isSubtype(subMirror, baseEntityMirror);
    }

    /**
     * 复制方法参数上的注解
     */
    private ParameterSpec copyResourceMethodParameterAnnotations(VariableElement p) {
        ParameterSpec.Builder builder = ParameterSpec.builder(TypeName.get(p.asType()), p.getSimpleName().toString());
        List<? extends AnnotationMirror> as = p.getAnnotationMirrors();
        if (CollectionUtils.isNotEmpty(as)) {
            as.forEach(a -> {
                if (!a.getAnnotationType().toString().equals(TypeName.get(RequestBody.class).toString())) {
                    builder.addAnnotation(AnnotationSpec.get(a));
                }
            });
        }
        builder.addAnnotation(AnnotationSpec.builder(RequestBody.class).build());
        return builder.build();
    }
}
