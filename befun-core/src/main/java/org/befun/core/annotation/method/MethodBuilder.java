package org.befun.core.annotation.method;

import com.fasterxml.jackson.annotation.JsonView;
import com.squareup.javapoet.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.*;

import javax.lang.model.element.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public interface MethodBuilder {

    default String buildCode(Map<ResourceMethod, String> codeMaps, ResourceMethod method, ResourceGeneratorContext context) {
        return codeMaps.get(method);
    }

    default MethodSpec.Builder buildCollectionMethod(String action, String path, RequestMethod method, List<ParameterSpec> parameters, TypeName returnType, String code, Class<?> jsonView, ResourceGeneratorContext context) {
        return buildCollectionMethod(action, null, path, method, parameters, returnType, code, jsonView, context);
    }

    private String confirmAction(String action, String path) {
        if (StringUtils.isNotEmpty(action)) {
            return action;
        }
        if (path.contains("-")) {
            Matcher matcher = Pattern.compile("-(\\w)").matcher(path);
            StringBuilder sb = new StringBuilder();
            while (matcher.find()) {
                matcher.appendReplacement(sb, "-" + matcher.group(0).toLowerCase());
            }
            matcher.appendTail(sb);
            return sb.toString();
        }
        return path;
    }

    default MethodSpec.Builder buildCollectionMethod(String action, String doc, String path, RequestMethod method, List<ParameterSpec> parameters, TypeName returnType, String code, Class<?> jsonView, ResourceGeneratorContext context) {
        List<AnnotationSpec> annotations = new ArrayList<>();
        Class<?> mappingClass = buildMethodMapping(method);
        annotations.add(AnnotationSpec.builder(mappingClass)
                .addMember("value", "$S", path)
                .build());
        action = confirmAction(action, path);
        return this.buildMethod(action, doc, parameters, annotations, returnType, code, jsonView, context);
    }

    default MethodSpec.Builder buildInstanceMethod(String action, String doc, String path, RequestMethod method, List<ParameterSpec> parameters, TypeName returnType, String code, Class<?> jsonView, ResourceGeneratorContext context) {
        List<AnnotationSpec> annotations = new ArrayList<>();
        Class<?> mappingClass = buildMethodMapping(method);
        List<ParameterSpec> allParameters = new ArrayList<>();
        allParameters.add(
                ParameterSpec.builder(long.class, "id")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
        if (parameters != null) {
            allParameters.addAll(parameters);
        }
        annotations.add(AnnotationSpec.builder(mappingClass)
                .addMember("value", "$S", "/{id}/" + path)
                .build()
        );
        action = confirmAction(action, path);
        return this.buildMethod(action, doc, allParameters, annotations, returnType, code, jsonView, context);
    }


    default MethodSpec.Builder buildMethod(
            String action,
            String doc,
            List<ParameterSpec> parameters,
            List<AnnotationSpec> annotations,
            TypeName returnType,
            String code,
            Class<?> jsonView,
            ResourceGeneratorContext context) {
        MethodSpec.Builder builder = MethodSpec.methodBuilder(action)
                .addParameters(parameters)
                .addAnnotations(annotations)
                .addCode(code)
                .returns(returnType)
                .addAnnotation(
                        AnnotationSpec.builder(Operation.class)
                                .addMember("summary", "$S", StringUtils.isEmpty(doc) ? action : doc)
                                .build()
                )
                .addModifiers(Modifier.PUBLIC);
        if (StringUtils.isNotEmpty(context.getDocTag())) {
            builder.addAnnotation(AnnotationSpec.builder(Tag.class)
                    .addMember("name", "$S", context.getDocTag())
                    .build()
            );
        }
        if (jsonView != null) {
            builder.addAnnotation(AnnotationSpec.builder(JsonView.class)
                    .addMember("value", "$T.class", ClassName.get(jsonView))
                    .build()
            );
        }

        return builder;
    }

    default Class<?> buildMethodMapping(RequestMethod method) {
        return switch (method) {
            case POST -> PostMapping.class;
            case GET -> GetMapping.class;
            case PUT -> PutMapping.class;
            case DELETE -> DeleteMapping.class;
            default -> throw new RuntimeException("unexpected method " + method);
        };
    }
}
