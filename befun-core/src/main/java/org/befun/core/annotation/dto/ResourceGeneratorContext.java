package org.befun.core.annotation.dto;

import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.TypeName;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.annotation.*;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.utils.AnnotationUtility;

import javax.lang.model.element.TypeElement;
import javax.lang.model.type.DeclaredType;
import javax.lang.model.type.TypeMirror;
import javax.lang.model.util.Elements;
import javax.lang.model.util.Types;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ResourceGeneratorContext extends GeneratorContext {
    private Types types;
    private Elements elements;
    private String packageName;
    private String entityName;
    private TypeMirror entityType;
    private TypeName entityTypeName;
    private TypeMirror dtoType;
    private TypeName dtoTypeName;
    private TypeMirror serviceType;
    private TypeName serviceTypeName;
    private TypeMirror repositoryType;
    private TypeName repositoryTypeName;
    private List<ResourceMethod> excludeActions;
    private String docTag; // 添加到方法的 Tag#name
    private String docCrud; // 添加到方法的 Operation#summary
    private Map<ResourceMethod, TypeName> requestDtoTypeMap;
    private Map<ResourceMethod, Boolean> requestDtoValidMap;
    private Map<ResourceMethod, String> permissions = new HashMap<>();
    private Map<ResourceMethod, RequirePermissionBuilder> requirePermissions = new HashMap<>();

    // embedded deep
    private String fieldNameInRoot = "";
    // deep
    private String fieldNameInEmbedded = "";
    // deep
    private TypeMirror embeddedEntityType;

    public ResourceGeneratorContext(Elements elements,
                                    Types types,
                                    TypeElement element,
                                    TypeMirror entity,
                                    TypeMirror dto,
                                    TypeMirror service,
                                    TypeMirror repository,
                                    ResourceMethod[] excludeActions,
                                    String docTag,
                                    String docCrud,
                                    ResourcePermission[] permissions,
                                    ResourceMethodDto[] customDto) {
        this.elements = elements;
        this.types = types;
        this.packageName = elements.getPackageOf(element).getQualifiedName().toString();
        this.entityType = entity;
        this.entityName = types.asElement(entityType).getSimpleName().toString();
        this.entityTypeName = ClassName.get(entity);
        this.dtoType = dto;
        this.dtoTypeName = ClassName.get(dto);
        this.serviceType = service;
        this.serviceTypeName = ClassName.get(service);
        this.repositoryType = repository;
        this.repositoryTypeName = ClassName.get(repository);
        this.excludeActions = Arrays.stream(excludeActions).collect(Collectors.toList());
        this.docCrud = docCrud;
        initDocTag(element, docTag);
        initMethodDtoTypeMap(customDto);
        initPermissions(permissions);
        confirmDto();
    }

    public ResourceGeneratorContext(Elements elements, Types types, TypeElement element, ResourceController annotation) {
        this(elements, types, element,
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.entityClass()),
                ((DeclaredType) ((TypeElement) types.asElement(AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.serviceClass()))).getSuperclass()).getTypeArguments().get(1),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.serviceClass()),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.repositoryClass()),
                annotation.excludeActions(),
                annotation.docTag(),
                annotation.docCrud(),
                annotation.permissions(),
                annotation.methodDtoClass());
    }

    public ResourceGeneratorContext(Elements elements, Types types, TypeElement element, ResourceEmbeddedMany annotation, ResourceGeneratorContext root) {
        this(elements, types, element,
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.entityClass()),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.dtoClass()),
                root.getServiceType(),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.repositoryClass()),
                annotation.excludeActions(),
                annotation.docTag(),
                annotation.docCrud(),
                annotation.permissions(),
                annotation.methodDtoClass());
    }

    public ResourceGeneratorContext(Elements elements, Types types, TypeElement element, ResourceEmbeddedOne annotation, ResourceGeneratorContext root) {
        this(elements, types, element,
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.entityClass()),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.dtoClass()),
                root.getServiceType(),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.repositoryClass()),
                annotation.excludeActions(),
                annotation.docTag(),
                annotation.docCrud(),
                annotation.permissions(),
                annotation.methodDtoClass());
    }

    public ResourceGeneratorContext(Elements elements, Types types, TypeElement element, ResourceDeepEmbeddedMany annotation, ResourceGeneratorContext embedded) {
        this(elements, types, element,
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.entityClass()),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.dtoClass()),
                embedded.getServiceType(),
                AnnotationUtility.getTypeMirrorFromAnnotation(() -> annotation.repositoryClass()),
                annotation.excludeActions(),
                annotation.docTag(),
                annotation.docCrud(),
                annotation.permissions(),
                annotation.methodDtoClass());
        embeddedEntityType = embedded.getEntityType();
    }

    private void confirmDto() {
        dtoTypeName = confirmTypeName(dtoTypeName);
    }

    public TypeName confirmTypeName(TypeName typeName) {
        // 如果计算出的 dto 类型不存在，或者和默认类型相同，则直接只用默认类型
        TypeName defaultDtoType = getDefaultDtoType();
        String dtoName = typeName.toString();
        if (dtoName.equalsIgnoreCase("void")
                || dtoName.contains("<any>")
                || defaultDtoType.toString().equals(dtoName)
                || StringUtils.isEmpty(((ClassName) typeName).packageName())) {
            return defaultDtoType;
        } else {
            return typeName;
        }
    }

    private TypeName getDefaultDtoType() {
        String entityPackage = elements.getPackageOf(types.asElement(entityType)).getQualifiedName().toString();
        String dtoClassName = entityName + "Dto";
        return ClassName.get(entityPackage, dtoClassName);
    }

    public TypeName getRequestDtoTypeName(ResourceMethod method) {
        return requestDtoTypeMap.getOrDefault(method, this.dtoTypeName);
    }

    public boolean validMethodParam(ResourceMethod method) {
        return requestDtoValidMap.getOrDefault(method, false);
    }

    private void initMethodDtoTypeMap(ResourceMethodDto[] methodDtos) {
        requestDtoTypeMap = new HashMap<>();
        requestDtoValidMap = new HashMap<>();
        Arrays.stream(methodDtos).forEach(m -> {
            requestDtoValidMap.put(m.method(), m.valid());
            TypeMirror type = AnnotationUtility.getTypeMirrorFromAnnotation(m::dtoClass);
            if (type != null) {
                String typeName = type.toString();
                if (typeName.equalsIgnoreCase("void") || typeName.equalsIgnoreCase("java.lang.Object")) {
                    //ignore
                } else if (m.method() != ResourceMethod.FIND_ALL || isSubClass(type, ResourceCustomQueryDto.class)) {
                    // find all dto must extends ResourceCustomQueryDto
                    requestDtoTypeMap.put(m.method(), ClassName.get(type));
                }
            }
        });
    }

    private void initDocTag(TypeElement clsElement, String docTag) {
        if (StringUtils.isNotEmpty(docTag)) {
            this.docTag = docTag;
        } else {
            Tag tag = clsElement.getAnnotation(Tag.class);
            if (tag != null && StringUtils.isNotEmpty(tag.name())) {
                this.docTag = tag.name();
            }
        }
    }

    public boolean isOverrideFindAllDto() {
        return requestDtoTypeMap.containsKey(ResourceMethod.FIND_ALL);
    }

    private boolean isSubClass(TypeMirror subType, Class<?> superClass) {
        String subName = subType.toString();
        if (subName.equalsIgnoreCase("void") || subName.equalsIgnoreCase("java.lang.Object")) {
            return false;
        }
        TypeMirror superType = elements.getTypeElement(superClass.getName()).asType();
        return types.isSubtype(subType, superType);
    }

    private void initPermissions(ResourcePermission[] permissions) {
        for (ResourcePermission p : permissions) {
            ResourceMethod method = ResourceMethod.parseByName(p.action());
            if (method != null) {
                if (StringUtils.isNotEmpty(p.permission())) {
                    this.permissions.put(method, p.permission());
                }
                RequirePermissionBuilder builder = RequirePermissionBuilder.build(p);
                if (builder != null) {
                    this.requirePermissions.put(method, builder);
                }
            }
        }
    }

    @Getter
    public static class RequirePermissionBuilder {
        private String requirePermissions;
        private String needSuperAdmin;
        private String matchType;

        public static RequirePermissionBuilder build(ResourcePermission resourcePermission) {
            if (resourcePermission.needSuperAdmin() || resourcePermission.requirePermissions().length > 0) {
                RequirePermissionBuilder builder = new RequirePermissionBuilder();
                if (resourcePermission.needSuperAdmin()) {
                    builder.needSuperAdmin = String.valueOf(true);
                }
                if (resourcePermission.requirePermissions().length > 0) {
                    builder.requirePermissions = Arrays.stream(resourcePermission.requirePermissions()).map(i -> "\"" + i + "\"").collect(Collectors.joining(",", "{", "}"));
                    if (resourcePermission.matchType() != RequirePermissions.MatchType.ALL_MATCH) {
                        builder.matchType = "RequirePermissions.MatchType." + resourcePermission.matchType().name();
                    }
                }
                return builder;
            }
            return null;
        }
    }
}
