package org.befun.core.annotation.generator;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.squareup.javapoet.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.annotation.dto.GeneratorContext;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.utils.AnnotationUtility;

import javax.lang.model.element.AnnotationMirror;
import javax.lang.model.element.Modifier;
import javax.lang.model.element.TypeElement;
import javax.lang.model.element.VariableElement;
import javax.lang.model.type.MirroredTypeException;
import javax.lang.model.type.TypeMirror;
import javax.tools.Diagnostic;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class DtoGenerator extends BaseGenerator<DtoClass, GeneratorContext> {

    private TypeMirror baseEntityMirror;

    public DtoGenerator() {
        super("dto", new Class[]{JsonProperty.Access.class});
    }

    @Override
    public GeneratorContext generateClass(TypeElement clsElement, DtoClass dtoClass, TypeSpec.Builder builder) {
        baseEntityMirror = elementUtils.getTypeElement(BaseEntity.class.getName()).asType();
        GeneratorContext context = new GeneratorContext();

        TypeName superType;
        TypeMirror superClass = AnnotationUtility.getTypeMirrorFromAnnotation(() -> dtoClass.superClass());
        if (superClass.toString().equalsIgnoreCase("void")) {
            // use default dto
            superType = ParameterizedTypeName.get(
                    ClassName.get(BaseEntityDTO.class),
                    ClassName.get(clsElement));
        } else {
            if (dtoClass.superClassParameterizedType()){
                superType = ParameterizedTypeName.get(
                        ClassName.get((TypeElement) types.asElement(superClass)),
                        getDefaultDtoType(clsElement));
            }else {
                superType = ClassName.get(superClass);
            }
        }

        builder.superclass(superType)
                .addAnnotation(Getter.class)
                .addAnnotation(Setter.class);

        List<? extends TypeMirror> types = AnnotationUtility.getGenericTypeMirrorFromAnnotation(() -> dtoClass.interfaces());

        for (TypeMirror typeMirror : types) {
            builder.addSuperinterface(typeMirror);
        }

        builder.addMethod(
                MethodSpec.constructorBuilder()
                        .addModifiers(Modifier.PUBLIC)
                        .addParameter(ParameterSpec.builder(ClassName.get(clsElement), "entity").build())
                        .addCode("super(entity);\n")
                        .build()
        );

        builder.addMethod(
                MethodSpec.constructorBuilder()
                        .addModifiers(Modifier.PUBLIC)
                        .addCode("super();\n")
                        .build()
        );


        return context;
    }

    @Override
    public List<FieldSpec> buildFields(GeneratorContext context, TypeElement clsElement, DtoClass dtoClass) {
        List<FieldSpec> fieldSpecs = new ArrayList<>();
        boolean includeAllFields = dtoClass.includeAllFields();
        this.visitAllField(clsElement, (fieldElement) -> {
            DtoProperty fieldAnnotation = fieldElement.getAnnotation(DtoProperty.class);
            if (fieldAnnotation != null) {
                if (!fieldAnnotation.ignore()) {
                    String fieldName = fieldElement.getSimpleName().toString();
                    if (fieldName == null || fieldName.length() < 1) {
                        fieldName = fieldElement.getSimpleName().toString();
                    }
                    messager.printMessage(Diagnostic.Kind.NOTE, "Processing [FIELD] -> " + fieldName);
                    fieldSpecs.add(buildField(context, fieldElement, fieldName, fieldAnnotation));
                }
            } else if (includeAllFields) {
                Optional.ofNullable(buildField(fieldElement)).ifPresent(fieldSpecs::add);
            }
        });

        // 把id移到第一个
        fieldSpecs.stream().filter(i -> i.name.equals("id")).findFirst().ifPresent(i -> {
            if (fieldSpecs.remove(i)) {
                fieldSpecs.add(0, i);
            }
        });

        return fieldSpecs;
    }

    private boolean isSubEntity(TypeName subType) {
        if (subType.isPrimitive()){
            return false;
        }
        String subName = subType.toString();
        if (subName.equalsIgnoreCase("void") || subName.equalsIgnoreCase("java.lang.Object")) {
            return false;
        }
        TypeMirror subMirror = elementUtils.getTypeElement(subType.toString()).asType();
        return types.isSubtype(subMirror, baseEntityMirror);
    }

    private TypeName getDefaultDtoType(TypeElement typeElement){
        TypeMirror entityMirror = typeElement.asType();
        String entityPackage = elementUtils.getPackageOf(types.asElement(entityMirror)).getQualifiedName().toString();
        String dtoClassName = types.asElement(entityMirror).getSimpleName().toString() + "Dto";
        return ClassName.get(entityPackage, dtoClassName);
    }

    private TypeName getDefaultDtoType(TypeName entityType) {
        TypeMirror entityMirror = elementUtils.getTypeElement(entityType.toString()).asType();
        String entityPackage = elementUtils.getPackageOf(types.asElement(entityMirror)).getQualifiedName().toString();
        String dtoClassName = types.asElement(entityMirror).getSimpleName().toString() + "Dto";
        return ClassName.get(entityPackage, dtoClassName);
    }

    /**
     * 复制没有 @DtoProperty 的属性
     * 前提是 {@link DtoClass#includeAllFields()} 是 true
     * 并且要求字段上的注解没有 {@link javax.persistence.Transient}
     * 会复制 除了 javax.persistence org.hibernate.annotations 这个包路径下的 其他所有注解
     */
    private FieldSpec buildField(VariableElement element) {
        // 注解没有 @Transient
        List<AnnotationMirror> copy = new ArrayList<>();
        for (AnnotationMirror annotation : elementUtils.getAllAnnotationMirrors(element)) {
            String anno = annotation.getAnnotationType().toString();
            if (anno.equals("javax.persistence.Transient")) {
                return null;
            }
            if (anno.startsWith("javax.persistence") || anno.startsWith("org.hibernate.annotations")) {
                continue;
            }
            copy.add(annotation);
        }
        TypeName typeName = TypeName.get(element.asType());
        // 如果是超过2层的泛型，直接使用原类型
        if (typeName instanceof ParameterizedTypeName) {
            ParameterizedTypeName rawTypeName = (ParameterizedTypeName) typeName;
            List<TypeName> typeArgs = new ArrayList<>();
            boolean hasNestParameterizedType = false;
            for (TypeName typeName1 : ((ParameterizedTypeName) typeName).typeArguments) {
                if (typeName1 instanceof ParameterizedTypeName) {
                    hasNestParameterizedType = true;
                    break;
                } else if (isSubEntity(typeName1)) {
                    typeArgs.add(getDefaultDtoType(typeName1));
                } else {
                    typeArgs.add(typeName1);
                }
            }
            if (!hasNestParameterizedType) {
                typeName = ParameterizedTypeName.get(rawTypeName.rawType, typeArgs.toArray(new TypeName[0]));
            }
        } else if (isSubEntity(typeName)) {
            typeName = getDefaultDtoType(typeName);
        }
        String fieldName = element.getSimpleName().toString();
        FieldSpec.Builder builder = FieldSpec.builder(typeName, fieldName).addModifiers(Modifier.PRIVATE);
        copy.forEach(a -> builder.addAnnotation(AnnotationSpec.get(a)));
        return builder.build();
    }

    /**
     * 构建Field，并且复制Schema等必要annotation
     *
     * @return
     */
    protected FieldSpec buildField(GeneratorContext context, VariableElement element, String fieldName, DtoProperty
            dtoProperty) {
        TypeName typeName = TypeName.get(element.asType());

        TypeMirror typeMirror = AnnotationUtility.getTypeMirrorFromAnnotation(() -> dtoProperty.type());
        TypeName annotatedTypeName = TypeName.get(typeMirror);
        messager.printMessage(Diagnostic.Kind.NOTE, "build field --> " + annotatedTypeName.toString());

        if (!annotatedTypeName.equals(TypeName.OBJECT)) {
            // apply customized dto type
            ClassName cname = (ClassName) annotatedTypeName;
            if (typeName instanceof ParameterizedTypeName) {
                // generic type, for example list, set...
                TypeMirror genericTypeMirror = AnnotationUtility.getTypeMirrorFromAnnotation(() -> dtoProperty.genericType());
                TypeName genericType = TypeName.get(genericTypeMirror);
                ClassName rawType = ((ParameterizedTypeName) typeName).rawType;
                if (!genericType.equals(TypeName.OBJECT)) {
                    // override 默认generic type，比如set -> tree set
                    rawType = (ClassName) genericType;
                }
                typeName = ParameterizedTypeName.get(rawType, ClassName.get(cname.packageName(), cname.simpleName()));
            } else {
                // normal type
                typeName = ClassName.get(cname.packageName(), cname.simpleName());
            }
        }

        FieldSpec.Builder builder = FieldSpec.builder(typeName, fieldName).addModifiers(Modifier.PRIVATE);

        // Schema
        String example = dtoProperty.example();
        String description = dtoProperty.description();

        // TBD, 以后可能需要复制更多属性
        builder.addAnnotation(AnnotationSpec.builder(Schema.class)
                .addMember("example", "$S", example)
                .addMember("description", "$S", description)
                .build());

        TypeMirror jsonViewType = AnnotationUtility.getTypeMirrorFromAnnotation(() -> dtoProperty.jsonView());
        builder.addAnnotation(AnnotationSpec.builder(JsonView.class)
                .addMember("value", "$T.class", TypeName.get(jsonViewType))
                .build());

        builder.addAnnotation(AnnotationSpec.builder(JsonProperty.class)
                .addMember("access", "$L", dtoProperty.access())
                .build());
        return builder.build();
    }

    protected static TypeMirror getAnnotation(ResourceController annotation) {
        try {
            annotation.serviceClass();
        } catch (MirroredTypeException mte) {
            return mte.getTypeMirror();
        }
        return null;
    }
}
