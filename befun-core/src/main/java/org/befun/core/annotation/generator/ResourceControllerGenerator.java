package org.befun.core.annotation.generator;

import com.squareup.javapoet.*;
import io.swagger.v3.oas.annotations.Hidden;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.annotation.method.ActionMethodBuilder;
import org.befun.core.annotation.method.CrudMethodBuilder;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Generated;
import javax.lang.model.element.ExecutableElement;
import javax.lang.model.element.Modifier;
import javax.lang.model.element.TypeElement;
import javax.lang.model.type.TypeMirror;
import javax.tools.Diagnostic;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.befun.core.dto.resource.ResourceCollectionType.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ResourceControllerGenerator extends BaseGenerator<ResourceController, ResourceGeneratorContext> {

    public ResourceControllerGenerator() {
        super("proxy", new Class[]{});
    }

    @Override
    public ResourceGeneratorContext generateClass(TypeElement clsElement, ResourceController annotation, TypeSpec.Builder builder) {
        TypeMirror classType = clsElement.asType();
        ResourceGeneratorContext context = new ResourceGeneratorContext(elementUtils, types, clsElement, annotation);
        RequestMapping rm = clsElement.getAnnotation(RequestMapping.class);
        if (rm == null) {
            messager.printMessage(Diagnostic.Kind.ERROR, "controller should define @RequestMapping");
            return context;
        }
        if ("@Hidden".equals(context.getDocTag())) {
            builder.addAnnotation(Hidden.class);
        }

        String path = rm.value()[0];
        builder.addAnnotation(AnnotationSpec.builder(Generated.class)
                        .addMember("value", "$S", "Auto Generated")
                        .build()
                )
                .addAnnotation(RestController.class)
                .addAnnotation(AnnotationSpec.builder(RequestMapping.class)
                        .addMember("value", "$S", path)
                        .build()
                )
                .addAnnotation(Validated.class)
                .addModifiers(Modifier.PUBLIC)
                .addField(FieldSpec.builder(TypeName.get(classType), "agent", Modifier.PRIVATE)
                        .addAnnotation(Autowired.class)
                        .build()
                );

        builder.superclass(ParameterizedTypeName.get(
                ClassName.get(BaseController.class),
                context.getServiceTypeName()
        ));
//        switch (annotation.collectionType()) {
//            case COLLECTION:
//
//                break;
//        }

        if (StringUtils.isNotEmpty(annotation.permission())) {
            builder.addAnnotation(AnnotationSpec.builder(PreAuthorize.class)
                    .addMember("value",
                            "$S",
                            annotation.permission()
                    )
                    .build()
            );
        }

        List<Class<? extends Annotation>> copyAnnotation = List.of(
                ConditionalOnProperty.class,
                ConditionalOnBean.class,
                ConditionalOnMissingBean.class,
                ConditionalOnMissingClass.class);
        copyAnnotation.forEach(i -> Optional.ofNullable(clsElement.getAnnotation(i)).ifPresent(a -> builder.addAnnotation(AnnotationSpec.get(a))));

        return context;
    }

    @Override
    public List<FieldSpec> buildFields(ResourceGeneratorContext context, TypeElement clsElement, ResourceController annotation) {
        List<FieldSpec> fieldSpecs = new ArrayList<>();
        return fieldSpecs;
    }

    /**
     * 构建CRUD方法
     *
     * @param context
     * @param clsElement
     * @param annotation
     * @return
     */
    @Override
    public List<MethodSpec> buildMethods(ResourceGeneratorContext context, TypeElement clsElement, ResourceController annotation) {
        List<MethodSpec> methodSpecs = new ArrayList<>();

        ResourceCollectionType collectionType = annotation.collectionType();
        if (collectionType == SINGLE_TYPE) {
            methodSpecs.addAll(CrudMethodBuilder.buildSingleCrudMethods(context));
        } else if (collectionType == COLLECTION_NO_PAGE) {
            methodSpecs.addAll(CrudMethodBuilder.buildNoPageCollectionCrudMethods(context));
        } else if (collectionType == COLLECTION) {
            // build crud
            methodSpecs.addAll(CrudMethodBuilder.buildCollectionCrudMethods(context));

            // build action method
            ActionMethodBuilder actionMethodBuilder = new ActionMethodBuilder(context, messager);
            methodSpecs.addAll(actionMethodBuilder.buildCollectionActions(getCollectionActions(clsElement)));
            methodSpecs.addAll(actionMethodBuilder.buildInstanceMethods(getInstanceActions(clsElement)));

            // build embedded many methods
            ResourceEmbeddedMany[] rems = clsElement.getAnnotationsByType(ResourceEmbeddedMany.class);
            for (ResourceEmbeddedMany rem : rems) {
                ResourceGeneratorContext embeddedContext = new ResourceGeneratorContext(elementUtils, types, clsElement, rem, context);
                embeddedContext.setFieldNameInRoot(rem.fieldNameInRoot());
                String embeddedPath = "/{id}/" + rem.path();
                String embeddedName = embeddedContext.getEntityName();
                methodSpecs.addAll(CrudMethodBuilder.buildEmbeddedCollectionCrudMethods(embeddedContext, embeddedName, embeddedPath));

                // build deep embedded methods
                for (ResourceDeepEmbeddedMany rdem : rem.deepEmbeddedMany()) {
                    ResourceGeneratorContext deepEmbeddedContext = new ResourceGeneratorContext(elementUtils, types, clsElement, rdem, embeddedContext);
                    deepEmbeddedContext.setFieldNameInRoot(rem.fieldNameInRoot());
                    deepEmbeddedContext.setFieldNameInEmbedded(rdem.fieldNameInEmbedded());
                    String deepEmbeddedPath = embeddedPath + "/{eid}/" + rdem.path();
                    String deepEmbeddedName = embeddedContext.getEntityName() + deepEmbeddedContext.getEntityName();
                    methodSpecs.addAll(CrudMethodBuilder.buildDeepEmbeddedCollectionCrudMethods(deepEmbeddedContext, deepEmbeddedName, deepEmbeddedPath));
                }
            }

            // build embedded one methods
            ResourceEmbeddedOne[] remo = clsElement.getAnnotationsByType(ResourceEmbeddedOne.class);
            for (ResourceEmbeddedOne rem : remo) {
                ResourceGeneratorContext embeddedContext = new ResourceGeneratorContext(elementUtils, types, clsElement, rem, context);
                embeddedContext.setFieldNameInRoot(rem.fieldNameInRoot());
                String embeddedPath = "/{id}/" + rem.path();
                String embeddedName = embeddedContext.getEntityName();
                methodSpecs.addAll(CrudMethodBuilder.buildEmbeddedOneCollectionCrudMethods(embeddedContext, embeddedName, embeddedPath));
            }
        }
        return methodSpecs;
    }

    private List<ExecutableElement> getCollectionActions(TypeElement classElement) {
        return elementUtils.getAllMembers(classElement).stream()
                .filter(x -> x instanceof ExecutableElement && x.getAnnotation(ResourceCollectionAction.class) != null)
                .map(x -> (ExecutableElement) x)
                .collect(Collectors.toList());
    }

    private List<ExecutableElement> getInstanceActions(TypeElement classElement) {
        return elementUtils.getAllMembers(classElement).stream()
                .filter(x -> x instanceof ExecutableElement && x.getAnnotation(ResourceInstanceAction.class) != null)
                .map(x -> (ExecutableElement) x)
                .collect(Collectors.toList());
    }


}
