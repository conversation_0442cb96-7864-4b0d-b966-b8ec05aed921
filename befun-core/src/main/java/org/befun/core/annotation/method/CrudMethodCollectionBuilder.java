package org.befun.core.annotation.method;

import com.squareup.javapoet.*;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.dto.*;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;


public class CrudMethodCollectionBuilder implements CrudMethodBuilder {

    protected final ResourceGeneratorContext context;
    protected final Map<ResourceMethod, String> codeMaps = new HashMap<>();
    protected final List<ParameterSpec> defaultParams = new ArrayList<>();
    protected String suffix = "";
    protected String path = "";

    CrudMethodCollectionBuilder(ResourceGeneratorContext context) {
        this.context = context;
        initCodeMap();
    }

    @Override
    public ResourceGeneratorContext getContext() {
        return context;
    }

    @Override
    public void initCodeMap() {
        codeMaps.put(FIND_ALL, "return new ResourcePageResponseDto(service.findAll(params));");
        codeMaps.put(COUNT, "return new ResourceResponseDto(service.count(params));");
        codeMaps.put(FIND_ONE, "return new ResourceResponseDto(service.findOne(id));");
        codeMaps.put(UPDATE_ONE, "return new ResourceResponseDto(service.updateOne(id, data));");
        codeMaps.put(CREATE, "return new ResourceResponseDto(service.create(data));");
        codeMaps.put(DELETE_ONE, "return new ResourceResponseDto(service.deleteOne(id));");
        codeMaps.put(BATCH_UPDATE, "return new ResourceListResponseDto(service.batchUpdate(changes));");
    }

    protected String resourcePath(List<ParameterSpec> params) {
        String resourcePath = "/{id}";
        params.add(
                ParameterSpec.builder(long.class, "id")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
        return resourcePath;
    }

    protected String crudDoc(ResourceMethod method) {
        return method.formatDoc(context.getDocCrud());
    }

    @Override
    public MethodSpec.Builder findAll() {
        ResourceMethod method = FIND_ALL;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        if (context.isOverrideFindAllDto()) {
            ParameterSpec.Builder builder = ParameterSpec.builder(context.getRequestDtoTypeName(method), "params")
                    .addAnnotation(ResourceQueryCustom.class);
            if (getContext().validMethodParam(method)) {
                builder.addAnnotation(Valid.class);
            }
            params.add(builder.build());
        } else {
            params.add(
                    ParameterSpec.builder(ParameterizedTypeName.get(
                                    ClassName.get(ResourceEntityQueryDto.class),
                                    context.getDtoTypeName()), "params")
                            .addAnnotation(
                                    AnnotationSpec.builder(ResourceQueryPredicate.class)
                                            .build()
                            )
                            .addAnnotation(Valid.class)
                            .build()
            );
        }
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path, RequestMethod.GET, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourcePageResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Basic.class,
                context);
    }

    @Override
    public MethodSpec.Builder count() {
        ResourceMethod method = COUNT;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        params.add(
                ParameterSpec.builder(
                                ParameterizedTypeName.get(
                                        ClassName.get(ResourceEntityQueryDto.class),
                                        context.getDtoTypeName()),
                                "params"
                        )
                        .addAnnotation(AnnotationSpec.builder(ResourceQueryPredicate.class)
                                .build()
                        )
                        .addAnnotation(Valid.class)
                        .build()
        );
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path + "/count", RequestMethod.GET, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        ClassName.get(CountDto.class)
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Basic.class,
                context);
    }

    @Override
    public MethodSpec.Builder create() {
        ResourceMethod method = CREATE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        params.add(dtoParam(method));
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path, RequestMethod.POST,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder findOne() {
        ResourceMethod method = FIND_ONE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        String newPath = resourcePath(params);
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), newPath, RequestMethod.GET,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder updateOne() {
        ResourceMethod method = UPDATE_ONE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        String newPath = resourcePath(params);
        params.add(dtoParam(method));
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), newPath, RequestMethod.PUT, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder deleteOne() {
        ResourceMethod method = DELETE_ONE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        String newPath = resourcePath(params);
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), newPath, RequestMethod.DELETE,
                params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceResponseDto.class),
                        ClassName.get(Boolean.class)
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Detail.class,
                context);
    }

    @Override
    public MethodSpec.Builder batchUpdate() {
        ResourceMethod method = BATCH_UPDATE;
        List<ParameterSpec> params = new ArrayList<>(defaultParams);
        params.add(
                ParameterSpec.builder(
                                ParameterizedTypeName.get(
                                        ClassName.get(ResourceBatchUpdateRequestDto.class),
                                        context.getRequestDtoTypeName(method)
                                ), "changes")
                        .addAnnotation(RequestBody.class)
                        .build()
        );
        return buildCollectionMethod(method.getName() + suffix, crudDoc(method), path + "/batch", RequestMethod.POST, params,
                ParameterizedTypeName.get(
                        ClassName.get(ResourceListResponseDto.class),
                        context.getDtoTypeName()
                ),
                buildCode(codeMaps, method, context),
                ResourceViews.Basic.class,
                context);
    }
}
