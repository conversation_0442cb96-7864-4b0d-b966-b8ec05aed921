package org.befun.core.annotation.method;

import com.squareup.javapoet.ParameterSpec;
import com.squareup.javapoet.TypeName;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.PathVariable;

import javax.lang.model.type.TypeMirror;
import java.util.List;
import java.util.Map;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

public class CrudMethodCollectionDeepEmbeddedBuilder extends CrudMethodCollectionBuilder {

    CrudMethodCollectionDeepEmbeddedBuilder(ResourceGeneratorContext context, String suffix, String path) {
        super(context);
        this.suffix = suffix;
        this.path = path;
        defaultParams.add(
                ParameterSpec.builder(long.class, "id")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
        defaultParams.add(
                ParameterSpec.builder(long.class, "eid")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
    }

    @Override
    protected String resourcePath(List<ParameterSpec> params) {
        String resourcePath = path + "/{did}";
        params.add(
                ParameterSpec.builder(long.class, "did")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
        return resourcePath;
    }

    @Override
    public void initCodeMap() {
        codeMaps.put(FIND_ALL, "return new ResourcePageResponseDto(service.findAllDeepEmbeddedMany(id, \"%s\", eid, \"%s\", %s.class, %s.class, %s.class, params));");
        codeMaps.put(COUNT, "return new ResourceResponseDto(service.countDeepEmbeddedMany(id, \"%s\", eid, \"%s\", %s.class, %s.class, %s.class, params));");
        codeMaps.put(FIND_ONE, "return new ResourceResponseDto(service.findOneDeepEmbeddedMany(id, \"%s\", eid, \"%s\", did, %s.class, %s.class, %s.class));");
        codeMaps.put(DELETE_ONE, "return new ResourceResponseDto(service.deleteOneDeepEmbeddedMany(id, \"%s\", eid, \"%s\", did, %s.class, %s.class, %s.class));");
        codeMaps.put(UPDATE_ONE, "return new ResourceResponseDto(service.updateOneDeepEmbeddedMany(id, \"%s\", eid, \"%s\", did, %s.class, %s.class, %s.class, data));");
        codeMaps.put(CREATE, "return new ResourceResponseDto(service.createDeepEmbeddedMany(id, \"%s\", eid, \"%s\", %s.class, %s.class, %s.class, data));");
        codeMaps.put(BATCH_UPDATE, "return new ResourceListResponseDto(service.batchUpdateDeepEmbeddedMany(id, \"%s\", eid, \"%s\", %s.class, %s.class, %s.class, changes));");
    }

    @Override
    public String buildCode(Map<ResourceMethod, String> codeMaps, ResourceMethod method, ResourceGeneratorContext context) {
        if (!codeMaps.containsKey(method)) {
            throw new RuntimeException("invalid method code " + method.getName());
        }

        TypeMirror entityType = context.getEntityType();
        TypeName dtoTypeName = context.getDtoTypeName();

        String code = String.format(codeMaps.get(method),
                context.getFieldNameInRoot(),
                context.getFieldNameInEmbedded(),
                entityType != null ? context.getEmbeddedEntityType().toString() : "",
                entityType != null ? entityType.toString() : "",
                dtoTypeName != null ? dtoTypeName.toString() : "");

        return code;
    }
}
