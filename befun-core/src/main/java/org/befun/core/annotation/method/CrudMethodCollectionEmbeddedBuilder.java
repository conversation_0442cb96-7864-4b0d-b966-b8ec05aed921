package org.befun.core.annotation.method;

import com.squareup.javapoet.ParameterSpec;
import com.squareup.javapoet.TypeName;
import org.befun.core.annotation.dto.ResourceGeneratorContext;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.springframework.web.bind.annotation.PathVariable;

import javax.lang.model.type.TypeMirror;
import java.util.List;
import java.util.Map;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

public class CrudMethodCollectionEmbeddedBuilder extends CrudMethodCollectionBuilder {

    CrudMethodCollectionEmbeddedBuilder(ResourceGeneratorContext context, String suffix, String path) {
        super(context);
        this.suffix = suffix;
        this.path = path;
        defaultParams.add(
                ParameterSpec.builder(long.class, "id")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
    }

    @Override
    protected String resourcePath(List<ParameterSpec> params) {
        String resourcePath = path + "/{eid}";
        params.add(
                ParameterSpec.builder(long.class, "eid")
                        .addAnnotation(PathVariable.class)
                        .build()
        );
        return resourcePath;
    }

    @Override
    public void initCodeMap() {
        codeMaps.put(FIND_ALL, "return new ResourcePageResponseDto(service.findAllEmbeddedMany(id, \"%s\", %s.class, %s.class, params));");
        codeMaps.put(COUNT, "return new ResourceResponseDto(service.countEmbeddedMany(id, \"%s\", %s.class, %s.class, params));");
        codeMaps.put(FIND_ONE, "return new ResourceResponseDto(service.findOneEmbeddedMany(id, \"%s\", eid, %s.class, %s.class));");
        codeMaps.put(DELETE_ONE, "return new ResourceResponseDto(service.deleteOneEmbeddedMany(id, \"%s\", eid, %s.class, %s.class));");
        codeMaps.put(UPDATE_ONE, "return new ResourceResponseDto(service.updateOneEmbeddedMany(id, \"%s\", eid, %s.class, %s.class, data));");
        codeMaps.put(CREATE, "return new ResourceResponseDto(service.createEmbeddedMany(id, \"%s\", %s.class, %s.class, data));");
        codeMaps.put(BATCH_UPDATE, "return new ResourceListResponseDto(service.batchUpdateEmbeddedMany(\"%s\", %s.class, %s.class, changes));");
    }

    @Override
    public String buildCode(Map<ResourceMethod, String> codeMaps, ResourceMethod method, ResourceGeneratorContext context) {
        if (!codeMaps.containsKey(method)) {
            throw new RuntimeException("invalid method code " + method.getName());
        }

        TypeMirror entityType = context.getEntityType();
        TypeName dtoTypeName = context.getDtoTypeName();

        String code = String.format(codeMaps.get(method),
                context.getFieldNameInRoot(),
                entityType != null ? entityType.toString() : "",
                dtoTypeName != null ? dtoTypeName.toString() : "");

        return code;
    }
}
