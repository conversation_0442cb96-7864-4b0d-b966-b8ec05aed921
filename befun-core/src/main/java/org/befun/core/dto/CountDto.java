package org.befun.core.dto;

import org.befun.core.rest.view.ResourceViews;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class CountDto {
    private static final long serialVersionUID = 6529685098267757690L;

    @JsonView(ResourceViews.Basic.class)
    private long total;

    @JsonView(ResourceViews.Basic.class)
    private List<CountItemDto> items = new ArrayList<>();

    public CountDto(long total) {
        this.total = total;
    }

    public CountDto(long total, List<CountItemDto> items) {
        this.total = total;
        this.items = items;
    }
}
