package org.befun.core.dto.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.DateHelper;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.util.Collection;
import java.util.Date;

@NoArgsConstructor
@Getter
@Setter
public class ResourceQueryCriteria extends BaseDTO {
    private String key;
    private Object value;
    private QueryOperator operator = QueryOperator.EQUAL;
    private String paramKey; // 支持 _or 查询条件
    private String join; // 支持 join

    /**
     * 自动解析
     *
     * @param key
     * @param value
     */
    public ResourceQueryCriteria(String key, Object value) {
        this(key, value, QueryOperator.EQUAL);
    }

    public ResourceQueryCriteria(String key, Object value, QueryOperator operator) {
        setKey(key);
        this.value = value;
        this.operator = operator;
        this.paramKey = key + "_" + operator.getAlias();
    }

    public void setKey(String key) {
        if (key.contains(".")) {
            String[] ss = key.split("\\.");
            if (ss.length > 2) {
                throw new BadRequestException(String.format("not support param key %s", key));
            }
            join = ss[0];
            key = ss[1];
        }
        this.key = key;
    }

    public Predicate toPredicate(Path<?> root, CriteriaBuilder cb) {
        switch (operator) {
            case EQUAL:
                return cb.equal(root.get(this.key), this.value);
            case NOT_EQUAL:
                return cb.notEqual(root.get(this.key), this.value);
            case CONTAIN:
            case LIKE:
                return cb.like(root.get(this.key), '%' + this.value.toString() + '%');
            case L_LIKE:
                return cb.like(root.get(this.key), '%' + this.value.toString());
            case R_LIKE:
                return cb.like(root.get(this.key), this.value.toString() + '%');
            case IS_NULL:
                return root.get(this.key).isNull();
            case NOT_NULL:
                return root.get(this.key).isNotNull();
            case IN:
                return root.get(this.key).in((Collection<?>) this.value);
            case NOT_IN:
                return cb.not(root.get(this.key).in((Collection<?>) this.value));
            case LESS_THAN:
                return cb.lessThan(root.get(this.key), formatEndDate(this.value));
            case LESS_THAN_EQUAL:
                return cb.lessThanOrEqualTo(root.get(this.key), formatEndDate(this.value));
            case GREATER_THAN:
                return cb.greaterThan(root.get(this.key), (Comparable) this.value);
            case GREATER_THAN_EQUAL:
                return cb.greaterThanOrEqualTo(root.get(this.key), (Comparable) this.value);
            default:
                throw new BadRequestException("invalid query");
        }
    }

    private Comparable formatEndDate(Object value) {
        if (value instanceof Date) {
            Date endDate = (Date) value;
            return DateHelper.atEndOfDay(endDate);
        }
        return (Comparable) value;
    }
}
