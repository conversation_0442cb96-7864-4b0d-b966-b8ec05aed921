package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.entity.SequenceAware;

public class BaseEntityDTO<E> extends BaseDTO implements Comparable<BaseEntityDTO<E>> {
    protected E entity;

    public BaseEntityDTO(E entity) {
        this.entity = entity;
        afterHasEntity(entity);
    }

    public BaseEntityDTO() {
    }

    @JsonIgnore
    public E getEntity() {
        return this.entity;
    }

    public void setEntity(E entity) {
        boolean callback = this.entity == null;
        this.entity = entity;
        if (callback) {
            afterHasEntity(entity);
        }
    }

    public Long getId() {
        throw new NotImplementedException();
    }

    public void afterHasEntity(E entity) {

    }

    /**
     * 默认执行顺序
     * - 优先看SequenceAware
     * - 其次按照id排序
     *
     * @param o
     * @return
     */
    @Override
    public int compareTo(BaseEntityDTO<E> o) {
        Long lid = this.getId();
        Long rid = o.getId();
        if (lid == null || rid == null) {
            return this.hashCode() - o.hashCode();
        }

        if (lid == rid) {
            return 0;
        } else if (SequenceAware.class.isAssignableFrom(this.getClass())) {
            Integer ls = ((SequenceAware) this).getSequence();
            Integer rs = ((SequenceAware) o).getSequence();

            if (ls != null && rs != null) {
                // 如果sequence一样，需要看id
                return ls != rs ? ls - rs : (int) (lid - rid);
            }
        }

        // fallback，最终看id
        return this.getId().compareTo(o.getId());
    }
}
