package org.befun.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ResourceUpdateItemRequestDto<T extends BaseEntityDTO> extends BaseDTO {
    private static final long serialVersionUID = 6529685098267757690L;

    @Schema(description = "修改的id列表")
    Long id;

    @Schema(description = "批量修改数值")
    T data;
}
