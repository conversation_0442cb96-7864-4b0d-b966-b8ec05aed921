package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
@NoArgsConstructor
public class ResourcePermissionUserDto extends ResourcePermissionPartDto {

    @JsonView(ResourceViews.Basic.class)
    private Boolean isAdmin;

    @JsonView(ResourceViews.Basic.class)
    private String truename;

    @JsonView(ResourceViews.Basic.class)
    private String avatar;

    @JsonView(ResourceViews.Basic.class)
    private String email;

    @JsonView(ResourceViews.Basic.class)
    private String mobile;


    public ResourcePermissionUserDto(Long id, Boolean isAdmin, String truename, String avatar, String email, String mobile) {
        this(id, isAdmin, truename, avatar, email, mobile, null);
    }

    public ResourcePermissionUserDto(Long id, Boolean isAdmin, String truename, String avatar, String email, String mobile, String type) {
        super(id, truename, type);
        this.isAdmin = isAdmin;
        this.truename = truename;
        this.avatar = avatar;
        this.email = email;
        this.mobile = mobile;
    }

    public ResourcePermissionUserDto copy() {
        ResourcePermissionUserDto dto = new ResourcePermissionUserDto();
        dto.setId(getId());
        dto.setTruename(truename);
        dto.setAvatar(avatar);
        dto.setEmail(email);
        dto.setMobile(mobile);
        dto.setType(getType());
        dto.setName(getName());
        return dto;
    }

    public String getName() {
        return truename;
    }
}
