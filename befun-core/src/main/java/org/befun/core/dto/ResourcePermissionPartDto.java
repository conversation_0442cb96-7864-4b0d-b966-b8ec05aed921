package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
public class ResourcePermissionPartDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private Long id;

    @JsonView(ResourceViews.Basic.class)
    private String name;

    @JsonView(ResourceViews.Basic.class)
    private String type;

    public ResourcePermissionPartDto() {
    }

    public ResourcePermissionPartDto(Long id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
    }


}
