package org.befun.core.dto.query;

import org.befun.core.dto.BaseDTO;
import lombok.*;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.Min;
import java.util.List;

@Setter
@Getter
public class ResourceQueryRequestDto extends BaseDTO {
    private List<SearchCriteria> criteriaList;
    private int limit = 20;
    private String by = null;
    private String q = null;
    private String or= null;

    @Min(1)
    private int page = 0;

    private Sort sort = Sort.by(Sort.Direction.ASC,"id");
}
