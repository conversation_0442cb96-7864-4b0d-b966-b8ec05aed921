package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
public class ResourcePermissionDepartmentDto extends ResourcePermissionPartDto {

    @JsonView(ResourceViews.Basic.class)
    private String code;

    public ResourcePermissionDepartmentDto() {
    }

    public ResourcePermissionDepartmentDto(Long id, String name, String type, String code) {
        super(id, name, type);
        this.code = code;
    }
}
