package org.befun.core.dto.annotation;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.befun.core.rest.view.ResourceViews;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface DtoProperty {
    /* 是否是 findAll _q 的查询字段*/
    boolean queryable() default false;

    /* @Schema->description标签 */
    String description() default "";

    /* @Schema->example */
    String example() default "";

    /* json view */
    Class jsonView() default ResourceViews.Detail.class;

    /* access */
    JsonProperty.Access access() default JsonProperty.Access.READ_WRITE;

    /* type */
    Class type() default Object.class;

    /* genericType */
    Class genericType() default Object.class;

    /**
     * true 忽略此字段
     */
    boolean ignore() default false;
}

