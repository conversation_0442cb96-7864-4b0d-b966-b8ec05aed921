package org.befun.core.dto.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.CustomQueryType;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.validation.ValidSearchText;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Set;

@Setter
@Getter
public abstract class ResourceCustomQueryWithGroupDto<
        E extends BaseEntity, D extends BaseEntityDTO<E>,
        GE extends BaseEntity, GD extends BaseEntityDTO<GE>
        > extends ResourceCustomQueryDto {

    @Schema(description = "查询类型(默认owner)：owner, share, all")
    private String type = "owner";

    @Schema(description = "控制返回列表中是否包含文件夹的条件之一：只有为true且无搜索条件(_q=&_sort=&groupId=)时才会包含文件夹")
    private Boolean hasGroup;

    @Schema(description = "文件夹id")
    private Long groupId;

    @JsonProperty("_q")
    @ValidSearchText
    @Schema(description = "关键字")
    private String q;

    private long unboxGroupId() {
        return getGroupId() == null ? 0 : getGroupId();
    }

    public CustomQueryType customQueryType() {
        return CustomQueryType.parseType(getType(), () ->
                getHasGroup() != null && getHasGroup()
                        && StringUtils.isEmpty(getQ())
                        && StringUtils.isEmpty(getSort())
                        && unboxGroupId() <= 0);
    }

    public ResourceEntityQueryDto<GD> transformQueryGroup() {
        ResourceEntityQueryDto<GD> queryDto = new ResourceEntityQueryDto<>();
        queryDto.setSorts(Sort.by("createTime").descending());
        return queryDto;
    }

    public ResourceEntityQueryDto<D> transformQueryResource(Set<Long> excludeGroupIds) {
        ResourceEntityQueryDto<D> queryDto = transform();
        if (StringUtils.isNotEmpty(getQ())) {
            queryDto.setQ(getQ());
        }
        if (unboxGroupId() > 0) {
            queryDto.addCriteria(new ResourceQueryCriteria("groupId", unboxGroupId()));
        }
        if (CollectionUtils.isNotEmpty(excludeGroupIds)) {
            List<ResourceQueryCriteria> notInOrNull = List.of(
                    new ResourceQueryCriteria("groupId", excludeGroupIds, QueryOperator.NOT_IN),
                    new ResourceQueryCriteria("groupId", null, QueryOperator.IS_NULL)
            );
            queryDto.addAllCriteria(notInOrNull);
            queryDto.addOr(notInOrNull);
        }
//        if (queryDto.getSorts() == null || queryDto.getSorts().isUnsorted()) {
//            queryDto.setSorts(Sort.by("createTime").descending());
//        }
        return queryDto;
    }
}
