package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPermissions {
    private static final long serialVersionUID = 6529685098267757690L;

    private List<String> Action;
    private List<String> Dashboard;

    @JsonProperty("Action")
    public List<String> getAction() {
        return Action;
    }

    @JsonProperty("Action")
    public void setAction(List<String> action) {
        Action = action;
    }

    @JsonProperty("Dashboard")
    public List<String> getDashboard() {
        return Dashboard;
    }

    @JsonProperty("Dashboard")
    public void setDashboard(List<String> dashboard) {
        Dashboard = dashboard;
    }

}
