package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonProperty.Access.READ_ONLY;

@Getter
@Setter
public class BaseResourcePermissionEntityDto<E extends EnterpriseEntity> extends BaseEntityDTO<E> {

    @JsonView(ResourceViews.Basic.class)
    @JsonProperty(access = READ_ONLY)
    private List<ResourcePermissionUserDto> permissionUsers;

    @JsonView(ResourceViews.Basic.class)
    @JsonProperty(access = READ_ONLY)
    private List<ResourcePermissionRoleDto> permissionRoles;

    @JsonView(ResourceViews.Basic.class)
    @JsonProperty(access = READ_ONLY)
    private List<ResourcePermissionDepartmentDto> permissionDepartments;

    @JsonView(ResourceViews.Basic.class)
    @JsonProperty(access = READ_ONLY)
    private Boolean hasShared;

    public BaseResourcePermissionEntityDto() {
    }

    public BaseResourcePermissionEntityDto(E entity) {
        super(entity);
    }

    public Boolean getHasShared() {
        return CollectionUtils.isNotEmpty(getPermissionUsers())
                || CollectionUtils.isNotEmpty(getPermissionRoles())
                || CollectionUtils.isNotEmpty(getPermissionDepartments());
    }

}
