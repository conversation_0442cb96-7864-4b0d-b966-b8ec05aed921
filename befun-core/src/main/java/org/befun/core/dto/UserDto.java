package org.befun.core.dto;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {
    private static final long serialVersionUID = 6529685098267757690L;

    private Long id;
    private Long orgId;
    private List<Long> roleIds;
    private List<Long> departmentIds;
    private List<Long> subDepartmentIds;
    private String orgCode;
    private String username;
    private String password;
    private String plan;
    private Boolean isAdmin = false;
    private Boolean isTop = false;
    private UserPermissions permissions;
    private List<String> userEvents;
    private Boolean activeSession = false;
}
