package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
public class CountItemDto {
    private static final long serialVersionUID = 6529685098267757690L;

    @JsonView(ResourceViews.Basic.class)
    private String name;

    @JsonView(ResourceViews.Basic.class)
    private Long count;

    public CountItemDto(String name, Long count) {
        this.name = name;
        this.count = count;
    }
}
