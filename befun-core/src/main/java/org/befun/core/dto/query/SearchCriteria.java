package org.befun.core.dto.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.SearchOperator;
import org.befun.core.dto.BaseDTO;

import javax.persistence.criteria.JoinType;

@NoArgsConstructor
@Getter
@Setter
public class SearchCriteria extends BaseDTO {
    private String originParam;
    private String key;
    private JoinType firstJoin;
    private String subKey;
    private JoinType secondJoin;
    private String thirdlyKey;
    private Object value;
    private SearchOperator operator = SearchOperator.EQUAL;

    public SearchCriteria(String key, Object value) {
        this.key = key;
        this.value = value;
    }

    public SearchCriteria(String key, Object value, SearchOperator operator) {
        this.key = key;
        this.value = value;
        this.operator = operator;

    }

    public SearchCriteria(String key, Object value, SearchOperator operator, Class<?> fieldClass) {
        this.key = key;
        this.value = value;
        this.operator = operator;

    }

    public SearchCriteria(String key, String subKey, Object value, String operator) {
        this.key = key;
        this.subKey = subKey;
        this.value = value;
        this.operator = SearchOperator.valueOfLabel(operator);
    }

    public SearchCriteria(String key, String subKey, String thirdlyKey, Object value, String operator) {
        this.key = key;
        this.subKey = subKey;
        this.thirdlyKey = thirdlyKey;
        this.value = value;
        this.operator = SearchOperator.valueOfLabel(operator);
    }

    public SearchCriteria(String key, JoinType firstJoin, String subKey, JoinType secondJoin, String thirdlyKey, Object value, String operator) {
        this.key = key;
        this.firstJoin = firstJoin;
        this.subKey = subKey;
        this.secondJoin = secondJoin;
        this.thirdlyKey = thirdlyKey;
        this.value = value;
        this.operator = SearchOperator.valueOfLabel(operator);
    }
}
