package org.befun.core.dto;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class ResourcePermissionRoleDto extends ResourcePermissionPartDto {

    public ResourcePermissionRoleDto() {
    }

    public ResourcePermissionRoleDto(Long id, String name, String type) {
        super(id, name, type);
    }

    public ResourcePermissionRoleDto copy() {
        ResourcePermissionRoleDto dto = new ResourcePermissionRoleDto();
        dto.setId(getId());
        dto.setName(getName());
        dto.setType(getType());
        return dto;
    }
}
