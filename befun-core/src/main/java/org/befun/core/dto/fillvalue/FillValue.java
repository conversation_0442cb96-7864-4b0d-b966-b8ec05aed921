package org.befun.core.dto.fillvalue;

import java.util.function.BiConsumer;
import java.util.function.Function;

public interface FillValue<O, V> {

    Function<O, Long> getRelationId();

    BiConsumer<O, V> setRelationValue();

    static <O, V> FillValue<O, V> create(Function<O, Long> getRelationId, BiConsumer<O, V> setValue) {

        return new FillValue<>() {

            @Override
            public Function<O, Long> getRelationId() {
                return getRelationId;
            }

            @Override
            public BiConsumer<O, V> setRelationValue() {
                return setValue;
            }
        };
    }

}
