package org.befun.core.dto;

import org.befun.core.rest.view.ResourceViews;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ResourcePageMetaDto {
    private static final long serialVersionUID = 6529685098267757690L;

    @JsonView(ResourceViews.Basic.class)
    private long total;

    @JsonView(ResourceViews.Basic.class)
    private int limit;

    @JsonView(ResourceViews.Basic.class)
    private int page;
}
