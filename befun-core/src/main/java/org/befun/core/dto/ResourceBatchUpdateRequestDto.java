package org.befun.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class ResourceBatchUpdateRequestDto<T extends BaseEntityDTO> extends BaseDTO {
    private static final long serialVersionUID = 6529685098267757690L;

    @Schema(description = "修改列表")
    List<ResourceUpdateItemRequestDto<T>> changes = new ArrayList<>();

//    public ResourceBatchUpdateRequestDto(List<ResourceUpdateItemRequestDto<T>> changes) {
//        this.changes = changes;
//    }

    public ResourceBatchUpdateRequestDto(List<T> changes) {
        if (CollectionUtils.isNotEmpty(changes)) {
            this.changes = changes.stream().filter(j -> j.getId() != null).map(i -> new ResourceUpdateItemRequestDto<>(i.getId(), i)).collect(Collectors.toList());
        }
    }
}
