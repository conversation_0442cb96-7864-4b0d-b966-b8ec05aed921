package org.befun.core.dto;

import org.befun.core.rest.view.ResourceViews;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ResourcePageResponseDto<T> extends BaseResponseDto {
    private static final long serialVersionUID = 6529685098267757690L;

    @JsonView(ResourceViews.Basic.class)
    List<T> items;

    @JsonView(ResourceViews.Basic.class)
    private ResourcePageMetaDto meta = new ResourcePageMetaDto();

    public ResourcePageResponseDto(Page<T> page) {
        this.items = page.toList();
        this.meta.setPage(page.getNumber() + 1);
        this.meta.setTotal(page.getTotalElements());
        this.meta.setLimit(page.getSize());
    }

    public ResourcePageResponseDto(List<T> items, int page, long total, int limit) {
        this.items = items;
        this.meta.setPage(page);
        this.meta.setTotal(total);
        this.meta.setLimit(limit);
    }
}
