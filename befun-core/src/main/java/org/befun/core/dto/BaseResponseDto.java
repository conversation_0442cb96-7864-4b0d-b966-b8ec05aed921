package org.befun.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import org.befun.core.exception.BaseException;
import org.befun.core.rest.view.ResourceViews;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class BaseResponseDto<T> extends BaseDTO {
    private static final long serialVersionUID = 6529685098267757690L;

    @JsonView(ResourceViews.Basic.class)
    private int code = 200;

    @JsonView(ResourceViews.Basic.class)
    private int internalCode = 0;

    @JsonView(ResourceViews.Basic.class)
    private String message = "";

    @JsonView(ResourceViews.Basic.class)
    private String detail = "";

    @JsonView(ResourceViews.Basic.class)
    private T data;

    @Schema(description = "用户事件")
    @JsonView(ResourceViews.Basic.class)
    private List<String> userEvents;

    public List<String> getUserEvents() {
        return userEvents == null ? List.of() : userEvents;
    }

    public BaseResponseDto() {
    }

    public BaseResponseDto(T data) {
        this.data = data;
    }

    public BaseResponseDto(BaseException ex) {
        this.code = ex.getCode();
        this.internalCode = ex.getInternalCode();
        this.message = ex.getMessage();
        this.detail = ex.getDetail();
    }
}
