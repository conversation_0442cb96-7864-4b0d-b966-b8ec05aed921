package org.befun.core.dto.fillvalue;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

public interface FillValueList<O, E, V> {

    String getGroupPropertyName();

    Function<E, Long> getGroupPropertyId();

    BiConsumer<O, List<V>> setRelationValueList();

    static <O, E, V> FillValueList<O, E, V> create(String groupPropertyName, Function<E, Long> getGroupPropertyId, BiConsumer<O, List<V>> setRelationValueList) {

        return new FillValueList<>() {

            @Override
            public String getGroupPropertyName() {
                return groupPropertyName;
            }

            @Override
            public Function<E, Long> getGroupPropertyId() {
                return getGroupPropertyId;
            }

            @Override
            public BiConsumer<O, List<V>> setRelationValueList() {
                return setRelationValueList;
            }
        };
    }
}
