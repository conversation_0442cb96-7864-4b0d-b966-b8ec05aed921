package org.befun.core.dto.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseDTO;
import org.befun.core.exception.BadRequestException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
public class ResourceCustomQueryDto extends BaseDTO {

    @JsonProperty("_limit")
    @Schema(name = "_limit", description = "每页数量", example = "_limit=20")
    private int limit = 20;

    @JsonProperty("_page")
    @Schema(name = "_page", description = "页码", example = "_page=1")
    private int page = 1;

    @JsonProperty("_sort")
    @Schema(name = "_sort", description = "排序", example = "_sort=id_asc,createTime_desc")
    private String sort;

    public <T> ResourceEntityQueryDto<T> transform() {
        ResourceEntityQueryDto<T> entityQueryDto = new ResourceEntityQueryDto<>();
        entityQueryDto.setPage(page);
        entityQueryDto.setLimit(limit);
        parseSort(entityQueryDto);
        return entityQueryDto;
    }

    public PageRequest toPageRequest() {
        return PageRequest.of(page - 1, limit, parseSort());
    }

    /**
     * _sort=createTime_asc,updateTime_desc,id
     */
    private void parseSort(ResourceEntityQueryDto<?> queryDto) {
        queryDto.setSorts(parseSort());
    }

    private Sort parseSort() {
        if (StringUtils.isEmpty(sort)) {
            return Sort.unsorted();
        }
        String[] ss = sort.split(",");
        List<Sort.Order> orders = Arrays.stream(ss).map(s -> {
            String[] sss = s.split("_");
            List<String> supportSortProperties = supportSortProperties();
            if ((sss.length == 1 || sss.length == 2) && (supportSortProperties == null || supportSortProperties.contains(sss[0]))) {
                if (sss.length == 1) {
                    return new Sort.Order(Sort.Direction.ASC, sss[0]);
                } else if ("asc".equalsIgnoreCase(sss[1])) {
                    return new Sort.Order(Sort.Direction.ASC, sss[0]);
                } else if ("desc".equalsIgnoreCase(sss[1])) {
                    return new Sort.Order(Sort.Direction.DESC, sss[0]);
                }
            }
            throw new BadRequestException(String.format("invalid _sort parameter %s ", s));
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orders)) {
            return Sort.by(orders);
        }
        return Sort.unsorted();
    }

    public List<String> supportSortProperties() {
        return null;
    }

    public int getPage() {
        return page <= 0 ? 1 : page;
    }
}
