package org.befun.core.dto.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记Entity生成DTO
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface DtoClass {
    /**
     * Class name, if not given then annotated class name will be used
     * by appending Dto to it.
     * @return
     */
    String name() default "";

    /**
     * Package where DTO class need to be generated, if not given
     * then annotated class's package will be use by adding "dto"
     * subpackage under it.
     * @return
     */
    String classPackage() default "";

    /**
     * if true then all the fields will be used for DTO generation
     * @return
     */
    boolean includeAllFields() default false;

    /**
     * superinterface of DTO class should implement
     * @return
     */
    Class<?>[] interfaces() default {};

    /**
     * superClass of DTO class
     * @return
     */
    Class<?> superClass() default void.class;

    /**
     * 把生成的dto类型，设置为父类的泛型类型
     */
    boolean superClassParameterizedType() default false;
}
