package org.befun.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ResourceListResponseDto<T> extends BaseResponseDto {
    private static final long serialVersionUID = 6529685098267757690L;

    @JsonView(ResourceViews.Basic.class)
    List<T> items;

    public ResourceListResponseDto(List<T> items) {
        this.items = items;
    }

    public ResourceListResponseDto(Iterable<T> items) {
        this.items = Lists.newArrayList(items);
    }
}
