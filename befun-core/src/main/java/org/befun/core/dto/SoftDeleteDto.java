package org.befun.core.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.exception.BadRequestException;

@Getter
@Setter
public class SoftDeleteDto {

    private boolean enable;
    private String property;
    private Object deleteValue;

    public SoftDeleteDto() {
    }

    public SoftDeleteDto(String property, Object deleteValue) {
        this.enable = true;
        this.property = property;
        this.deleteValue = deleteValue;
    }

    public static SoftDeleteDto createDisable() {
        return new SoftDeleteDto();
    }

    public static SoftDeleteDto createByDefault() {
        return new SoftDeleteDto("deleted", true);
    }

    public static SoftDeleteDto createByAnnotation(SoftDelete softDelete) {
        SoftDeleteDto dto = new SoftDeleteDto();
        dto.setEnable(true);
        dto.setProperty(softDelete.property());
        if (softDelete.propertyType().isAssignableFrom(Boolean.class)) {
            dto.setDeleteValue(Boolean.valueOf(softDelete.deleteValue()));
        } else if (softDelete.propertyType().isAssignableFrom(Integer.class)) {
            dto.setDeleteValue(Integer.valueOf(softDelete.deleteValue()));
        } else {
            throw new BadRequestException("不支持的软删除属性类型");
        }
        return dto;
    }
}
