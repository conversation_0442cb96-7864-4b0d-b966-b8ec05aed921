package org.befun.core.dto;

import lombok.*;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.ResourcePermission;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourcePermissionDto extends BaseDTO {
    private Long id;
    private Long userId;
    private Long resourceId;
    private Long roleId;
    private Long departmentId;
    private String resourceType;
    private String relationType;

    public ResourcePermissionDto(ResourcePermission rp) {
        this.id = rp.getId();
        this.userId = rp.getUserId();
        this.resourceId = rp.getResourceId();
        this.roleId = rp.getRoleId();
        this.departmentId = rp.getDepartmentId();
        this.resourceType = rp.getResourceType();
        this.relationType = rp.getRelationType();
    }
}
