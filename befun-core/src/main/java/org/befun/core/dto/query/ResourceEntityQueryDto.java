package org.befun.core.dto.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseDTO;
import org.befun.core.validation.ValidSearchText;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Setter
@Getter
@Schema(description = "查询属性：[property[.subProperty]]_[eq|neq|gt|lt|gte|lte|in|contain|like|llike|rlike|isnull|notnull]=[value]")
public class ResourceEntityQueryDto<T> extends BaseDTO {

    @JsonProperty("_limit")
    @Schema(description = "每页数量", example = "_limit=20")
    private int limit = 20;

    /* 分页从1开始，JPA底层需要-1 */
    @JsonProperty("_page")
    @Schema(description = "页码", example = "_page=1")
    private int page = 1;

    @JsonProperty("_by")
    @Schema(description = "分组统计", example = "_by=status")
    private String by = null;

    @JsonProperty("_q")
    @ValidSearchText
    @Schema(description = "关键字查询", example = "_q=abc")
    private String q = null;

    @JsonProperty("_or")
    @Schema(description = "将查询条件组合为或", example = "_or=id_eq,id_lt,id_neq;id,id_in,id_notnull")
    private String or = null;

    @JsonProperty("_sort")
    @Schema(description = "排序", example = "_sort=id_asc,createTime_desc")
    private String sort;

    @Schema(description = "查询属性：[property[.subProperty]]_[eq|neq|gt|lt|gte|lte|in|contain|like|llike|rlike|isnull|notnull]=[value]",
            example = "id_in=1,2,3&id=1&id_isnull=1&id_notnull=1")
    private T supportProperty;

    /**
     * 内置展开
     */
    @JsonIgnore
    private List<ResourceQueryCriteria> queryCriteriaList = new ArrayList<>();

    @JsonIgnore
    private Sort sorts;

    @JsonIgnore
    private Set<String> joins = new HashSet<>();

    @JsonIgnore
    private int pageStartOffset;

    public Sort getSorts() {
        return sorts == null ? Sort.unsorted() : sorts;
    }

    public ResourceEntityQueryDto<T> addCriteria(ResourceQueryCriteria criteria) {
        if (criteria != null) {
            queryCriteriaList.add(criteria);
        }
        return this;
    }

    public void addAllCriteria(List<ResourceQueryCriteria> criterias) {
        queryCriteriaList.addAll(criterias);
    }

    public void addOr(List<ResourceQueryCriteria> criterias) {
        String or = criterias.stream().map(ResourceQueryCriteria::getParamKey).filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(or)) {
            if (StringUtils.isNotEmpty(this.or)) {
                this.or += ";" + or;
            } else {
                this.or = or;
            }
        }
    }
}
