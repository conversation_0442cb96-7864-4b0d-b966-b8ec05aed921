package org.befun.core.repository;

/**
 * The class description
 *
 * <AUTHOR>
 */

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.constant.EntityScopeStrategyTypes;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.context.TenantData;
import org.hibernate.Filter;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

import static org.befun.core.constant.EntityScopeStrategyType.NONE;
import static org.befun.core.constant.EntityScopeStrategyType.ORGANIZATION;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class GlobalFilter {

    @Autowired
    private EntityManager entityManager;

    @Pointcut("execution(public !void org.springframework.data.repository.Repository+.find*(..))")
    protected void repositoryFindFunction() {
    }

    @Pointcut("execution(public !void org.springframework.data.repository.Repository+.count*(..))")
    protected void repositoryCountFunction() {
    }

    @Around("repositoryFindFunction() || repositoryCountFunction()")
    public Object applyFilters(ProceedingJoinPoint joinPoint) throws Throwable {
        TenantData tenantData = TenantData.current();
        if (tenantData == null || tenantData.getOrgId() == null) {
            // 必须有tenantId以及指定具体权限策略
            return joinPoint.proceed();
        }

        Class<?> entityType = parseEntityType(joinPoint);

        // 必须指定具体权限策略
        EntityScopeStrategy strategy = entityType.getAnnotation(EntityScopeStrategy.class);
        if (strategy == null) {
            // 必须有tenantId以及指定具体权限策略
            return joinPoint.proceed();
        }
        String resourceType = Optional.ofNullable(strategy.resource()).filter(StringUtils::isNotEmpty).orElse(entityType.getSimpleName().toUpperCase());
        String groupResourceType = strategy.groupResource();

        Session session = entityManager.unwrap(Session.class);
        List<String> filterNames = new ArrayList<>();
        Object result;
        try {
            // 线程上下文中的策略，支持在线程上下文中重新指定策略（会忽略超管的特殊权限，只使用指定的filter）
            EntityScopeStrategyTypes strategyType = TenantContext.getCustomEntityScopeStrategy(entityType);
            if (strategyType != null) {
                // 修改了 EntityScopeStrategy 上面定义的策略，使用线程上下文中指定的策略
                if (strategyType == NONE) {
                    // 明确指定不需要使用策略
                } else {
                    // 上下文的策略中如果指定了resourceType则使用
                    // 未指定则使用默认的resourceType
                    String replaceResourceType = StringUtils.isNotEmpty(strategyType.getResourceType()) ? strategyType.getResourceType() : resourceType;
                    filterNames.add(applyScope(tenantData, session, strategyType.getFilter(), replaceResourceType, groupResourceType));
                }
            } else if (strategy.value().length > 0 || strategy.filterNames().length > 0) {
                // entity 上的策略
                EntityScopeStrategyType[] strategyTypes = strategy.value();
                String[] entityFilterNames = strategy.filterNames();
                Boolean isAdmin = TenantContext.getCurrentIsAdmin();
                boolean adminPrivilege = strategy.enableAdmin() && isAdmin != null && isAdmin;
                if (adminPrivilege && Arrays.stream(strategyTypes).noneMatch(i -> i == EntityScopeStrategyType.SHARE)) {
                    // 是超管，并且 EntityScopeStrategy 允许超管越权（默认行为），使用 ORGANIZATION 策略
                    filterNames.add(applyScope(tenantData, session, ORGANIZATION.getFilter(), resourceType, groupResourceType));
                } else {
                    // 使用 entity 上的策略
                    Arrays.stream(strategyTypes).forEach(i -> {
                        filterNames.add(applyScope(tenantData, session, i.getFilter(), resourceType, groupResourceType));
                    });
                    Arrays.stream(entityFilterNames).forEach(i -> {
                        filterNames.add(applyScope(tenantData, session, i, resourceType, groupResourceType));
                    });
                }
            }

            result = joinPoint.proceed();
        } finally {
            if (!filterNames.isEmpty()) {
                filterNames.forEach(session::disableFilter);
            }
        }
        return result;
    }

    private Class<?> parseEntityType(ProceedingJoinPoint joinPoint) {
        Type[] types = joinPoint.getThis().getClass().getGenericInterfaces();
        types = ((Class<?>) types[0]).getGenericInterfaces();
        return (Class<?>) ((ParameterizedType) types[0]).getActualTypeArguments()[0];
    }

    private String applyScope(TenantData params, Session session, String filterName, String resourceType, String groupResourceType) {
        Filter filter = session.enableFilter(filterName);
        Assert.notNull(filter, "invalid filter");
        setFilterParams(filter, params, resourceType, groupResourceType);
        return filterName;
    }

    private void setFilterParams(Filter filter, TenantData params, String resourceType, String groupResourceType) {
        Map<String, org.hibernate.type.Type> parameterTypes = filter.getFilterDefinition().getParameterTypes();
        if (MapUtils.isNotEmpty(parameterTypes)) {
            parameterTypes.forEach((k, v) -> {
                switch (k) {
                    case "orgId" -> filter.setParameter("orgId", params.getOrgId());
                    case "departmentIds" -> filter.setParameterList("departmentIds", params.getDepartmentIds());
                    case "subDepartmentIds" -> filter.setParameterList("subDepartmentIds", params.getSubDepartmentIds());
                    case "type" -> filter.setParameter("type", resourceType);
                    case "groupResource" -> filter.setParameter("groupResource", groupResourceType);
                    case "userId" -> filter.setParameter("userId", params.getUserId());
                    case "roleIds" -> filter.setParameterList("roleIds", params.getRoleIds());
                    default -> {
                        Object value = TenantContext.getExtFilterParams(k);
                        if (value == null) {
                            // hibernate filter params is null
                            throw new BadRequestException(String.format("hibernate filter param (%s) is null", k));
                        }
                        if (value instanceof Collection) {
                            filter.setParameterList(k, (Collection<?>) value);
                        } else {
                            filter.setParameter(k, value);
                        }
                    }
                }
            });
        }
    }


}