
package org.befun.core.repository.impl;

import lombok.SneakyThrows;
import org.apache.commons.beanutils.PropertyUtils;
import org.befun.core.dto.CountItemDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseAware;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.repository.BaseRepository;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.rest.query.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.LongSupplier;

/**
 * 基础Repository，可以在这里定制需要的基础方法
 *
 * @param <T>
 * @param <ID>
 */
public class BaseRepositoryImpl<T extends BaseEntity, ID>
        extends SimpleJpaRepository<T, ID> implements BaseRepository<T, ID> {

    private final EntityManager em;
    protected Class<T> entityClass;

    public BaseRepositoryImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.em = entityManager;
        this.entityClass = ((JpaMetamodelEntityInformation) entityInformation).getJavaType();
    }

    /**
     * 软删除，使用update去替换delete操作，delete会导致一些集联变化
     *
     * @param t
     */
    @SneakyThrows
    @Override
    @Transactional
    public void deleteSoft(T t) {
        PropertyUtils.setProperty(t, "deleted", true);
        this.em.merge(t);
    }

    /**
     * 简单版本的聚合接口，实现单一字段的count计算
     *
     * @param fieldName
     * @param specification
     * @return
     */
    @Override
    public List<CountItemDto> countBy(String fieldName, GenericSpecification specification) {
        List<Selection<?>> columns = new ArrayList<>();
        List<Expression<?>> groupBy = new ArrayList<>();
        CriteriaBuilder builder = em.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = builder.createTupleQuery();
        Root<T> from = query.from(entityClass);

        columns.add(from.get(fieldName));
        columns.add(builder.count(from.get(fieldName)));
        groupBy.add(from.get(fieldName));

        query = query.multiselect(columns);
        Predicate where = specification.toPredicate(from, query, builder);
        if (where != null) {
            query = query.where(where);
        }

        query = query.groupBy(groupBy);

        TypedQuery<Tuple> typedQuery = em.createQuery(query);
        List<Tuple> result = typedQuery.getResultList();
        List<CountItemDto> countItemDtos = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            Tuple tp = result.get(i);
            countItemDtos.add(new CountItemDto(tp.get(0).toString(), Long.valueOf(tp.get(1).toString())));
        }
        return countItemDtos;
    }

    @Override
    public List<T> findTreeAll() {
        // build root level entity...
        CriteriaBuilder builder = em.getCriteriaBuilder();
        CriteriaQuery<T> query = builder.createQuery(entityClass);
        Root<T> from = query.from(entityClass);
        TypedQuery<T> typedQuery = em.createQuery(query.where(builder.isNull(from.get("parent"))));
        List<T> result = typedQuery.getResultList();
        return result;
    }

    @Override
    public List<T> findPageList(GenericSpecification<T> specification, Pageable pageable) {
        TypedQuery<T> query = getQuery(specification, pageable);
        if (pageable.isPaged()) {
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());
        }
        return query.getResultList();
    }

    @Override
    public Optional<T> findById(ID id) {
        Optional<T> result = super.findById(id);
        if (result.isPresent() && EnterpriseEntity.class.isAssignableFrom(this.entityClass)) {
            final Long tenantId = TenantContext.getCurrentTenant();
            if (tenantId != null) {
                EnterpriseAware enterpriseAware = (EnterpriseAware) result.get();
                if (enterpriseAware.getOrgId() != null && !Objects.equals(enterpriseAware.getOrgId(), tenantId)) {
                    return Optional.empty();
                }
            }
        }
        return result;
    }

    @Override
    protected <S extends T> Page<S> readPage(TypedQuery<S> query, final Class<S> domainClass, Pageable pageable,
                                             @Nullable Specification<S> spec) {
        if (pageable.isPaged()) {
            int pageStartOffset = 0;
            if (spec instanceof GenericSpecification && (pageStartOffset = ((GenericSpecification<?>) spec).getPageStartOffset()) > 0) {
                pageStartOffset = ((GenericSpecification<?>) spec).getPageStartOffset();
            }
            query.setFirstResult((int) pageable.getOffset() + pageStartOffset);
            query.setMaxResults(pageable.getPageSize());
        }

        //return PageableExecutionUtils.getPage(query.getResultList(), pageable,
        //        () -> executeCountQuery(getCountQuery(spec, domainClass)));
        return new PageResult<>(query.getResultList(),pageable,executeCountQuery(getCountQuery(spec,domainClass)));
//        return new PageImpl<>(query.getResultList(), pageable,executeCountQuery(getCountQuery(spec, domainClass)));
    }

    private static long executeCountQuery(TypedQuery<Long> query) {

        Assert.notNull(query, "TypedQuery must not be null!");

        List<Long> totals = query.getResultList();
        long total = 0L;

        for (Long element : totals) {
            total += element == null ? 0 : element;
        }

        return total;
    }
}