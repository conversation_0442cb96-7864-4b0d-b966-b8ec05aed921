package org.befun.core.repository;

import org.befun.core.dto.CountItemDto;
import org.befun.core.rest.query.GenericSpecification;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.io.Serializable;
import java.util.*;

public interface BaseRepository<T extends Serializable, ID> extends JpaRepository<T, ID> {
    void deleteSoft(T t);
    List<CountItemDto> countBy(String fieldName, GenericSpecification specification);
    List<T> findTreeAll();
    List<T> findPageList(GenericSpecification<T> specification, Pageable pageable);
}