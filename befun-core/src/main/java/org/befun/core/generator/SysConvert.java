package org.befun.core.generator;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

/**
 * 转换数字为X进制，并且相邻的数字最终转换的字符串不会相似
 */
public final class SysConvert {

    /**
     * X进制的字符序列（61位,[a-zA-Z1-9]）
     * 修改之后会导致之前转码的数据无法解析
     */
    private static final String chars = "vXyEe6qaAGMpcSLzQs1nd7w3bPhNOFu5xBgKVDRijUmZTC4Ior8tlYk2W9JfH";
    private static final int length = chars.length();
    private static final String reverseFlag = "0";
    private static final long start = (long) length * length * length * length * length + 1; // 保证最小会有6位 61L*61*61*61*61 向上取整
    private static final int offset = 100;//偏移量

    public static void main(String[] args) {
        System.out.println(toX(312312312312312L));
        System.out.println(toDecimal("1WghRpRMy"));
    }

    /**
     * 转成 X进制
     */
    public static String toX(long decimal) {
        if (decimal <= 0) {
            return "0";
        }
        decimal += start; // 加上初始值
        boolean reverse = decimal % 10 > 0; // 如果最后一位是0，则在反转的时候会丢失数据，所以不反转
        if (reverse) {
            decimal = reverse(decimal); // 反转数字，使相邻的数字最终转换后不相似
        } else {
            decimal += offset; // 加上偏移量
        }
        StringBuilder s = new StringBuilder();
        long step = decimal;
        // 每次除以进制长度然后取整，再取模
        // 把每次取模定位的字符合并起来，
        while (step > 0) {
            int i = (int) (step % length);
            s.append(chars.charAt(i));
            step = step / length;
        }
        if (reverse) {// 有反转，这里记录在字符串中
//            int p = RandomUtils.nextInt(0, s.length());
//            s.insert(p, reverseFlag);
            s.append(reverseFlag);
        }
        return s.toString();
    }

    /**
     * 转成 十进制
     */
    public static Long toDecimal(String xx) {
        if (xx == null || "0".equals(xx)) {
            return 0L;
        }
        int countReverseFlag = StringUtils.countMatches(xx, reverseFlag);
        if (countReverseFlag > 1) { // 反转标记 最多只能有1个
            return 0L;
        }
        boolean reverse = countReverseFlag == 1;//判断是否包含反转标记
        if (reverse) {
            xx = xx.replace(reverseFlag, "");// 替换掉反转标记
        }
        int l = xx.length();
        long decimal = 0;
        long step = 1;
        for (int i = 0; i < l; i++) {
            char c = xx.charAt(i);
            int indexOf = chars.indexOf(c);
            if (indexOf == -1) {
                return 0L;
            }
            decimal += step * indexOf;
            step = step * length;
        }
        if (reverse) {
            decimal = reverse(decimal);
        } else {
            decimal -= offset;
        }
        decimal -= start;// 减去初始值
        return decimal < 0 ? 0 : decimal;
    }

    private static long reverse(long lo) {
        String s = String.valueOf(lo);
        int l = s.length();
        char[] cc = new char[l];
        for (int i = 0; i < l; i++) {
            cc[i] = s.charAt(l - 1 - i);
        }
        s = new String(cc);
        return Long.parseLong(s);
    }
}
