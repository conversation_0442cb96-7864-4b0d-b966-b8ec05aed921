package org.befun.core.generator;

import org.befun.core.entity.BaseEntity;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import java.io.Serializable;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class SnowflakeGenerator implements IdentifierGenerator {
    private final Snowflake generator = new Snowflake();

    @Override
    public Serializable generate(SharedSessionContractImplementor sharedSessionContractImplementor, Object o) throws HibernateException {
        if (o instanceof BaseEntity) {
            BaseEntity entity = (BaseEntity) o;
            if (entity.getId() != null && entity.getId() > 0) {
                return entity.getId();
            }
        }
        return generator.nextId();
    }

}
