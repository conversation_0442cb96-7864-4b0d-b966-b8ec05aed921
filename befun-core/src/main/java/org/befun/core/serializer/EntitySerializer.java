package org.befun.core.serializer;

import org.befun.core.entity.BaseEntity;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.persistence.OneToOne;
import javax.persistence.OneToMany;
import javax.persistence.ManyToOne;
import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Field;

//@JsonComponent
public class EntitySerializer extends JsonSerializer<BaseEntity> {

    @Override
    public void serialize(BaseEntity baseEntity, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        if (ra instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes)ra).getRequest();
            System.out.println(request.getHeaderNames());
        }
        jsonGenerator.writeStartObject();
        for (Field field : baseEntity.getClass().getDeclaredFields()) {
            if ( field.getAnnotation(OneToOne.class) != null
                    || field.getAnnotation(ManyToOne.class) != null
                    || field.getAnnotation(OneToMany.class) != null) {
                continue;
            }
            try {
                Object value = new PropertyDescriptor((String)field.getName(), baseEntity.getClass()).getReadMethod().invoke(baseEntity);
                jsonGenerator.writeObjectField(field.getName(), value);
            } catch (Exception ex) {
            }
        }
        jsonGenerator.writeObjectField("created", baseEntity.createTime);
        jsonGenerator.writeObjectField("updated", baseEntity.modifyTime);
        jsonGenerator.writeEndObject();
    }
}