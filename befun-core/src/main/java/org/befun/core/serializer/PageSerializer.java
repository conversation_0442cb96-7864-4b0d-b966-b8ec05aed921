package org.befun.core.serializer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.jackson.JsonComponent;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@JsonComponent
public class PageSerializer extends JsonSerializer<Page> {

    @Override
    public void serialize(Page page, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        jsonGenerator.writeStartObject();

        Map<String, Object> meta = new HashMap<>();
        meta.put("total", page.getTotalElements());
        meta.put("page", page.getTotalPages());
        meta.put("limit", page.getSize());

        jsonGenerator.writeObjectField("code", 0);
        jsonGenerator.writeObjectField("message", "");
        jsonGenerator.writeObjectField("data", page.getContent());
        jsonGenerator.writeObjectField("meta", meta);
        jsonGenerator.writeEndObject();
    }
}