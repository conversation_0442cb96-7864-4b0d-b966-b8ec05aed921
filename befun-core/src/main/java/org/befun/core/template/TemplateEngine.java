package org.befun.core.template;

import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringSubstitutor;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * The Template Engine
 * help render text and json by dynamic placeholder with json path (city.name) support
 * - template format ${name} for example: hello ${city.name}
 *
 * <AUTHOR>
 */
@Slf4j
public class TemplateEngine {

    private static Pattern PARAM_PATTERN = Pattern.compile("\\$\\{(.+?)\\}");
    private static Pattern PARAM_PATTERN2 = Pattern.compile("[#|$]\\{(.+?)}");

    public static String renderTextTemplate(String template, Map<String, Object> params) {
        return renderTextTemplate(template, PARAM_PATTERN, "${", params);
    }

    public static String renderTextTemplate2(String template, Map<String, Object> params) {
        return renderTextTemplate(template, PARAM_PATTERN2, "#{", params);
    }

    /**
     * renderTextTemplate
     * 基于正则表达式查找pattern，一一使用JsonPath寻找
     *
     * @param template hello ${city.name}
     * @param params   {
     *                 "city": {
     *                 "id": 1,
     *                 "name": "shenzhen"
     *                 }
     *                 }
     * @return String
     * for example: hello shenzhen
     */
    private static String renderTextTemplate(String template, Pattern pattern, String prefix, Map<String, Object> params) {
        if (template == null || template.isEmpty()) {
            return "";
        }

        if (params == null || params.isEmpty()) {
            return template;
        }

        if (!template.contains(prefix)) {
            // 如果不包含，快速退出
            return template;
        }

        int lastIndex = 0;
        StringBuilder output = new StringBuilder();
        DocumentContext context = JsonPath.parse(params);
        Matcher matcher = pattern.matcher(template);
        while (matcher.find()) {
            Object res = null;
            String regex = matcher.group(1);
            if (params.containsKey(regex)) {
                res = params.get(regex);
            } else {
                try {
                    String path = Arrays.stream(regex.split("\\."))
                            .map(s -> String.format("['%s']", s))
                            .collect(Collectors.joining("."));
                    res = context.read(path);
                } catch (PathNotFoundException ex) {
                    log.warn("Failed to resolve path {} use default val", matcher.group(1));
                }
            }
            String val = res != null ? res.toString() : "";
            output.append(template, lastIndex, matcher.start()).append(val);
            lastIndex = matcher.end();
        }
        if (lastIndex < template.length()) {
            output.append(template, lastIndex, template.length());
        }
        return output.toString();
    }

    /**
     * renderTextTemplateSimple, 和renderTextTemplate的区别是只处理一层，并不支持嵌套查询替换
     * 基于StringSubstitutor实现
     *
     * @param template hello ${name}
     * @param params   {
     *                 "name": "shenzhen"
     *                 }
     * @return String
     * for example: hello shenzhen
     */
    static public String renderTextTemplateSimple(String template, Map<String, Object> params) {
        StringSubstitutor sub = new StringSubstitutor(params);
        return sub.replace(template);
    }

    /**
     * renderJsonTemplate
     *
     * @param template {
     *                 "keyword1": "${city}.name"
     *                 }
     * @param params   {
     *                 "city": {
     *                 "id": 1,
     *                 "name": "shenzhen"
     *                 }
     *                 }
     * @return Map<String, String>
     * {
     * "keyword1": "shenzhen"
     * }
     * @return
     */
    static public Map<String, Object> renderJsonTemplate(Map<String, Object> template, Map<String, Object> params) {
        if (template == null || template.isEmpty()) {
            return params;
        }
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : template.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            if (entry.getValue().toString().contains("${")) {
                result.put(entry.getKey(), renderTextTemplate(entry.getValue().toString(), params));
            } else {
                result.put(entry.getKey(), entry.getValue().toString());
            }
        }
        return result;
    }
}
