package org.befun.core.collection;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.SequenceAware;
import org.befun.core.entity.TreeAware;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeSet;

/**
 * 多树节点的森林结构
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TreeNode<T extends BaseEntityDTO> implements Comparable<TreeNode<T>> {
    @JsonView(ResourceViews.Basic.class)
    private T node;

    @JsonView(ResourceViews.Basic.class)
    private TreeSet<TreeNode> children = new TreeSet<>();

    public TreeNode(T node) {
        Assert.isTrue(node instanceof TreeAware);
        this.node = node;
    }

    public Boolean isRoot() {
        TreeAware treeAware = (TreeAware) node;
        return treeAware.getParentId() == null;
    }

    /**
     * check is a children of parent
     * @param parent
     * @return
     */
    public boolean isChildren(TreeNode parent) {
        TreeAware treeAware = (TreeAware) node;
        TreeAware parentAware = (TreeAware) parent.node;

        return treeAware.getParentId().equals(parentAware.getId());
    }

    public void addChildren(TreeNode node) {
        this.children.add(node);
    }

    public Long getParentId() {
        TreeAware treeAware = (TreeAware) node;
        return treeAware.getParentId();
    }

    public Long getId() {
        TreeAware treeAware = (TreeAware) node;
        return treeAware.getId();
    }

    @Override
    public int compareTo(TreeNode<T> o) {
        return this.node.compareTo(o.getNode());
    }
}
