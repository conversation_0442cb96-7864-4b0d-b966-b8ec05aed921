package org.befun.core.collection;

import com.fasterxml.jackson.annotation.JsonView;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 多树节点的森林结构
 *
 * <AUTHOR>
 */
public class Forest<T extends BaseEntityDTO> {
    @JsonView(ResourceViews.Basic.class)
    private TreeSet<TreeNode> nodes = new TreeSet<>();

    class ForestContextDto {
        private Set<TreeNode> current = new HashSet<>();
        private Set<TreeNode>  leftItems = new HashSet<>();
    }

    /**
     *
     * @return
     */
    public Set<TreeNode> getNodes() {
       return this.nodes;
    }

    /**
     * 通过扁平化的列表，迭代的方式回造森林结构
     * @param items
     */
    public Forest(List<T> items) {
        Set<TreeNode> treeNodes = items.stream()
                .map(x -> new TreeNode(x))
                .collect(Collectors.toSet());

        Set<Long> ids = treeNodes.stream().map(x -> x.getId())
                .collect(Collectors.toSet());

        // 两种情况会被放到root
        // - parentId == null
        // - parentId 无效
        Set<TreeNode>  others = new HashSet<>();
        for (TreeNode node : treeNodes) {
            if (node.getParentId() == null || !ids.contains(node.getParentId())) {
                nodes.add(node);
            } else {
                others.add(node);
            }
        }

        Set<TreeNode> current = nodes;
        while (others.size() > 0) {
            ForestContextDto contextDto = buildChildrenIterator(current, others);
            current = contextDto.current;
            others = contextDto.leftItems;
        }
    }

    /**
     * 迭代器
     * @param nodes
     * @param leftItems
     * @return
     */
    private ForestContextDto buildChildrenIterator(Set<TreeNode> nodes, Set<TreeNode> leftItems) {
        ForestContextDto contextDto = new ForestContextDto();
        if (leftItems.size() == 0) {
            return contextDto;
        }
        for (TreeNode item : leftItems) {
            boolean found = false;
            for (TreeNode parent : nodes) {
                if (item.isChildren(parent)) {
                    parent.addChildren(item);
                    contextDto.current.add(item);
                    found = true;
                    break;
                }
            }
            if (!found) {
                contextDto.leftItems.add(item);
            }
        }
        return contextDto;
    }
}
