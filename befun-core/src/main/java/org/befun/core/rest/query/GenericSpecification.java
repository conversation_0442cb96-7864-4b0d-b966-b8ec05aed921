package org.befun.core.rest.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Getter
@Setter
@NoArgsConstructor
public class GenericSpecification<T> implements Specification<T> {
    private final Map<Class<?>, List<Field>> cacheQueryFieldMaps = new HashMap<>();

    /* 查询条件 */
    private ResourceEntityQueryDto<?> queryDto;
    private List<ResourceQueryCriteria> criterias = new ArrayList<>();
    private String q;
    private String or;
    private int pageStartOffset;

    public GenericSpecification(ResourceEntityQueryDto<?> queryDto) {
        this.q = queryDto.getQ();
        this.or = queryDto.getOr();
        this.queryDto = queryDto;
        this.pageStartOffset = queryDto.getPageStartOffset();
        if (CollectionUtils.isNotEmpty(queryDto.getQueryCriteriaList())) {
            this.criterias.addAll(queryDto.getQueryCriteriaList());
        }
    }

    public void add(ResourceQueryCriteria criteria) {
        criterias.add(criteria);
    }

    @Override
    @SuppressWarnings("NullableProblems")
    public Predicate toPredicate(Root<T> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
        try {
            List<ResourceQueryCriteria> criterias = new ArrayList<>(this.criterias);
            List<Predicate> where = new ArrayList<>();
            Map<String, Path<?>> pathMap = new HashMap<>();
            pathMap.put("root", root);
            if (StringUtils.isNotEmpty(this.q)) {
                List<Field> queryFields = getQueryFields(root.getJavaType());
                if (queryFields.size() == 0) {
                    throw new BadRequestException("invalid q search");
                }
                List<Predicate> conditions = new ArrayList<>();
                for (Field field : queryFields) {
                    conditions.add(criteriaBuilder.like(root.get(field.getName()), "%" + this.q + "%"));
                }

                where.add(criteriaBuilder.or(conditions.toArray(new Predicate[0])));
            }
            if (StringUtils.isNotEmpty(this.or)) {
                // _or=id_eq,code_eq,name_like;id_neq,code_isnull,name_isnull
                Map<String, ResourceQueryCriteria> criteriaMap = criterias.stream().collect(Collectors.toMap(ResourceQueryCriteria::getParamKey, Function.identity(), (o1, o2) -> o1));
                Arrays.stream(this.or.split(";")).forEach(i -> {
                    List<ResourceQueryCriteria> orCriteria = Arrays.stream(i.split(",")).map(criteriaMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                    if (orCriteria.size() > 1) {
                        criterias.removeAll(orCriteria);
                        where.add(criteriaBuilder.or(orCriteria.stream().map(k -> toPredicate(k, pathMap, criteriaBuilder)).toArray(value -> new Predicate[orCriteria.size()])));
                    }
                });
            }
            for (ResourceQueryCriteria criteria : criterias) {
                where.add(toPredicate(criteria, pathMap, criteriaBuilder));
            }
            if (where.size() > 0) {
                return criteriaBuilder.and(where.toArray(new Predicate[0]));
            }
            return null;
        } catch (Exception ex) {
            log.warn("invalid query parameter: {}", ex.getMessage());
            ex.printStackTrace();
            throw new BadRequestException("查询参数错误", ex.getMessage());
        }
    }

    private Predicate toPredicate(ResourceQueryCriteria criteria, Map<String, Path<?>> pathMap, CriteriaBuilder cb) {
        Root<?> root = (Root<?>) pathMap.get("root");
        if (StringUtils.isNotEmpty(criteria.getJoin())) {
            Path<?> path = pathMap.get(criteria.getJoin());
            if (path == null) {
                path = root.join(criteria.getJoin());
                pathMap.put(criteria.getJoin(), path);
            }
            return criteria.toPredicate(path, cb);
        } else {
            return criteria.toPredicate(root, cb);
        }
    }

    private List<Field> getQueryFields(Class<?> clazz) {
        List<Field> fieldList = cacheQueryFieldMaps.get(clazz);
        if (fieldList == null) {
            fieldList = new ArrayList<>();
            List<Field> allFields = parseAllFields(clazz, new ArrayList<>());
            for (Field field : allFields) {
                DtoProperty dtoProperty = field.getAnnotation(DtoProperty.class);
                if (dtoProperty != null && dtoProperty.queryable()) {
                    fieldList.add(field);
                }
            }
            cacheQueryFieldMaps.put(clazz, fieldList);
        }
        return fieldList;
    }

    private List<Field> parseAllFields(Class<?> clazz, List<Field> allFields) {
        if (!clazz.equals(Object.class)) {
            Field[] fields = clazz.getDeclaredFields();
            allFields.addAll(Arrays.asList(fields));
            parseAllFields(clazz.getSuperclass(), allFields);
        }
        return allFields;
    }
}