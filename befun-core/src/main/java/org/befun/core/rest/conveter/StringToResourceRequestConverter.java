package org.befun.core.rest.conveter;

import org.befun.core.dto.query.ResourceQueryRequestDto;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class StringToResourceRequestConverter implements Converter<String, ResourceQueryRequestDto> {
    @Override
    public ResourceQueryRequestDto convert(String source) {
        return null;
    }
}
