package org.befun.core.rest.annotation.processor;

public final class ResourceMethodName {
    public static final String FIND_ALL = "findAll";
    public static final String FIND_ONE = "findOne";
    public static final String UPDATE_ONE = "updateOne";
    public static final String DELETE_ONE = "deleteOne";
    public static final String CREATE = "create";
    public static final String COUNT = "count";
    public static final String BATCH_UPDATE = "batchUpdate";

}
