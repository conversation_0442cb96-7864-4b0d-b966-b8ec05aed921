package org.befun.core.rest.annotation;

import org.springframework.web.bind.annotation.RequestMethod;

import java.lang.annotation.*;

/**
 * Annotate for a Resource
 * <AUTHOR>
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface ResourceCollectionAction {
    String action() default "";
    String path() default "";
    String description() default "";
    RequestMethod method() default RequestMethod.GET;
    String permission() default "";
}
