package org.befun.core.rest.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ResourceFieldCloneRule {
    String value() default "";
    boolean ignore() default false;
}