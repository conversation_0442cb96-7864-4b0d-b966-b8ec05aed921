package org.befun.core.rest.annotation;

import org.befun.core.rest.annotation.processor.ResourceMethod;

import java.lang.annotation.*;

/**
 * Annotate for a Resource
 * <AUTHOR>
 */
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Repeatable(MultipleResourceEmbeddedMany.class)
public @interface ResourceEmbeddedMany {
    String path();
    /**
     * 替换 mappedBy
     * 上一级的实体中的对应的集合字段名，会通过此字段查找到注解中的mappedBy属性
     */
    String fieldNameInRoot();
    Class<?> entityClass();
    Class<?> repositoryClass();
    Class<?> dtoClass() default void.class;
    ResourceMethodDto[] methodDtoClass() default {};
    ResourcePermission[] permissions() default {};
    ResourceMethod[] excludeActions() default {};
    String docTag() default "";
    String docCrud() default "";
    ResourceDeepEmbeddedMany[] deepEmbeddedMany() default {};
}
