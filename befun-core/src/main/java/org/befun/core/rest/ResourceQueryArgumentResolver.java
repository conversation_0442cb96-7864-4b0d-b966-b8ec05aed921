package org.befun.core.rest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.ParameterType;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.utils.ParamsHelper;
import org.springframework.core.MethodParameter;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class ResourceQueryArgumentResolver implements HandlerMethodArgumentResolver {
    private final Map<Class<?>, Map<String, Field>> cacheFieldMaps = new HashMap<>();

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(ResourceQueryPredicate.class) != null
                && parameter.getParameterType() == ResourceEntityQueryDto.class;
    }

    /**
     * 使用ObjectMapper 转换对应query的value
     *
     * @param parameter
     * @param modelAndViewContainer
     * @param webRequest
     * @param webDataBinderFactory
     * @return
     * @throws Exception
     */
    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest webRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        ResourceEntityQueryDto<?> queryDto = new ResourceEntityQueryDto<>();
        Class<?> dtoClass = (Class<?>) ((ParameterizedType) parameter.getGenericParameterType()).getActualTypeArguments()[0];
        Map<String, Field> fieldsMap = getFieldMap(dtoClass);
        webRequest.getParameterMap().forEach((key, values) -> {
            if (values.length > 1) {
                // TBD
                log.warn("unexpected query values {}", values.length);
                throw new BadRequestException("invalid query unexpected values length");
            }
            String value = values[0];
            if (StringUtils.isNotEmpty(value)) {
                if (ParameterType.OPERATORS_LOOKUP.containsKey(key)) {
                    parseDefault(fieldsMap, queryDto, key, value);
                } else {
                    parseProperty(fieldsMap, queryDto, key, value);
                }
            }
        });

        return queryDto;
    }

    public Map<String, Field> getFieldMap(Class<?> clazz) {
        Map<String, Field> fieldMap = cacheFieldMaps.get(clazz);
        if (fieldMap == null) {
            fieldMap = new HashMap<>();
            List<Field> allFields = parseAllFields(clazz, new ArrayList<>());
            for (Field field : allFields) {
                if (BaseEntityDTO.class.isAssignableFrom(field.getType()) || BaseEntity.class.isAssignableFrom(field.getType())) {
                    List<Field> allFields1 = parseAllFields(field.getType(), new ArrayList<>());
                    for (Field subField : allFields1) {
                        fieldMap.put(field.getName() + "." + subField.getName(), subField);
                    }
                    fieldMap.put(field.getName(), field);
                } else if (Collection.class.isAssignableFrom(field.getType())) {
                    Type type = ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0];
                    Class<?> c;
                    if (type instanceof Class && BaseEntityDTO.class.isAssignableFrom(c = (Class<?>) type)) {
                        List<Field> allFields2 = parseAllFields(c, new ArrayList<>());
                        for (Field subField : allFields2) {
                            fieldMap.put(field.getName() + "." + subField.getName(), subField);
                        }
                    }
                } else {
                    fieldMap.put(field.getName(), field);
                }
            }
            cacheFieldMaps.put(clazz, fieldMap);
        }
        return fieldMap;
    }

    private List<Field> parseAllFields(Class<?> clazz, List<Field> allFields) {
        if (!clazz.equals(Object.class)) {
            Field[] fields = clazz.getDeclaredFields();
            allFields.addAll(Arrays.asList(fields));
            parseAllFields(clazz.getSuperclass(), allFields);
        }
        return allFields;
    }

    private void parseDefault(Map<String, Field> fieldsMap, ResourceEntityQueryDto<?> queryDto, String key, String value) {
        ParameterType type = ParameterType.OPERATORS_LOOKUP.get(key);
        switch (type) {
            case LIMIT:
                queryDto.setLimit(checkGtZero(Integer.parseInt(value), ParameterType.LIMIT.getLabel()));
                break;
            case PAGE:
                queryDto.setPage(checkGtZero(Integer.parseInt(value), ParameterType.PAGE.getLabel()));
                break;
            case Q:
                queryDto.setQ(value);
                break;
            case BY:
                parseBy(fieldsMap, queryDto, value);
                break;
            case OR:
                queryDto.setOr(value);
                break;
            case SORT:
                parseSort(fieldsMap, queryDto, value);
                break;
        }
    }

    private int checkGtZero(Integer v, String param) {
        if (v == null || v <= 0) {
            throw new BadRequestException("参数" + param + "必须大于0");
        }
        return v;
    }

    /**
     * _by=status
     */
    private void parseBy(Map<String, Field> fieldsMap, ResourceEntityQueryDto<?> queryDto, String value) {
        if (fieldsMap.containsKey(value)) {
            queryDto.setBy(value);
            return;
        }
        throw new BadRequestException(String.format("invalid _by parameter %s ", value));
    }

    /**
     * _sort=createTime_asc,updateTime_desc,id
     */
    private void parseSort(Map<String, Field> fieldsMap, ResourceEntityQueryDto<?> queryDto, String value) {
        String[] ss = value.split(",");
        List<Sort.Order> orders = Arrays.stream(ss).map(s -> {
            String[] sss = s.split("_");
            if ((sss.length == 1 || sss.length == 2) && fieldsMap.containsKey(sss[0])) {
                if (sss.length == 1) {
                    return new Sort.Order(Sort.Direction.ASC, sss[0]);
                } else if ("asc".equalsIgnoreCase(sss[1])) {
                    return new Sort.Order(Sort.Direction.ASC, sss[0]);
                } else if ("desc".equalsIgnoreCase(sss[1])) {
                    return new Sort.Order(Sort.Direction.DESC, sss[0]);
                }
            }
            throw new BadRequestException(String.format("invalid _sort parameter %s ", s));
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orders)) {
            queryDto.setSorts(Sort.by(orders));
        }
    }

    private void parseProperty(Map<String, Field> fieldsMap, ResourceEntityQueryDto<?> queryDto, String key, String value) {
        String[] keys = key.split("_");
        if (keys.length > 2) {
            throw new BadRequestException("invalid query field %s", key);
        }
        if (!fieldsMap.containsKey(keys[0])) {
            throw new BadRequestException(String.format("invalid query field %s not exist ", key));
        }
        try {
            ResourceQueryCriteria criteria = ParamsHelper.parseCriteria(key, value, fieldsMap.get(keys[0]));
            if (criteria != null) {
                queryDto.getQueryCriteriaList().add(criteria);
                if (StringUtils.isNotEmpty(criteria.getJoin())) {
                    queryDto.getJoins().add(criteria.getJoin());
                }
            }
        } catch (IllegalArgumentException e) {
            throw new BadRequestException(String.format("invalid query %s error: %s", key, e.getMessage()));
        }
    }
}
