package org.befun.core.rest;

import lombok.SneakyThrows;
import org.befun.core.dto.*;
import org.befun.core.dto.query.ResourceQueryRequestDto;
import org.befun.core.dto.query.SearchCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.ParamsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.collection.internal.PersistentSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public abstract class DeepEmbeddedController<T extends BaseEntity, M extends BaseEntity, E extends BaseEntity> {
//    protected final ResourceRepository<T, Long> repository;
//    protected final ResourceRepository<M, Long> middleRepository;
//    protected final ResourceRepository<E, Long> embeddedRepository;
//    protected Class<T> entityClass;
//    protected Class<M> middleEntityClass;
//    protected Class<E> embeddedEntityClass;
//
//
//    protected String getMappedBy() {
//        throw new NotImplementedException("should override getMappedBy");
//    }
//    protected String getFieldName() {
//        throw new NotImplementedException("should override getMappedBy");
//    }
//
//    @SuppressWarnings("unused")
//    protected DeepEmbeddedController(
//            ResourceRepository<T, Long> repository,
//            ResourceRepository<M, Long> middleRepository,
//            ResourceRepository<E, Long> embeddedRepository) {
//        this.repository = repository;
//        this.middleRepository = middleRepository;
//        this.embeddedRepository = embeddedRepository;
//    }
//
//    @PostConstruct
//    public void init() {
//        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
//                .getGenericSuperclass()).getActualTypeArguments()[0];
//        this.middleEntityClass = (Class<M>) ((ParameterizedType) getClass()
//                .getGenericSuperclass()).getActualTypeArguments()[1];
//        this.embeddedEntityClass = (Class<E>) ((ParameterizedType) getClass()
//                .getGenericSuperclass()).getActualTypeArguments()[2];
//    }
//
//    private T ensureLeft(long id) {
//        Optional<T> instance = repository.findById(id);
//        if (!instance.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        return instance.get();
//    }
//
//    private M ensureMiddle(long id) {
//        Optional<M> instance = middleRepository.findById(id);
//        if (!instance.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        return instance.get();
//    }
//
//    /**
//     *
//     * @return
//     */
//    @SneakyThrows
//    private Collection<E> ensureEmbeddedList(M holder) {
//        String fieldName = getFieldName();
//        return (Collection<E>) PropertyUtils.getProperty(holder, fieldName);
//    }
//
//    private GenericSpecification<E> buildScopeSpecification(long mid) {
//        GenericSpecification<E> specification = new GenericSpecification<>();
//        String mappedBy = getMappedBy();
//        specification.add(new SearchCriteria(mappedBy, mid));
//        return specification;
//    }
//
//    private GenericSpecification<E> buildEmbeddedSpecification(long mid, long eid) {
//        GenericSpecification<E> specification = buildScopeSpecification(mid);
//        specification.add(new SearchCriteria("id", eid));
//        return specification;
//    }
//
//    @SuppressWarnings("unused")
//    public ResourcePageResponseDto<E> findAll(long id, long mid, Map<String, Object> params) {
//        ResourceQueryRequestDto requestDto = ParamsHelper.parseQueryParams(params, embeddedEntityClass);
//        PageRequest p = PageRequest.of(requestDto.getPage(), requestDto.getLimit());
//        GenericSpecification<E> specification = buildScopeSpecification(mid);
//        specification.addList(requestDto.getCriteriaList());
//        return new ResourcePageResponseDto<>(embeddedRepository.findAll(specification, p));
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<CountDto> count(long id, long mid, Map<String, Object> params) {
//        ResourceQueryRequestDto requestDto = ParamsHelper.parseQueryParams(params, embeddedEntityClass);
//        GenericSpecification<E> specification = buildScopeSpecification(mid);
//        specification.addList(requestDto.getCriteriaList());
//        return new ResourceResponseDto<>(new CountDto(embeddedRepository.count(specification)));
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<E> findOne(long id, long mid, long eid) {
//        GenericSpecification<E> specification = buildEmbeddedSpecification(mid, eid);
//        Optional<E> object = embeddedRepository.findOne(specification);
//        if (!object.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        return new ResourceResponseDto<>(object.get());
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<E> updateOne(long id, long mid, long eid, Map<String, Object> data) {
//        T left = ensureLeft(id);
//        GenericSpecification<E> specification = buildEmbeddedSpecification(mid, eid);
//        Optional<E> object = embeddedRepository.findOne(specification);
//        if (!object.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//
//        EntityUtility.mergeEntityWithData(object.get(), data);
//        E result = embeddedRepository.save(object.get());
//        repository.save(left);
//        return new ResourceResponseDto<>(result);
//    }
//
//    @SuppressWarnings("unused")
//    public BaseResponseDto<String> batchUpdate(long id, long mid, ResourceBatchUpdateRequestDto batchUpdateDto) {
//        for (ResourceUpdateItemRequestDto change : batchUpdateDto.getChanges()) {
//            T left = ensureLeft(id);
//            Long eid = change.getId();
//            GenericSpecification<E> specification = buildEmbeddedSpecification(mid, eid);
//            Optional<E> object = embeddedRepository.findOne(specification);
//            if (!object.isPresent()) {
//                throw new EntityNotFoundException();
//            }
//            EntityUtility.mergeEntityWithData(object.get(), change.getData());
//            log.debug("batchUpdate for embedded item {}", object.get().getId());
//            embeddedRepository.save(object.get());
//            repository.save(left);
//        }
//        return new BaseResponseDto<>();
//    }
//
//    @SuppressWarnings("unused")
//    public BaseResponseDto<String> deleteOne(long id, long mid, long eid) {
//        M middle = ensureMiddle(mid);
//        Collection<E> embeddedList = ensureEmbeddedList(middle);
//        boolean removed = embeddedList.removeIf( embed -> embed.getId().equals(eid));
//        if (removed) {
//            middleRepository.save(middle);
//        } else {
//            throw new EntityNotFoundException();
//        }
//        return new BaseResponseDto<>();
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<E> create(long id, long mid, E data) {
//        T left = ensureLeft(id);
//        M middle = ensureMiddle(mid);
//        try {
//            PropertyUtils.setProperty(data, getMappedBy(), middle);
//            EntityUtility.fillUpReference(data);
//            E saved = embeddedRepository.save(data);
//            repository.save(left);
//            Optional<E> newObject = Optional.ofNullable(embeddedRepository.save(data));
//            return new ResourceResponseDto<>(newObject.get());
//        } catch (Exception ex) {
//            throw new BadRequestException(ex.getMessage());
//        }
//    }
}
