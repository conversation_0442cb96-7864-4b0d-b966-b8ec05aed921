package org.befun.core.rest.context;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TenantData {
    private Long orgId;
    private Long userId;
    private List<Long> departmentIds;
    private List<Long> subDepartmentIds;
    private List<Long> roleIds;

    public static TenantData currentOrCopied() {
        TenantData tenantData = current();
        if (tenantData != null) {
            return tenantData;
        }
        return TenantContext.copiedTenantData.get();
    }

    public static TenantData current() {
        Long orgId = TenantContext.getCurrentTenant();
        if (orgId == null) {
            return null;
        }
        Long userId = Optional.ofNullable(TenantContext.getCurrentUserId()).orElse(0L);
        List<Long> departmentIds = confirmNotEmpty(TenantContext.getCurrentDepartmentIds());
        List<Long> subDepartmentIds = confirmNotEmpty(TenantContext.getCurrentSubDepartmentIds());
        List<Long> roleIds = confirmNotEmpty(TenantContext.getCurrentRoleIds());
        return new TenantData(orgId, userId, departmentIds, subDepartmentIds, roleIds);
    }

    private static List<Long> confirmNotEmpty(List<Long> list) {
        List<Long> r = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(i -> {
                if (i != null) {
                    r.add(i);
                }
            });
        }
        if (r.isEmpty()) {
            r.add(0L);
        }
        return r;
    }
}