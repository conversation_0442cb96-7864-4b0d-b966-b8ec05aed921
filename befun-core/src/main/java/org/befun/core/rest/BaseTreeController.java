package org.befun.core.rest;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.*;
import org.befun.core.dto.query.ResourceQueryRequestDto;
import org.befun.core.entity.EnterpriseTreeEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.ParamsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.function.BiFunction;

@Getter
@Setter
@Slf4j
public abstract class BaseTreeController<T extends EnterpriseTreeEntity> {

    @Autowired
    private EntityManager entityManager;

    protected ResourceRepository<T, Long> repository;
    protected Class<T> entityClass;

    @Inject
    private RequestMappingHandlerMapping handlerMapping;

    @SuppressWarnings("unused")
    protected BaseTreeController(ResourceRepository<T, Long> repository) {
        this.repository = repository;
    }

    @SuppressWarnings("unused")
    private BaseTreeController() {
        this.repository = null;
    }

    @PostConstruct
    public void init() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
    }

//    public ResourcePageResponseDto<T> findAll(Map<String, Object> params) {
////        ResourceQueryRequestDto requestDto = ParamsHelper.parseQueryParams(params, entityClass);
////        PageRequest pageRequest = PageRequest.of(requestDto.getPage(), requestDto.getLimit(), requestDto.getSort());
////        GenericSpecification<T> specification = new GenericSpecification<>(requestDto);
////        specification.addList(requestDto.getCriteriaList());
//        return new ResourcePageResponseDto<T>(repository.findAll(specification, pageRequest));
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceListResponseDto<T> findTreeAll() {
//        List<T> items = repository.findTreeAll();
//        return new ResourceListResponseDto<T>(items);
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<T> findOne(long id) {
//        Optional<T> object = repository.findById(id);
//        if (!object.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        return new ResourceResponseDto<>(object.get());
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<T> updateOne(long id, Map<String, Object> data) {
//        Optional<T> object = repository.findById(id);
//        if (!object.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        BiFunction<Long, Class<?>, Object> getSubEntity = (subId, clazz) -> {
//            if (subId != null && subId > 0) {
//                return entityManager.find(clazz, subId);
//            }
//            return null;
//        };
//        EntityUtility.mergeEntityWithData(object.get(), data, getSubEntity);
//        T result = repository.save(object.get());
//        return new ResourceResponseDto<>(result);
//    }
//
//    @SuppressWarnings("unused")
//    @Transactional
//    public ResourceResponseDto<Boolean> move(ResourceTreeMoveRequestDto requestDto) {
//        long sourceId = requestDto.getSourceId();
//        long destinationId = requestDto.getDestinationId();
//        Optional<T> object = repository.findById(sourceId);
//        Optional<T> newParent = repository.findById(destinationId);
//        if (!object.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        if (!newParent.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//
//        if (newParent.get().isFolder() == false) {
//            throw new BadRequestException("destination is not folder");
//        }
//
//        T parent = (T) object.get().getParent();
//        if (parent != null && parent.getId() == destinationId) {
//            throw new BadRequestException("destination could not be same as current parent");
//        }
//
//        if (parent != null) {
//            parent.getChildren().remove(object.get());
//            repository.save(parent);
//        }
//
//        newParent.get().getChildren().add(object.get());
//        repository.save(newParent.get());
//
//        object.get().setParent(newParent.get());
//        repository.save(object.get());
//
//        return new ResourceResponseDto<>(true);
//    }
//
//    @SuppressWarnings("unused")
//    @Transactional
//    public BaseResponseDto<String> deleteOne(long id) {
//        Optional<T> object = repository.findById(id);
//        if (!object.isPresent()) {
//            throw new EntityNotFoundException();
//        }
//        repository.delete(object.get());
//        return new BaseResponseDto<>();
//    }
//
//    @SuppressWarnings("unused")
//    public ResourceResponseDto<T> create(T data) {
//        EntityUtility.fillUpReference(data);
//        Optional<T> newObject = Optional.ofNullable(repository.save(data));
//        return new ResourceResponseDto<>(newObject.get());
//    }
}
