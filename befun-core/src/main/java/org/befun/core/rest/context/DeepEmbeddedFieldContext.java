package org.befun.core.rest.context;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;
import static org.befun.core.rest.context.FieldRelationType.ONE_TO_ONE;

/**
 * <pre class="code">
 * class FirstEntity {
 *      List<SecondEntity> seconds;
 * }
 * class SecondEntity {
 *      FirstEntity first;
 *      List<ThirdEntity> thirds;
 * }
 * class ThirdEntity {
 *      SecondEntity second;
 * }
 * </pre>
 */
public class DeepEmbeddedFieldContext {
    /**
     * List<ThirdEntity> thirds
     */
    public Field deepFieldInEmbedded;
    /**
     * SecondEntity second;
     */
    public Field embeddedFieldInDeep;
    /**
     * second
     */
    public String embeddedFieldNameInDeep;
    public FieldRelationType relationType;
    public EmbeddedFieldContext parent;

    public DeepEmbeddedFieldContext(Class<?> deepClass, String embeddedFieldNameInDeep, EmbeddedFieldContext parent) {
        this.deepFieldInEmbedded = null;
        this.embeddedFieldNameInDeep = embeddedFieldNameInDeep;
        try {
            this.embeddedFieldInDeep = deepClass.getDeclaredField(embeddedFieldNameInDeep);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
        this.relationType = ONE_TO_MANY;
        this.parent = parent;
    }

    public DeepEmbeddedFieldContext(Field deepFieldInEmbedded, Field embeddedFieldInDeep, FieldRelationType relationType, String embeddedFieldNameInDeep, EmbeddedFieldContext parent) {
        this.deepFieldInEmbedded = deepFieldInEmbedded;
        this.embeddedFieldInDeep = embeddedFieldInDeep;
        this.relationType = relationType;
        this.embeddedFieldNameInDeep = embeddedFieldNameInDeep;
        this.parent = parent;
    }

    public DeepEmbeddedFieldContext(Field deepFieldInEmbedded, FieldRelationType relationType, String embeddedFieldNameInDeep, EmbeddedFieldContext parent) {
        this.deepFieldInEmbedded = deepFieldInEmbedded;
        this.relationType = relationType;
        this.embeddedFieldNameInDeep = embeddedFieldNameInDeep;
        this.parent = parent;
        if (deepFieldInEmbedded != null) {
            try {
                if (relationType == ONE_TO_ONE) {
                    embeddedFieldInDeep = deepFieldInEmbedded.getType().getDeclaredField(embeddedFieldNameInDeep);
                } else {
                    Type type = deepFieldInEmbedded.getGenericType();
                    if (type instanceof ParameterizedType) {
                        Type[] argTypes = ((ParameterizedType) type).getActualTypeArguments();
                        if (argTypes.length == 1) {
                            embeddedFieldInDeep = ((Class<?>) argTypes[0]).getDeclaredField(embeddedFieldNameInDeep);
                        }
                    }
                }
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
    }
}
