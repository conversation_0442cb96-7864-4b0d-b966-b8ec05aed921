package org.befun.core.rest;

import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.*;
import org.befun.core.dto.query.ResourceQueryRequestDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.ParamsHelper;
import org.hibernate.annotations.SQLDelete;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.lang.reflect.ParameterizedType;
import java.util.*;

@Getter
@Setter
@Slf4j
public abstract class BaseSingleController<T extends BaseEntity> {

    @Autowired
    private EntityManager entityManager;

    protected ResourceRepository<T, Long> repository;
    protected Class<T> entityClass;
    private boolean supportSoftDelete = false;

    @Inject
    private RequestMappingHandlerMapping handlerMapping;

    @SuppressWarnings("unused")
    protected BaseSingleController(ResourceRepository<T, Long> repository) {
        this.repository = repository;
    }

    @SuppressWarnings("unused")
    private BaseSingleController() {
        this.repository = null;
    }

    @PostConstruct
    public void init() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];

        if (!EnterpriseOwnerEntity.class.isAssignableFrom(this.entityClass)) {
            throw new IllegalArgumentException("single type should be enterprise owner entity");
        }

        SQLDelete[] deleteAnnotation = this.entityClass.getAnnotationsByType(SQLDelete.class);
        if (deleteAnnotation != null && deleteAnnotation.length > 0) {
            this.supportSoftDelete = true;
        }
    }

    @SuppressWarnings("unused")
    public ResourceResponseDto<T> findOne() {
        Page<T> page = repository.findAll(PageRequest.of(0, 1));
        if (!page.hasContent()) {
            return new ResourceResponseDto<>();
        }
        return new ResourceResponseDto<>(page.getContent().get(0));
    }

    @SuppressWarnings("unused")
    public ResourceResponseDto<T> updateOne(Map<String, Object> data) {
        Page<T> page = repository.findAll(PageRequest.of(0, 1));

        if (!page.hasContent()) {
            throw new EntityNotFoundException();
        }

        T object = page.getContent().get(0);
        EntityUtility.mergeEntityWithData(object, data);
        T result = repository.save(object);
        return new ResourceResponseDto<>(result);
    }

    @SuppressWarnings("unused")
    @Transactional
    public BaseResponseDto<String> deleteOne() {
        Page<T> page = repository.findAll(PageRequest.of(0, 1));
        if (!page.hasContent()) {
            throw new EntityNotFoundException();
        }
        T object = page.getContent().get(0);
        if (this.supportSoftDelete) {
            repository.deleteSoft(object);
        } else {
            repository.delete(object);
        }
        return new BaseResponseDto<>();
    }

    @SuppressWarnings("unused")
    public ResourceResponseDto<T> create(T data) {
        Page<T> page = repository.findAll(PageRequest.of(0, 1));
        if (page.hasContent()) {
            throw new BadRequestException("can't create multiple object for single type");
        }
        EntityUtility.fillUpReference(data);
        Optional<T> newObject = Optional.ofNullable(repository.save(data));
        return new ResourceResponseDto<>(newObject.get());
    }
}
