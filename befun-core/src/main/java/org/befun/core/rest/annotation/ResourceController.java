package org.befun.core.rest.annotation;

import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import java.lang.annotation.*;

/**
 * Annotate for a Resource
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.SOURCE)
@Inherited
public @interface ResourceController {
    boolean cacheable() default false;
    String name() default "";
    Class<?> entityClass();
    /**
     * 这个属性无效
     * ignore 直接解析service中的dto
     */
    @Deprecated
    Class<?> dtoClass() default void.class;
    /**
     * 指定方法的自定义入参dto,默认使用service的第二个泛型参数
     */
    ResourceMethodDto[] methodDtoClass() default {};
    /**
     * 指定实现的service
     */
    Class<?> serviceClass();
    Class<?> repositoryClass();
    /**
     * 指定数据类型：有分页的列表，无分页的列表，单条数据，树结构
     */
    ResourceCollectionType collectionType() default ResourceCollectionType.COLLECTION;
    /**
     * 方法的权限
     */
    ResourcePermission[] permissions() default {};
    /**
     * 生成的类上的权限
     */
    String permission() default "";
    /**
     * 指定不生成的方法
     */
    ResourceMethod[] excludeActions() default {};

    String docTag() default "";
    String docCrud() default "";
}
