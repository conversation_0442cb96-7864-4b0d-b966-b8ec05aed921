package org.befun.core.rest.annotation;

import org.befun.core.rest.annotation.processor.ResourceMethod;

import java.lang.annotation.*;

/**
 * Annotate for a Resource
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.SOURCE)
@Inherited
public @interface ResourceMethodDto {
    /**
     * support methods
     * {@link ResourceMethod#FIND_ALL}      dto must extends {@link org.befun.core.dto.query.ResourceCustomQueryDto}
     * {@link ResourceMethod#CREATE}        dto must extends {@link org.befun.core.dto.BaseEntityDTO}
     * {@link ResourceMethod#UPDATE_ONE}    dto must extends {@link org.befun.core.dto.BaseEntityDTO}
     * {@link ResourceMethod#BATCH_UPDATE}  dto must extends {@link org.befun.core.dto.BaseEntityDTO}
     */
    ResourceMethod method();
    Class<?> dtoClass();
    boolean valid() default false;
}
