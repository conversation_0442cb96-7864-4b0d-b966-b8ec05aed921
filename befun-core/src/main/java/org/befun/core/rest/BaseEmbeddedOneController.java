package org.befun.core.rest;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.befun.core.dto.*;
import org.befun.core.dto.query.SearchCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.EntityUtility;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

@Slf4j
public abstract class BaseEmbeddedOneController<T extends BaseEntity, E extends BaseEntity> {
    protected final ResourceRepository<T, Long> repository;
    protected final ResourceRepository<E, Long> embeddedRepository;
    protected Class<T> entityClass;
    protected Class<E> embeddedEntityClass;

    protected String getMappedBy() {
        throw new NotImplementedException("should override getMappedBy");
    }

    protected String getFieldName() {
        throw new NotImplementedException("should override getMappedBy");
    }

    @SuppressWarnings("unused")
    protected BaseEmbeddedOneController(ResourceRepository<T, Long> repository, ResourceRepository<E, Long> embeddedRepository) {
        this.repository = repository;
        this.embeddedRepository = embeddedRepository;
    }

    @PostConstruct
    public void init() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
        this.embeddedEntityClass = (Class<E>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[1];
    }

    /**
     * ensure the embedded holder (parent)
     *
     * @param id
     * @return
     */
    private T ensureHold(Long id) {
        Optional<T> holder = repository.findById(id);
        if (!holder.isPresent()) {
            throw new EntityNotFoundException();
        }
        return holder.get();
    }

    /**
     * @return
     */
    @SneakyThrows
    private Collection<E> ensureEmbeddedList(T holder) {
        String fieldName = getFieldName();
        return (Collection<E>) PropertyUtils.getProperty(holder, fieldName);
    }

    private GenericSpecification<E> buildScopeSpecification(long id) {
        // TBD
        GenericSpecification<E> specification = new GenericSpecification<>();
        String mappedBy = getMappedBy();
//        specification.add(new SearchCriteria(mappedBy, id));
        return specification;
    }

    private GenericSpecification<E> buildEmbeddedSpecification(long id, long eid) {
        // TBD
        GenericSpecification<E> specification = buildScopeSpecification(id);
//        specification.add(new SearchCriteria("id", eid));
        return specification;
    }

    @SuppressWarnings("unused")
    public ResourceResponseDto<E> findOne(long id, long eid) {
        GenericSpecification<E> specification = buildEmbeddedSpecification(id, eid);
        Optional<E> object = embeddedRepository.findOne(specification);
        if (!object.isPresent()) {
            throw new EntityNotFoundException();
        }
        return new ResourceResponseDto<>(object.get());
    }

    @SuppressWarnings("unused")
    public ResourceResponseDto<E> updateOne(long id, long eid, Map<String, Object> data) {
        T holder = ensureHold(id);
        GenericSpecification<E> specification = buildEmbeddedSpecification(id, eid);
        Optional<E> object = embeddedRepository.findOne(specification);
        if (!object.isPresent()) {
            throw new EntityNotFoundException();
        }
        EntityUtility.mergeEntityWithData(object.get(), data);
        E result = embeddedRepository.save(object.get());
        repository.save(holder);
        return new ResourceResponseDto<>(object.get());
    }

    @SuppressWarnings("unused")
    public BaseResponseDto<String> deleteOne(long id, long eid) {
        T holder = ensureHold(id);
        Collection<E> embeddedList = ensureEmbeddedList(holder);
        boolean removed = embeddedList.removeIf(embed -> embed.getId().equals(eid));
        if (removed) {
            repository.save(holder);
        } else {
            throw new EntityNotFoundException();
        }
        return new BaseResponseDto<>();
    }

    @SuppressWarnings("unused")
    public ResourceResponseDto<E> create(long id, E data) {
        T holder = ensureHold(id);
        try {
            PropertyUtils.setProperty(data, getMappedBy(), holder);
            EntityUtility.fillUpReference(data);
            E saved = embeddedRepository.save(data);
            Optional<E> newObject = Optional.ofNullable(embeddedRepository.save(data));
            repository.save(holder);
            return new ResourceResponseDto<>(newObject.get());
        } catch (Exception ex) {
            throw new BadRequestException(ex.getMessage());
        }
    }
}
