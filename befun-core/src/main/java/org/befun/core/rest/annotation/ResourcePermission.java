package org.befun.core.rest.annotation;

import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.annotation.processor.ResourceMethod;

import java.lang.annotation.*;

/**
 * Annotate for a Resource
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface ResourcePermission {
    String action() default "";

    String permission() default "";

    /**
     * PermissionPath.*
     */
    String[] requirePermissions() default {};

    /**
     * 需要是超级管理员
     */
    boolean needSuperAdmin() default false;

    /**
     * 权限匹配规则
     */
    RequirePermissions.MatchType matchType() default RequirePermissions.MatchType.ALL_MATCH;
}
