package org.befun.core.rest.query;

import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;

public class PageResult<T> extends PageImpl<T> {

    private final long total;

    public PageResult(List<T> content, Pageable pageable, long total) {
        super(content, pageable, total);
        this.total = total;
    }

    public PageResult(List<T> content) {
        this(content, Pageable.unpaged(), null == content ? 0 : content.size());
    }

    @Override
    public int getTotalPages() {
        return getSize() == 0 ? 1 : (int) Math.ceil((double) total / (double) getSize());
    }

    @Override
    public long getTotalElements() {
        return total;
    }

}
