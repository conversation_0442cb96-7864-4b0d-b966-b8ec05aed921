package org.befun.core.rest.annotation.processor;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
public enum ResourceMethod {
    FIND_ALL(ResourceMethodName.FIND_ALL, "全部%s"),
    FIND_ONE(ResourceMethodName.FIND_ONE, "单个%s"),
    UPDATE_ONE(ResourceMethodName.UPDATE_ONE, "修改%s"),
    DELETE_ONE(ResourceMethodName.DELETE_ONE, "删除%s"),
    CREATE(ResourceMethodName.CREATE, "新增%s"),
    COUNT(ResourceMethodName.COUNT, "统计%s"),
    BATCH_UPDATE(ResourceMethodName.BATCH_UPDATE, "批量修改%s"),
    ;
    private final String name;
    private final String doc;

    ResourceMethod(String name, String doc) {
        this.name = name;
        this.doc = doc;
    }

    public String formatDoc(String docCrud) {
        if (StringUtils.isEmpty(docCrud)) {
            return null;
        }
        return String.format(doc, docCrud);
    }

    public static ResourceMethod parseByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(values()).filter(i -> i.name.equals(name)).findFirst().orElse(null);
    }
}
