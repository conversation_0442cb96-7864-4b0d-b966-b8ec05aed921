package org.befun.core.rest;

import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.security.UserEvent;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.List;


@ControllerAdvice
@SuppressWarnings("NullableProblems")
@ConditionalOnProperty(name = UserEvent.CONFIG_KEY, havingValue = "true")
public class ResponseBodyAdviceHandler implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        List<String> userEvents;
        if (body instanceof BaseResponseDto && CollectionUtils.isNotEmpty(userEvents = TenantContext.getCurrentUserEvents())) {
            ((BaseResponseDto<?>) body).setUserEvents(userEvents);
        }
        return body;
    }
}
