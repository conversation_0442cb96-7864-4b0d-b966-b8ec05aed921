package org.befun.core.rest.context;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;
import static org.befun.core.rest.context.FieldRelationType.ONE_TO_ONE;

/**
 * <pre class="code">
 * class FirstEntity {
 *      List<SecondEntity> seconds;
 * }
 * class SecondEntity {
 *      FirstEntity first;
 *      List<ThirdEntity> thirds;
 * }
 * class ThirdEntity {
 *      SecondEntity second;
 * }
 * </pre>
 */
public class EmbeddedFieldContext {
    /**
     * List<SecondEntity> seconds;
     */
    public Field embeddedFieldInRoot;
    /**
     * FirstEntity first;
     */
    public Field rootFieldInEmbedded;
    /**
     * first
     */
    public String rootFieldNameInEmbedded;
    public FieldRelationType relationType;
    public final Map<String, DeepEmbeddedFieldContext> deepRelationMaps = new HashMap<>();

    public EmbeddedFieldContext(Class<?> embeddedClass, String rootFieldNameInEmbedded) {
        this.embeddedFieldInRoot = null;
        this.rootFieldNameInEmbedded = rootFieldNameInEmbedded;
        try {
            this.rootFieldInEmbedded = embeddedClass.getDeclaredField(rootFieldNameInEmbedded);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
        this.relationType = ONE_TO_MANY;
    }

    public EmbeddedFieldContext(Field embeddedFieldInRoot, Field rootFieldInEmbedded, String rootFieldNameInEmbedded, FieldRelationType relationType) {
        this.embeddedFieldInRoot = embeddedFieldInRoot;
        this.rootFieldInEmbedded = rootFieldInEmbedded;
        this.rootFieldNameInEmbedded = rootFieldNameInEmbedded;
        this.relationType = relationType;
    }

    public EmbeddedFieldContext(Field embeddedFieldInRoot, String rootFieldNameInEmbedded, FieldRelationType relationType) {
        this.embeddedFieldInRoot = embeddedFieldInRoot;
        this.rootFieldNameInEmbedded = rootFieldNameInEmbedded;
        this.relationType = relationType;
        if (embeddedFieldInRoot != null) {
            try {
                if (relationType == ONE_TO_ONE) {
                    rootFieldInEmbedded = embeddedFieldInRoot.getType().getDeclaredField(rootFieldNameInEmbedded);
                } else {
                    Type type = embeddedFieldInRoot.getGenericType();
                    if (type instanceof ParameterizedType) {
                        Type[] argTypes = ((ParameterizedType) type).getActualTypeArguments();
                        if (argTypes.length == 1) {
                            rootFieldInEmbedded = ((Class<?>) argTypes[0]).getDeclaredField(rootFieldNameInEmbedded);
                        }
                    }
                }
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
    }
}
