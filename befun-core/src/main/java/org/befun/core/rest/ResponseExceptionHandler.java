package org.befun.core.rest;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.constant.ErrorCode;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.exception.*;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class ResponseExceptionHandler {

    @Value(value = "${befun.server.enable-constraint-violation-path:false}")
    private Boolean enableConstraintViolationPath;

    @Value(value = "${befun.server.mutiple-constraint-violation:false}")
    private Boolean multipleConstraintViolation;

    @ExceptionHandler({UserEventException.class})
    public ResponseEntity<BaseResponseDto<?>> handleUserEventException(UserEventException ex) {
        log.warn("handleUserEventException", ex);
        BaseResponseDto<?> responseDto = new BaseResponseDto<>();
        responseDto.setCode(ex.getCode());
        responseDto.setMessage(ex.getMessage());
        responseDto.setUserEvents(List.of(ex.getEvent()));
        return ResponseEntity.status(ex.getHttpStatus()).body(responseDto);
    }

    @ExceptionHandler({BusinessException.class})
    public ResponseEntity<BaseResponseDto<?>> handleOperatorException(BusinessException ex) {
        log.warn("handleOperatorException:{}", ex.getMessage());
        BaseResponseDto<Object> responseDto = new BaseResponseDto<>(ex);
        responseDto.setCode(ex.getCode());
        responseDto.setInternalCode(ex.getInternalCode());
        responseDto.setData(ex.getData());
        return ResponseEntity.ok().body(responseDto);
    }

    @ExceptionHandler({BaseException.class, EntityNotFoundException.class,
            BadRequestException.class})
    public ResponseEntity<BaseResponseDto<?>> handleManagedException(
            BaseException ex, WebRequest request) {
        if (ex.isPrintStack()) {
            log.error("handleManagedException", ex);
        } else {
            log.error("handleManagedException: {}", ex.getMessage());
        }
        BaseResponseDto<?> responseDto = new BaseResponseDto<>(ex);
        return ResponseEntity.badRequest().body(responseDto);
    }

    // 授权失败  403
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<BaseResponseDto<?>> handleAccessDeniedException(AccessDeniedException ex,
                                                                          WebRequest request) {
        BaseResponseDto<?> responseDto = new BaseResponseDto<>();
        responseDto.setMessage(ex.getMessage());
        if (request.getUserPrincipal() == null) {
            // 没有认证信息 401
            responseDto.setCode(HttpStatus.UNAUTHORIZED.value());
            responseDto.setMessage("请重新登录");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(responseDto);
        }
        responseDto.setCode(HttpStatus.FORBIDDEN.value());
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(responseDto);
    }

    // 认证失败 401
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<BaseResponseDto<?>> handleAuthenticationException(
            AuthenticationException ex, WebRequest request) {
        BaseResponseDto<?> responseDto = new BaseResponseDto<>();
        responseDto.setCode(HttpStatus.UNAUTHORIZED.value());
        responseDto.setMessage(ex.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(responseDto);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<BaseResponseDto<?>> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException e) {
        String message;
        if (!multipleConstraintViolation) {
            message = e.getBindingResult().getFieldErrors().stream()
                    .map(i -> enableConstraintViolationPath ?
                            String.format("%s:%s", getFieldSchemaDesc(e.getParameter(), i.getField()), i.getDefaultMessage()) : i.getDefaultMessage())
                    .findFirst()
                    .orElse(null);
        } else {
            message = e.getBindingResult().getFieldErrors().stream()
                    .map(i -> enableConstraintViolationPath ?
                            String.format("%s:%s", getFieldSchemaDesc(e.getParameter(), i.getField()), i.getDefaultMessage()) : i.getDefaultMessage())
                    .collect(Collectors.joining(","));
        }
        BaseResponseDto<?> responseDto = new BaseResponseDto<>(
                new BaseException(ErrorCode.BAD_PARAMETER, message));
        return ResponseEntity.badRequest().body(responseDto);
    }

    /**
     * <p>
     * <table>
     * <caption>Supported Exceptions</caption>
     * <thead>
     * <tr>
     * <th class="colFirst">Exception</th>
     * <th class="colLast">HTTP Status Code</th>
     * </tr>
     * </thead>
     * <tbody>
     * <tr class="altColor">
     * <td><p>HttpRequestMethodNotSupportedException</p></td>
     * <td><p>405 (SC_METHOD_NOT_ALLOWED)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>HttpMediaTypeNotSupportedException</p></td>
     * <td><p>415 (SC_UNSUPPORTED_MEDIA_TYPE)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>HttpMediaTypeNotAcceptableException</p></td>
     * <td><p>406 (SC_NOT_ACCEPTABLE)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>MissingPathVariableException</p></td>
     * <td><p>500 (SC_INTERNAL_SERVER_ERROR)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>MissingServletRequestParameterException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>ServletRequestBindingException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>ConversionNotSupportedException</p></td>
     * <td><p>500 (SC_INTERNAL_SERVER_ERROR)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>TypeMismatchException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>HttpMessageNotReadableException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>HttpMessageNotWritableException</p></td>
     * <td><p>500 (SC_INTERNAL_SERVER_ERROR)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>MethodArgumentNotValidException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>MissingServletRequestPartException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>BindException</p></td>
     * <td><p>400 (SC_BAD_REQUEST)</p></td>
     * </tr>
     * <tr class="rowColor">
     * <td><p>NoHandlerFoundException</p></td>
     * <td><p>404 (SC_NOT_FOUND)</p></td>
     * </tr>
     * <tr class="altColor">
     * <td><p>AsyncRequestTimeoutException</p></td>
     * <td><p>503 (SC_SERVICE_UNAVAILABLE)</p></td>
     * </tr>
     * </tbody>
     * </table>
     *
     * @see org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver
     */
    @ExceptionHandler(Throwable.class)
    public ResponseEntity<BaseResponseDto<?>> exception(Throwable ex) {
        log.error("exception", ex);
        HttpStatus status = null;
        String message = null;
        try {
            if (ex instanceof HttpRequestMethodNotSupportedException) {
                status = HttpStatus.METHOD_NOT_ALLOWED;
            } else if (ex instanceof HttpMediaTypeNotSupportedException) {
                status = HttpStatus.UNSUPPORTED_MEDIA_TYPE;
            } else if (ex instanceof HttpMediaTypeNotAcceptableException) {
                status = HttpStatus.NOT_ACCEPTABLE;
            } else if (ex instanceof MissingPathVariableException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof MissingServletRequestParameterException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof ServletRequestBindingException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof ConversionNotSupportedException) {
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            } else if (ex instanceof TypeMismatchException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof HttpMessageNotReadableException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof HttpMessageNotWritableException) {
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            } else if (ex instanceof MethodArgumentNotValidException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof MissingServletRequestPartException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof BindException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof RequestRejectedException) {
                status = HttpStatus.BAD_REQUEST;
            } else if (ex instanceof NoHandlerFoundException) {
                status = HttpStatus.NOT_FOUND;
            } else if (ex instanceof AsyncRequestTimeoutException) {
                status = HttpStatus.SERVICE_UNAVAILABLE;
            }
        } catch (Exception handlerEx) {
            log.error("exception：{}", handlerEx.getMessage());
        }
        if (status == null) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        } else {
            message = status.name();
        }
        if (message == null) {
            message = "UNKNOWN";
        }
        BaseResponseDto<?> responseDto = new BaseResponseDto<>(new BaseException(status.value(), message));
        return ResponseEntity.status(status).body(responseDto);
    }


    private String getFieldSchemaDesc(MethodParameter methodParameter, String fieldNames) {
        Class<?> clazz = methodParameter.getParameterType();
        Iterator<String> iterator = Arrays.stream(fieldNames.split("\\.")).iterator();
        String desc = fieldNames;
        if (iterator.hasNext()) {
            do {
                String fieldName = iterator.next();
                Field field;
                try {
                    field = clazz.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    break;
                }
                if (iterator.hasNext()) {
                    clazz = field.getType();
                } else {
                    Schema schema = field.getAnnotation(Schema.class);
                    if (schema != null) {
                        desc = schema.description();
                    }
                    break;
                }
            } while (true);
        }
        return desc;
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<BaseResponseDto<?>> handleConstraintViolationException(
            ConstraintViolationException e) {
        String message = Optional.ofNullable(e.getConstraintViolations())
                .map(l -> {
                    if (!multipleConstraintViolation) {
                        return l.stream().filter(Objects::nonNull)
                                .findFirst()
                                .map(i -> enableConstraintViolationPath ? String
                                        .format("%s:%s", getPathShortName(i.getPropertyPath()),
                                                i.getMessage()) : i.getMessage())
                                .orElse(null);
                    } else {
                        return l.stream()
                                .filter(Objects::nonNull)
                                .map(i -> enableConstraintViolationPath ? String
                                        .format("%s:%s", getPathShortName(i.getPropertyPath()),
                                                i.getMessage()) : i.getMessage())
                                .collect(Collectors.joining(","));
                    }
                })
                .orElse(null);
        BaseResponseDto<?> responseDto = new BaseResponseDto<>(
                new BaseException(ErrorCode.BAD_PARAMETER, message));
        return ResponseEntity.badRequest().body(responseDto);
    }

    @ExceptionHandler(BindException.class)
    public ResponseEntity<BaseResponseDto<?>> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(i -> String.format("%s:%s", i.getField(), i.getDefaultMessage()))
                .collect(Collectors.joining(","));
        BaseResponseDto<?> responseDto = new BaseResponseDto<>(
                new BaseException(ErrorCode.BAD_PARAMETER, message));
        return ResponseEntity.badRequest().body(responseDto);
    }

    private String getPathShortName(Path path) {
        if (path == null) {
            return null;
        }
        Iterator<Path.Node> iterator = path.iterator();
        String name = null;
        while (iterator.hasNext()) {
            name = iterator.next().getName();
        }
        return name;
    }
}
