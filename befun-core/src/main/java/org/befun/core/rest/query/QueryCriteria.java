package org.befun.core.rest.query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.query.SearchCriteria;
import org.befun.core.exception.BadRequestException;

import javax.persistence.criteria.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class QueryCriteria {

    private final Map<String, Path<?>> pathMap;
    private final CriteriaBuilder cb;
    private final List<SearchCriteria> criteriaList;
    private final Predicate.BooleanOperator operator;

    public QueryCriteria(Map<String, Path<?>> pathMap, CriteriaBuilder cb, List<SearchCriteria> criteriaList, Predicate.BooleanOperator operator) {
        this.pathMap = pathMap;
        this.cb = cb;
        this.criteriaList = criteriaList;
        this.operator = operator;
    }

    public Predicate predicate() {
        Predicate[] predicates = predicate(pathMap, cb, criteriaList);
        if (predicates != null && predicates.length > 0) {
            switch (operator) {
                case OR -> {
                    return cb.or(predicates);
                }
                case AND -> {
                    return cb.and(predicates);
                }
            }
        }
        return null;
    }

    private Predicate[] predicate(Map<String, Path<?>> pathMap, CriteriaBuilder cb, List<SearchCriteria> sc) {
        return Optional.ofNullable(sc)
                .map(l -> l.stream()
                        .map(i -> create(pathMap, cb, i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .map(list -> {
                    if (CollectionUtils.isNotEmpty(list)) {
                        return list.toArray(new Predicate[0]);
                    } else {
                        return null;
                    }
                }).orElse(null);
    }

    private Predicate create(Map<String, Path<?>> pathMap, CriteriaBuilder cb, SearchCriteria sc) {
        if (sc.getValue() != null) {
            Path<?> path = getPath(pathMap, sc);
            return sc.getOperator().predicate(path, cb, sc.getValue());
        }
        return null;
    }

    private Path<?> getPath(Map<String, Path<?>> pathMap, SearchCriteria sc) {
        if (StringUtils.isNotEmpty(sc.getThirdlyKey())) {
            if (StringUtils.isNotEmpty(sc.getSubKey())) {
                return getSecondJoin(sc, pathMap).get(sc.getThirdlyKey());
            } else {
                throw new BadRequestException();
            }
        } else if (StringUtils.isNotEmpty(sc.getSubKey())) {
            return getFirstJoin(sc, pathMap).get(sc.getSubKey());
        }
        return getRootPath(pathMap).get(sc.getKey());
    }

    private Path<?> getSecondJoin(SearchCriteria sc, Map<String, Path<?>> pathMap) {
        String mapKey = String.format("%s-%s", sc.getKey(), sc.getSubKey());
        Path<?> path = pathMap.get(mapKey);
        if (path == null) {
            path = ((Join<?, ?>) getFirstJoin(sc, pathMap)).join(sc.getSubKey(), sc.getSecondJoin() == null ? JoinType.INNER : sc.getSecondJoin());
            pathMap.put(mapKey, path);
        }
        return path;
    }

    private Path<?> getFirstJoin(SearchCriteria sc, Map<String, Path<?>> pathMap) {
        String mapKey = String.format("%s", sc.getKey());
        Path<?> path = pathMap.get(mapKey);
        if (path == null) {
            path = getRootPath(pathMap).join(sc.getKey(), sc.getFirstJoin() == null ? JoinType.INNER : sc.getFirstJoin());
            pathMap.put(mapKey, path);
        }
        return path;
    }

    private Root<?> getRootPath(Map<String, Path<?>> pathMap) {
        return (Root<?>) pathMap.get("root");
    }

}
