package org.befun.core;

import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

///**
// * The class description
// *
// * <AUTHOR>
// */
//@SpringBootApplication
//public class Application {
//
//    @Configuration
//    @EnableJpaRepositories(basePackages = "org.befun.core.repository.impl",  repositoryBaseClass = BaseRepositoryImpl.class)
//    public class BaseJPAConfig {
//    }
//
//        public static void main(String[] args) {
//        SpringApplication.run(Application.class, args);
//    }
//}