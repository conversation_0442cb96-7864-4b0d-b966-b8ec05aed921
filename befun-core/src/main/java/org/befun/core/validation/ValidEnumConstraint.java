package org.befun.core.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ValidEnumConstraint implements ConstraintValidator<ValidEnum, Enum<?>> {

    private boolean notnull;
    private int flag;
    private Class<? extends ValidEnumEnable> enumClass;

    @Override
    public void initialize(ValidEnum constraintAnnotation) {
        this.flag = constraintAnnotation.flag();
        this.notnull = constraintAnnotation.notnull();
        this.enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(Enum<?> value, ConstraintValidatorContext context) {
        if (value == null) {
            if (notnull) {
                setInvalid("不能为空", context);
                return false;
            }
            return true;
        }
        boolean r = false;
        String validValues = null;
        if (enumClass.isEnum() && value instanceof ValidEnumEnable) {
            ValidEnumEnable valid = (ValidEnumEnable) value;
            r = valid.check(flag,value);
            validValues = valid.validValues();
        }
        if (r) {
            return true;
        }
        setInvalid(validValues, context);
        return false;
    }

    private void setInvalid(String msg, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg).addConstraintViolation();
    }
}
