package org.befun.core.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Repeatable(ValidSearchText.List.class)
@Documented
@Constraint(validatedBy = {ValidSearchTextConstraint.class})
public @interface ValidSearchText {

    boolean notEmpty() default false;

    String illegalRegexp() default "[#$&*!=<>()'\"\\s]+";

    String message() default "搜索条件不支持空格换行或包含#$&*!=<>()'\"等特殊字符";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
    @Retention(RUNTIME)
    @Documented
    @interface List {

        ValidSearchText[] value();
    }
}