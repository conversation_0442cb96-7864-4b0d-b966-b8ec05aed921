package org.befun.core.validation;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.RegHelper;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.regex.Pattern;

public class ValidSearchTextConstraint implements ConstraintValidator<ValidSearchText, String> {

    private boolean notEmpty;
    private String illegalRegexp;

    @Override
    public void initialize(ValidSearchText constraintAnnotation) {
        this.notEmpty = constraintAnnotation.notEmpty();
        this.illegalRegexp = constraintAnnotation.illegalRegexp();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isEmpty(value)) {
            if (notEmpty) {
                setInvalid("不能为空", context);
                return false;
            }
            return true;
        }
        Pattern pattern = Pattern.compile(illegalRegexp);
        return Arrays.stream(value.split("")).noneMatch(i -> RegHelper.isMatched(i, pattern));
    }

    private void setInvalid(String msg, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg).addConstraintViolation();
    }
}
