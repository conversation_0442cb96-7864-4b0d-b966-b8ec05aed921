package org.befun.core.converter;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import java.util.Set;


@Slf4j
public class SetConverter implements AttributeConverter<Set<String>, String> {

    @Override
    public String convertToDatabaseColumn(Set<String> customerInfo) {
        return JsonHelper.toJson(customerInfo);
    }

    @Override
    public Set<String> convertToEntityAttribute(String customerInfoJSON) {
        return JsonHelper.toSet(customerInfoJSON, String.class);
    }

}

