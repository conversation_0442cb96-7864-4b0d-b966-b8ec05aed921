package org.befun.core.converter;

import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Converter
public class StringListConverter implements AttributeConverter<List<String>, String> {

    public String getSplitChar() {
        return ";";
    }

    @Override
    public String convertToDatabaseColumn(List<String> stringList) {
        return stringList != null ? String.join(getSplitChar() , stringList) : null;
    }

    @Override
    public List<String> convertToEntityAttribute(String string) {
        return StringUtils.isEmpty(string) ? Collections.emptyList() : Arrays.asList(string.split(getSplitChar()));
    }
}
