package org.befun.core.converter;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class ListConverter implements AttributeConverter<List<String>, String> {

    @Override
    public String convertToDatabaseColumn(List<String> customerInfo) {
        return JsonHelper.toJson(customerInfo);
    }

    @Override
    public List<String> convertToEntityAttribute(String customerInfoJSON) {
        return JsonHelper.toList(customerInfoJSON, String.class);
    }

}

