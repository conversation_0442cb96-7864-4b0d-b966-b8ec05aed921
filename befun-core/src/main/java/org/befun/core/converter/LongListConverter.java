package org.befun.core.converter;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Converter
public  class LongListConverter implements AttributeConverter<List<Long>, String> {
    public String getSplitChar() {
        return ";";
    }

    @Override
    public String convertToDatabaseColumn(List<Long> list) {
        return list != null ? StringUtils.join(list, getSplitChar()) : "";
    }

    @Override
    public List<Long> convertToEntityAttribute(String string) {
        return string != null ?
                Arrays.stream(string.split(getSplitChar()))
                        .filter(NumberUtils::isDigits)
                        .map(Long::valueOf)
                        .collect(Collectors.toList())
                : Collections.emptyList();
    }
}
