package org.befun.core.converter;

import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Converter
public class LongRawListConverter implements AttributeConverter<List<Long>, String> {

    @Override
    public String convertToDatabaseColumn(List<Long> customerInfo) {
        return JsonHelper.toJson(customerInfo);
    }

    @Override
    public List<Long> convertToEntityAttribute(String customerInfoJSON) {
        return JsonHelper.toList(customerInfoJSON, Long.class);
    }

}

