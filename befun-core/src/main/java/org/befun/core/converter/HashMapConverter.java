package org.befun.core.converter;

import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import java.io.IOException;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class HashMapConverter implements AttributeConverter<Map<String, Object>, String> {

    @Override
    public String convertToDatabaseColumn(Map<String, Object> customerInfo) {
        return JsonHelper.toJson(customerInfo);
    }

    @Override
    public Map<String, Object> convertToEntityAttribute(String customerInfoJSON) {
        return JsonHelper.toMap(customerInfoJSON);
    }

}

