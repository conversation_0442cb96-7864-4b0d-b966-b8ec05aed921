package org.befun.core.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class ObjectConverter<T> implements AttributeConverter<T, String> {

    @Override
    public String convertToDatabaseColumn(T customerInfo) {
        return JsonHelper.toJson(customerInfo);
    }

    @Override
    public T convertToEntityAttribute(String s) {
        return JsonHelper.toObject(s, new TypeReference<T>() {});
    }
}

