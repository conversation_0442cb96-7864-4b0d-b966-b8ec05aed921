package org.befun.core.converter;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;
import java.util.Map;

@Converter
@Slf4j
public class MapListConverter implements AttributeConverter<List<Map<String, Object>>, String> {

    @Override
    public String convertToDatabaseColumn(List<Map<String, Object>> list) {
        return JsonHelper.toJson(list);
    }

    @Override
    public List<Map<String, Object>> convertToEntityAttribute(String value) {
        return JsonHelper.toListMap(value);
    }
}
