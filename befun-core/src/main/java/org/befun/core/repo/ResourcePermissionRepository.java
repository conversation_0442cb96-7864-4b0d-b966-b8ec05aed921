package org.befun.core.repo;

import org.befun.core.entity.ResourcePermission;
import org.befun.core.repository.ResourceRepository;

import java.util.Optional;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
public interface ResourcePermissionRepository extends ResourceRepository<ResourcePermission, Long> {
    List<ResourcePermission> findAllByResourceTypeAndResourceId(String type, Long resourceId);
    List<ResourcePermission> findAllByResourceTypeAndResourceIdIn(String type, List<Long> resourceId);
    List<ResourcePermission> findAllByResourceTypeAndResourceIdAndUserId(String type, Long resourceId, Long userId);
    List<ResourcePermission> findAllByResourceTypeAndResourceIdAndRoleId(String type, Long resourceId, Long roleId);
}