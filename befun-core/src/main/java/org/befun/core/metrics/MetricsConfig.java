package org.befun.core.metrics;

import io.micrometer.core.instrument.config.MeterFilter;
import org.befun.core.configuration.BefunMetricsProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class MetricsConfig {

    @Bean
    @ConditionalOnProperty(value = "befun.core.metrics.enable-common-tags", havingValue = "true")
    public MeterFilter befunCommonTagFilter(BefunMetricsProperties properties) {
        return MeterFilter.commonTags(properties.commonTagsAsSet());
    }

    @Bean
    public MeterFilter befunAcceptFilter(BefunMetricsProperties properties) {
        return MeterFilter.denyUnless(id -> {
            return properties.getSupportMetrics().stream().anyMatch(i -> id.getName().startsWith(i));
        });
    }
}
