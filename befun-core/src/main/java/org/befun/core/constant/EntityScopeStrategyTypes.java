package org.befun.core.constant;

public interface EntityScopeStrategyTypes {

    String getFilter();

    default String getResourceType() {
        return null;
    }

    public static EntityScopeStrategyTypes getInstance(ResourcePermissionTypes resourceType, EntityScopeStrategyType type) {
        return getInstance(resourceType.name(), type.getFilter());
    }

    public static EntityScopeStrategyTypes getInstance(String resourceType, String filter) {
        return new EntityScopeStrategyTypes() {

            @Override
            public String getFilter() {
                return filter;
            }

            @Override
            public String getResourceType() {
                return resourceType;
            }
        };
    }

}
