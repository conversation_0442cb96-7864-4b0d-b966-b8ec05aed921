package org.befun.core.constant;

import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Map;

@NoArgsConstructor
@Getter
public enum ParameterType {
    LIMIT("_limit"),
    PAGE("_page"),
    SORT("_sort"),
    BY("_by"),
    Q("_q"),
    OR("_or");

    private String label = "";

    public static final Map<String, ParameterType> OPERATORS_LOOKUP = Maps.uniqueIndex(
            Arrays.asList(ParameterType.values()),
            ParameterType::getLabel
    );

    private ParameterType(String label) {
        this.label = label;
    }
}
