package org.befun.core.constant;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import java.util.function.Supplier;

public enum CustomQueryType {

    ALL,
    ALL_WITH_GROUP,
    OWNER,
    OWNER_WITH_GROUP,
    SHARE,
    SHARE_WITH_GROUP,
    OWNER_SHARE,
    OWNER_SHARE_WITH_GROUP,
    ;

    /**
     * @param type owner share all
     */
    public static CustomQueryType parseType(String type, Supplier<Boolean> hasGroup) {
        if (StringUtils.isEmpty(type)) {
            type = "owner";
        }
        if ("owner".equalsIgnoreCase(type)) {
            return hasGroup.get() ? OWNER_WITH_GROUP : OWNER;
        } else if ("all".equalsIgnoreCase(type)) {
            return hasGroup.get() ? ALL_WITH_GROUP : ALL;
        } else if ("share".equalsIgnoreCase(type)) {
            return hasGroup.get() ? SHARE_WITH_GROUP : SHARE;
        } else if ("ownerShare".equalsIgnoreCase(type)) {
            return hasGroup.get() ? OWNER_SHARE_WITH_GROUP : OWNER_SHARE;
        } else {
            return OWNER;
        }
    }
}
