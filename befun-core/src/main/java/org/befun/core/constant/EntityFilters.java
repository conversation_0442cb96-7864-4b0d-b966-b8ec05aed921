package org.befun.core.constant;

public interface EntityFilters {
    String shareFilter = "shareFilter";
    String organizationFilter = "organizationFilter";
    String orgExcludeOwnerFilter = "orgExcludeOwnerFilter";
    String departmentFilter = "departmentFilter";
    String ownerFilter = "ownerFilter";
    String corpFilter = "corpFilter";
    String ownerCorpFilter = "ownerCorpFilter";
    String ownerFolderCorpFilter = "ownerFolderCorpFilter";
    String groupCorpFilter = "groupCorpFilter";
    String ownerGroupCorpFilter = "ownerGroupCorpFilter";
    String none = "none";

    String resourceIdsSql = "(select rc.resource_id from resource_permission as rc where rc.org_id = :orgId and rc.resource_type = :type and (rc.user_id = :userId or rc.role_id in (:roleIds) or rc.department_id in (:departmentIds)))";
    String resourceGroupIdsSql = "(select rc.resource_id from resource_permission as rc where rc.org_id = :orgId and rc.resource_type = :groupResource and (rc.user_id = :userId or rc.role_id in (:roleIds) or rc.department_id in (:departmentIds)))";
}
