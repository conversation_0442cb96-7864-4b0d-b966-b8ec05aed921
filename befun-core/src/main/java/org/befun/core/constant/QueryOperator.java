package org.befun.core.constant;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.*;

@Getter
@NoArgsConstructor
public enum QueryOperator {
    GREATER_THAN("gt"),
    LESS_THAN("lt"),
    GREATER_THAN_EQUAL("gte"),
    LESS_THAN_EQUAL("lte"),
    NOT_EQUAL("neq"),
    EQUAL("eq"),
    IN("in", ValueType.MULTI),
    NOT_IN("notin", ValueType.MULTI),
    CONTAIN("contain"),
    LIKE("like"),
    L_LIKE("llike"),
    R_LIKE("rlike"),
    IS_NULL("isnull", ValueType.NONE),
    NOT_NULL("notnull", ValueType.NONE),
    ;

    private String alias;
    private ValueType valueType = ValueType.SINGLE;

    private static final Map<String, QueryOperator> BY_ALIAS = new HashMap<>();

    static {
        for (QueryOperator e : values()) {
            BY_ALIAS.put(e.alias, e);
        }
    }

    QueryOperator(String alias) {
        this.alias = alias;
    }

    QueryOperator(String alias, ValueType valueType) {
        this.alias = alias;
        this.valueType = valueType;
    }

    public static QueryOperator valueOfAlias(String alias) {
        return BY_ALIAS.get(alias);
    }

    public enum ValueType {
        NONE,
        SINGLE,
        MULTI
    }
}
