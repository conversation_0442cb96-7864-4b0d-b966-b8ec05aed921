package org.befun.core.constant;

import lombok.Getter;

@Getter
public enum ErrorCode implements IErrorCode {
    UNKNOWN(-1),
    TOKEN_REFRESH(10000),       // 重新登录，导致了token刷新了
    ENTITY_NOT_FOUND(20001),
    BAD_PARAMETER(20002),
    BAD_BUSINESS(20010),
    EXPIRED(20003),
    INTEGRATION_FAULT(21000),
    OVER_LIMIT(30000);

    private final int value;

    ErrorCode(int value) {
        this.value = value;
    }
}
