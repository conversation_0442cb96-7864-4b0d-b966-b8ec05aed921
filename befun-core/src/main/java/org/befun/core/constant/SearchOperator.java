package org.befun.core.constant;

import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.query.criteria.internal.path.SingularAttributePath;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.metamodel.Attribute;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.befun.core.utils.ParamsHelper.castFun;

@Getter
@NoArgsConstructor
public enum SearchOperator {
    //    GREATER_THAN("gt"),
    //    LESS_THAN("lt"),
    //    GREATER_THAN_EQUAL("gte"),
    //    LESS_THAN_EQUAL("lte"),
    //    NOT_EQUAL("neq"),
    //    EQUAL("eq"),
    //    IN("in"),
    //    CONTAIN("contain");
    GREATER_THAN("gt") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.greaterThan((Path<Comparable>) path, (Comparable) castValue(path, value));
        }
    },
    LESS_THAN("lt") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.lessThan((Path<Comparable>) path, (Comparable) castValue(path, value));
        }
    },
    GREATER_THAN_EQUAL("gte") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.greaterThanOrEqualTo((Path<Comparable>) path, (Comparable) castValue(path, value));
        }
    },
    LESS_THAN_EQUAL("lte") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.lessThanOrEqualTo((Path<Comparable>) path, (Comparable) castValue(path, value));
        }
    },
    NOT_EQUAL("neq") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.notEqual(path, castValue(path, value));
        }
    },
    EQUAL("eq") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.equal(path, castValue(path, value));
        }
    },
    IN("in") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return path.in((List<?>) castValue(path, value));
        }

        @Override
        public Object castValue(Path<?> path, Object value) {
            return castInValue(path, value);
        }
    },
    CONTAIN("contain") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return cb.like((Path<String>) path, "%" + castValue(path, value) + "%");
        }
    },
    NOT_NULL("notnull"){
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return path.isNotNull();
        }
    },
    ISNULL("isnull") {
        @Override
        public Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value) {
            return path.isNull();
        }
    };

    private String label = "";

    public abstract Predicate predicate(Path<?> path, CriteriaBuilder cb, Object value);

    public Object castValue(Path<?> path, Object value) {
        if (forceToLong(path)) {
            return Long.valueOf(value.toString());
        }
        return castFun(path.getJavaType()).apply(value);
    }

    private boolean forceToLong(Path<?> path) {
        if (path instanceof SingularAttributePath) {
            Attribute.PersistentAttributeType type = ((SingularAttributePath<?>) path).getAttribute().getPersistentAttributeType();
            switch (type) {
                case MANY_TO_ONE, ONE_TO_ONE -> {
                    return true;
                }
            }
        }
        return false;
    }

    public Object castInValue(Path<?> path, Object value) {
        Function<Object, Object> castFun;
        if (forceToLong(path)) {
            castFun = v -> Long.parseLong(v.toString());
        } else {
            castFun = castFun(path.getJavaType());
        }
        return Arrays.stream(value.toString().split(",")).map(castFun).filter(Objects::nonNull).collect(Collectors.toList());
    }


    public static final Map<String, SearchOperator> OPERATORS_LOOKUP = Maps.uniqueIndex(
            Arrays.asList(SearchOperator.values()),
            SearchOperator::getLabel
    );

    public static SearchOperator valueOfLabel(String label) {
        return OPERATORS_LOOKUP.get(label);
    }

    private SearchOperator(String label) {
        this.label = label;
    }
}
