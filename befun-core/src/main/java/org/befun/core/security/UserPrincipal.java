package org.befun.core.security;

import lombok.Getter;
import org.befun.core.constant.LoginMethod;
import org.befun.core.dto.UserDto;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
public class UserPrincipal implements UserDetails {
    private static final List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(
            new String[]{
                "ROLE_ANONYMOUS",
                "EXECUTE_OPENAPI"
            }
    );;
    private UserDto user;

    /**
     *
     * @param user
     */
    public UserPrincipal(UserDto user) {
        this.user = user;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return user.getUsername();
    }

    @Override
    public boolean isAccountNonExpired() {
        return false;
    }

    @Override
    public boolean isAccountNonLocked() {
        return false;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return false;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
