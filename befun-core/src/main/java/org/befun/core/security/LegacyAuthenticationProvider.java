package org.befun.core.security;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.constant.ErrorCode;
import org.befun.core.dto.UserDto;
import org.befun.core.exception.BusinessException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LegacyAuthenticationProvider implements AuthenticationProvider {
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        Object userPrincipal = authentication.getPrincipal();
        UserDto userDto;
        if (userPrincipal instanceof UserPrincipal && (userDto = ((UserPrincipal) userPrincipal).getUser()) != null && !userDto.getActiveSession()) {
            throw new BusinessException(ErrorCode.TOKEN_REFRESH, "TOKEN_REFRESH");
        }
        return authentication;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }
}
