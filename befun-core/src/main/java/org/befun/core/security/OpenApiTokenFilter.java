package org.befun.core.security;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.UserDto;
import org.befun.core.utils.ListHelper;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * LegacyAuthToken, 兼容旧系统的AuthToken验证，基于Redis获取UserService登陆的Redis Token 信息
 *
 * <AUTHOR>
 */
@Slf4j
@Order(10)
public class OpenApiTokenFilter extends OncePerRequestFilter {
    public static final String HEADER = "X-API-KEY";
    @Autowired
    private EntityManager entityManager;

    /**
     * OpenApiTokenFilter 使用开放接口x-api-key
     * @param request
     * @param response
     * @param filterChain
     * @throws ServletException
     * @throws IOException
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String apiKey = request.getHeader(HEADER);
        if (!Strings.isNullOrEmpty(apiKey)) {
            UserDto user = fetchUserByApiKey(apiKey);
            if (user != null) {
                log.debug("fetch user info userId:{} orgId:{}", user.getId(), user.getOrgId());
                UserPrincipal principal = new UserPrincipal(user);
                Collection<? extends GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(
                        new String[]{
                                "EXECUTE_OPENAPI"
                        }
                );
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, apiKey, authorities);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                TenantInterceptor.interceptTenant();
            }
        }
        try {
            filterChain.doFilter(request, response);
        } finally {
            TenantInterceptor.afterCompletion();
        }
    }

    private Set<Long> getSubId(List<Long> targetIds, List<Long[]> departmentIds) {
        Set<Long> subIds = new HashSet<>();
        for (Long tid : targetIds) {
            departmentIds.stream().filter(d -> d[1].equals(tid)).forEach(d -> {
                subIds.add(d[0]);
                subIds.addAll(getSubId(ListHelper.arrayList(d[0]), departmentIds));
            });
        }
        return subIds;
    }
    /**
     *
     * @param apiKey
     * @return
     */
    private UserDto fetchUserByApiKey(String apiKey) {
        String sql = "SELECT u.id AS user_id," +
                "u.org_id AS org_id," +
                "u.truename AS truename," +
                "u.department_ids AS department_ids, " +
                "u.is_admin AS is_admin " +
                "FROM api_key k JOIN user u ON k.user_id = u.id WHERE k.api_key = :apiKey";
        Session session = entityManager.unwrap(Session.class);
        NativeQuery query = session.createSQLQuery(sql);
        log.info(sql);
        query.setParameter("apiKey", apiKey);
        try {
            Object result = query.getSingleResult();
            if (result != null) {
                Object[] attrs = (Object[]) result;
                UserDto userDto = new UserDto();
                userDto.setId(Long.valueOf(attrs[0].toString()));
                userDto.setOrgId(Long.valueOf(attrs[1].toString()));
                userDto.setUsername(Optional.ofNullable(attrs[2]).map(Object::toString).orElse(""));
                if (attrs[3] != null) {
                    List<Long> departmentIds = ListHelper.parseDepartmentIds(attrs[3].toString());
                    userDto.setDepartmentIds(departmentIds);
                    String subDepartmentIds = "SELECT id, pid\n" +
                            "  FROM department\n" +
                            "  WHERE org_id = :orgId";
                    NativeQuery querySubDepartmentIds = session.createSQLQuery(subDepartmentIds);
                    log.info(subDepartmentIds);
                    querySubDepartmentIds.setParameter("orgId", attrs[1]);
                    List<Object[]> subDepartmentIdsResult = querySubDepartmentIds.getResultList();
                    Optional.ofNullable(subDepartmentIdsResult).ifPresent(sub -> {
                        Set<Long> subIds = getSubId(departmentIds, sub.stream().map(s -> {
                            return new Long[]{Long.valueOf(s[0].toString()), Long.valueOf(s[1].toString())};
                        }).collect(Collectors.toList()));
                        subIds.addAll(departmentIds);
                        userDto.setSubDepartmentIds(new ArrayList<>(subIds));
                    });

                }
                if (attrs[4] != null) {
                    userDto.setIsAdmin(Integer.parseInt(attrs[4].toString()) == 1);
                }
                userDto.setActiveSession(true);
                return userDto;
            }
        } catch (NoResultException ex) {
            log.warn("api key not found {}", apiKey.substring(0, 4));
        }
        return null;
    }
}


