package org.befun.core.security;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.UserDto;
import org.befun.core.rest.annotation.Interceptor;
import org.befun.core.rest.context.TenantContext;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Interceptor
@Order(20)
public class TenantInterceptor {

    public static boolean interceptTenant() {
        TenantContext.clear();
        SecurityContext securityContext = SecurityContextHolder.getContext();
        if (securityContext != null) {
            Authentication authentication = securityContext.getAuthentication();
            Object principal = authentication.getPrincipal();
            if (principal instanceof UserPrincipal) {
                UserDto user = ((UserPrincipal) principal).getUser();
                log.debug("tenant->preHandle for user{}", user.getId());
                TenantContext.setCurrentTenant(user.getOrgId());
                TenantContext.setCurrentUserId(user.getId());
                TenantContext.setCurrentDepartmentIds(user.getDepartmentIds());
                TenantContext.setCurrentSubDepartmentIds(user.getSubDepartmentIds());
                TenantContext.setCurrentRoleIds(user.getRoleIds());
                TenantContext.setCurrentIsAdmin(user.getIsAdmin());
                TenantContext.setCurrentIsTop(user.getIsTop());
                TenantContext.setCurrentPermissions(user.getPermissions());
                TenantContext.setCurrentUserEvents(user.getUserEvents());
            }
        }
        return true;
    }

    public static void afterCompletion() {
        log.debug("tenant->afterCompletion->cleanup");
        TenantContext.clear();
    }
}
