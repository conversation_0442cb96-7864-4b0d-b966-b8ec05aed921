package org.befun.core.security;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.dto.UserDto;
import org.befun.core.dto.UserPermissions;
import org.befun.core.exception.UserEventException;
import org.befun.core.service.IDepartmentCacheService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.ListHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.befun.core.utils.JsonHelper.*;

/**
 * LegacyAuthToken, 兼容旧系统的AuthToken验证，基于Redis获取UserService登陆的Redis Token 信息
 *
 * <AUTHOR>
 */
@Slf4j
@Order(10)
public class LegacyAuthTokenFilter extends OncePerRequestFilter {
    private static final String SESSION_KEY_PREFIX = "session:";                            // hash     ${token}                用户信息
    private static final String SESSION_KEY_ACTIVE = "session.active:%s:%d";                // string   ${platform} ${userId}   指定平台上用户的唯一有效的token
    private static final String SESSION_KEY_FIXED = "session.active:fixed";                 // set                              列表中的token不做唯一限制
    private static final String USER_SESSION_KEY = "user.session:%d";                       // zSet     ${userId}               记录用户的所有token
    private static final String USER_SESSION_WHITELIST_KEY = "user.session.whitelist";      // set                              列表中的用户不做唯一限制

    @Autowired
    private RedisTemplate<String, String> template;

    @Autowired(required = false)
    private List<UserEvent> userEvents;

    @Autowired(required = false)
    private IDepartmentCacheService departmentCacheService;

    @Value(UserEvent.CONFIG_KEY_VALUE)
    private boolean enableUserEvent;

    public static final String AUTHORIZATION = "Authorization";

    /**
     * LegacyAuthToken 使用目前 UserService 鉴权， 基于Redis实现
     *
     * @param request
     * @param response
     * @param filterChain
     * @throws ServletException
     * @throws IOException
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String token = request.getHeader(AUTHORIZATION);
        if (!Strings.isNullOrEmpty(token)) {
            UserDto user = fetchUserInfoByToken(token);
            if (user != null) {
                log.debug("fetch user info userId:{} orgId:{}", user.getId(), user.getOrgId());
                UserPrincipal principal = new UserPrincipal(user);
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, token);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                TenantInterceptor.interceptTenant();
            }
        }
        try {
            filterChain.doFilter(request, response);
        } finally {
            TenantInterceptor.afterCompletion();
        }
    }

    public static String getSessionKey(String token) {
        return SESSION_KEY_PREFIX + token;
    }

    public static String getActiveSessionKey(Long userId, String platform) {
        return String.format(SESSION_KEY_ACTIVE, platform, userId);
    }

    public static String getUserSessionKey(Long userId) {
        return String.format(USER_SESSION_KEY, userId);
    }

    public static String getUserSessionWhitelistKey() {
        return USER_SESSION_WHITELIST_KEY;
    }

    /**
     * LegacyAuthToken 使用目前 UserService 鉴权， Redis token如果存在就会同时有一个完整的对象，包括
     * - orgId
     * - userId
     * - roleId
     * - orgCode
     * - username
     * hmset session:test_user1 orgId 1 userId 1 roleId 1 departmentId 1 username user1 is_admin 1 departments [1,2,3]
     *
     * @param token
     * @return
     */
     public UserDto fetchUserInfoByToken(String token) {
        token = StringUtils.removeStart(token, "Bearer").trim();
        log.debug("checking token from redis {}", token);

        String sessionKey = getSessionKey(token);
        Map<Object, Object> maps = template.opsForHash().entries(sessionKey);
        if (maps.size() == 0) {
            return null;
        }

        UserDto user = new UserDto();
        user.setUserEvents(getUserEvent(maps));
        mapLongProperty(maps.get("userId"), user::setId);
        mapLongProperty(maps.get("orgId"), user::setOrgId);
        mapStringProperty(maps.get("plan"), user::setPlan);
        mapStringProperty(maps.get("orgCode"), user::setOrgCode);
        mapStringProperty(maps.get("username"), user::setUsername);
        mapProperty(maps.get("is_admin"), NumberUtils::isDigits, i -> Integer.parseInt(i) == 1, user::setIsAdmin);
        mapProperty(maps.get("is_top"), NumberUtils::isDigits, i -> Integer.parseInt(i) == 1, user::setIsTop);
        mapProperty(maps.get("permissions"), i -> true, j -> JsonHelper.toObject(j, UserPermissions.class), user::setPermissions);
        // roleIds
        // 优先使用 roleIds 这个key
        // 如果不存在，则使用 roleId 这个key
        Object roleIds = maps.get("roleIds");
        if (roleIds != null) {
            mapListLongProperty(roleIds, user::setRoleIds);
        } else {
            mapListLongProperty(maps.get("roleId"), user::setRoleIds);
        }
        // departmentIds
        // 优先使用 departmentIds 这个key
        // 如果不存在，则使用 departmentId 这个key
        Object departmentIds = maps.get("departmentIds");
        if (departmentIds != null) {
            mapListLongProperty(departmentIds, user::setDepartmentIds);
        } else {
            mapLongProperty(maps.get("departmentId"), i -> user.setDepartmentIds(ListHelper.arrayList(i)));
        }
        // subDepartmentIds
        // 优先使用 calSubDepartmentIds 这个方法
        // 如果返回null, 则使用 subDepartmentIds 这个key
        // 如果不存在，则使用 departments 这个key
        List<Long> subDepartmentIds = calSubDepartmentIds(user);
        if (subDepartmentIds != null) {
            user.setSubDepartmentIds(subDepartmentIds);
        } else {
            Object departmentIds1 = maps.get("subDepartmentIds");
            if (departmentIds1 != null) {
                mapListLongProperty(departmentIds1, user::setSubDepartmentIds);
            } else {
                mapListLongProperty(maps.get("departments"), i -> i.replace("[", "").replace("]", ""), user::setSubDepartmentIds);
            }
        }
        activeSession(token, sessionKey, user);
        return user;
    }

    private void activeSession(String token, String sessionKey, UserDto user) {
        Boolean isFixed = template.opsForSet().isMember(SESSION_KEY_FIXED, token);
        if (isFixed != null && isFixed) {
            user.setActiveSession(true);
        } else {
            String[] s = token.split("\\.");
            String platform;
            if (s.length > 1) {
                platform = s[0];
            } else {
                platform = "pc";
            }

            String activeSessionKey = getActiveSessionKey(user.getId(), platform);
            String activeToken = template.opsForValue().get(activeSessionKey);
            user.setActiveSession(StringUtils.isEmpty(activeToken) || activeToken.equals(sessionKey));
        }
    }

    private HashOperations<String, String, String> hashOpt() {
        return template.opsForHash();
    }

    private List<Long> calSubDepartmentIds(UserDto user) {
        if (departmentCacheService != null) {
            Set<Long> subDepartmentIds = new HashSet<>();
            if (user.getIsAdmin() != null && user.getIsAdmin()) {
                List<Long> ids = departmentCacheService.getCacheSubDepartmentIds(user.getOrgId(), null, true);
                if (CollectionUtils.isNotEmpty(ids)) {
                    subDepartmentIds.addAll(ids);
                }
            } else if (CollectionUtils.isNotEmpty(user.getDepartmentIds())) {
                user.getDepartmentIds().forEach(i -> {
                    List<Long> ids = departmentCacheService.getCacheSubDepartmentIds(user.getOrgId(), i, false);
                    if (CollectionUtils.isNotEmpty(ids)) {
                        subDepartmentIds.addAll(ids);
                    }
                });
            }
            return new ArrayList<>(subDepartmentIds);
        }
        return null;
    }

    private List<String> getUserEvent(Map<Object, Object> maps) {
        List<String> events = new ArrayList<>();
        if (enableUserEvent && CollectionUtils.isNotEmpty(userEvents)) {
            for (UserEvent event : userEvents) {
                if (event.hasEvent(maps)) {
                    UserEventException exception;
                    if (event.throwable() && (exception = event.exception()) != null) {
                        throw exception;
                    } else {
                        events.add(event.key());
                    }
                }
            }
        }
        return events;
    }
}


