package org.befun.core.security;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.UserEventException;
import org.springframework.data.redis.core.StringRedisTemplate;


import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 在实现类上标注{@link org.springframework.core.annotation.Order} 越小的优先级越高，如果前面的抛出异常了，后面就都会忽略了
 */
public interface UserEvent {

    String CONFIG_KEY = "befun.server.user-event.enable";
    String CONFIG_KEY_VALUE = "${" + CONFIG_KEY + ":false}";

    String EVENT_FLAG = "1";

    StringRedisTemplate redisTemplate();

    /**
     * 事件的key user-event-*
     */
    String key();

    /**
     * 如果 user session 中有此事件，是否抛出异常
     * 默认不抛出异常
     */
    default boolean throwable() {
        return false;
    }

    /**
     * 如果 user session 中有此事件，且{@link UserEvent#throwable()}==true，且此异常不为null，则抛出此异常
     */
    default UserEventException exception() {
        return null;
    }

    default boolean hasEvent(Map<Object, Object> redisCache) {
        String key = key();
        if (StringUtils.isNotEmpty(key)) {
            return Optional.ofNullable(redisCache.get(key))
                    .map(Object::toString)
                    .map(EVENT_FLAG::equals)
                    .orElse(false);
        }
        return false;
    }

    default void setEvent(String sessionKey) {
        String key = key();
        if (StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(sessionKey)) {
            redisTemplate().opsForHash().put(sessionKey, key, EVENT_FLAG);
        }
    }

    default void setEvent(Long userId) {
        String userSessionKey = LegacyAuthTokenFilter.getUserSessionKey(userId);
        Set<String> keys = redisTemplate().opsForZSet().rangeByScore(userSessionKey, 0, System.currentTimeMillis());
        if (CollectionUtils.isNotEmpty(keys)) {
            keys.forEach(this::setEvent);
        }
    }

}
