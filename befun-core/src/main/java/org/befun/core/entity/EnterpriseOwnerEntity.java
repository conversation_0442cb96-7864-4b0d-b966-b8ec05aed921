package org.befun.core.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
@EntityListeners(EnterpriseListener.class)
public abstract class EnterpriseOwnerEntity extends EnterpriseEntity implements EnterpriseOwnerAware {
    @Column(name = "user_id")
    public Long userId;
}
