package org.befun.core.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@MappedSuperclass
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
@EntityListeners(EnterpriseListener.class)
public abstract class EnterpriseTreeEntity<T extends EnterpriseTreeEntity> extends EnterpriseOwnerEntity implements TreeAware {
    @Column( name = "is_folder", columnDefinition = "bit(1) default 0")
    @DtoProperty(description = "isFolder", jsonView = ResourceViews.Basic.class)
    private Boolean isFolder = null;

    @Column( name = "parent_id", columnDefinition = "bigint")
    @DtoProperty(description = "parentId", jsonView = ResourceViews.Basic.class)
    private Long parentId;
}
