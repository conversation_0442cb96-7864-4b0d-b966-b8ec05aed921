package org.befun.core.entity.annotation;

import org.befun.core.constant.EntityScopeStrategyType;

import java.lang.annotation.*;

/**
 * 标记数据权限类型
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Documented
public @interface EntityScopeStrategy {
    EntityScopeStrategyType[] value() default EntityScopeStrategyType.ORGANIZATION;
    String[] filterNames() default {};

    String resource() default "";
    String groupResource() default "";

    /**
     * true:    如果是超级管理员，则会使用{@link EntityScopeStrategyType#ORGANIZATION}, 否则使用 {@link EntityScopeStrategy#value()}
     * false:   使用 {@link EntityScopeStrategy#value()}
     */
    boolean enableAdmin() default true;
}
