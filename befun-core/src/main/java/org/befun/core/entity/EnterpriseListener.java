package org.befun.core.entity;

import org.befun.core.rest.context.TenantContext;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class EnterpriseListener {

    @PrePersist
    public void setTenant(Object entity) {
        final Long tenantId = TenantContext.getCurrentTenant();
        final Long userId = TenantContext.getCurrentUserId();
        if (tenantId != null) {
            EnterpriseAware aware = (EnterpriseAware) entity;
            if (aware != null) {
                if (aware.getOrgId() == null) {
                    aware.setOrgId(tenantId);
                }
                if (userId != null) {
                    if (entity instanceof EnterpriseOwnerAware) {
                        EnterpriseOwnerAware ownerAware = (EnterpriseOwnerAware) entity;
                        if (ownerAware.getUserId() == null) {
                            ownerAware.setUserId(userId);
                        }
                    }
                    if (entity instanceof EnterpriseModifyAware) {
                        EnterpriseModifyAware modifyAware = (EnterpriseModifyAware) entity;
                        modifyAware.setModifyUserId(userId);
                    }
                }
            }
        }
    }

    @PreUpdate
    public void preUpdate(Object entity) {
        final Long userId = TenantContext.getCurrentUserId();
        if (userId != null) {
            if (entity instanceof EnterpriseModifyAware) {
                EnterpriseModifyAware modifyAware = (EnterpriseModifyAware) entity;
                modifyAware.setModifyUserId(userId);
            }
        }
    }
}