package org.befun.core.entity;

/**
 * The class description
 *
 * <AUTHOR>
 */

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * COPY FROM AUTH
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "resource_permission")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourcePermission extends EnterpriseEntity {

    @Column(name = "user_id")
    @DtoProperty
    private Long userId;

    @Column(name = "resource_id")
    @DtoProperty
    private Long resourceId;

    @Column(name = "role_id")
    @DtoProperty
    private Long roleId;

    @Column(name = "department_id")
    @DtoProperty
    private Long departmentId;

    @Column(name = "resource_type")
    @DtoProperty
    private String resourceType;

    @Column(name = "relation_type")
    @DtoProperty
    private String relationType;
}