package org.befun.core.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
@EntityListeners(EnterpriseListener.class)
public abstract class EnterpriseDepartmentEntity extends EnterpriseEntity implements EnterpriseAware {
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "department_id")
    public Long departmentId;
}
