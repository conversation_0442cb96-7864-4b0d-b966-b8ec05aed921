package org.befun.core.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.EntityUtility;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@MappedSuperclass
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public abstract class BaseEntity implements Serializable {

    @Id
    @GenericGenerator(name = "snowflake", strategy = "org.befun.core.generator.SnowflakeGenerator")
    @GeneratedValue(generator = "snowflake")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT '主键'")
    @DtoProperty(description = "主键", jsonView = ResourceViews.Basic.class)
    protected Long id;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "create_time", updatable = false, columnDefinition = "TIMESTAMP")
    @Schema(description = "新增和编辑时无需此参数")
    @DtoProperty(description = "创建时间", jsonView = ResourceViews.Basic.class, access = JsonProperty.Access.READ_ONLY)
    public Date createTime;

    @Temporal(TemporalType.TIMESTAMP)
    @UpdateTimestamp
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "modify_time", columnDefinition = "TIMESTAMP")
    @Schema(description = "新增和编辑时无需此参数")
    @DtoProperty(description = "修改时间", jsonView = ResourceViews.Basic.class, access = JsonProperty.Access.READ_ONLY)
    public Date modifyTime;

    @SneakyThrows
    public BaseEntity clone() {
        return EntityUtility.clone(this);
    }

    /**
     * not use CreationTimestamp, which is not easy for unit test.
     */
    @PrePersist
    public void setup() {
        if (this.createTime == null) {
            this.createTime = new Date();
        }
    }
}
