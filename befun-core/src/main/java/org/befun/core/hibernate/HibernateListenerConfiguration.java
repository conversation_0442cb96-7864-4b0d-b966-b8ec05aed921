package org.befun.core.hibernate;

import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Component
public class HibernateListenerConfiguration {
    @PersistenceUnit
    private EntityManagerFactory emf;

    @PostConstruct
    protected void init() {
        SessionFactoryImpl sessionFactory = emf.unwrap(SessionFactoryImpl.class);
        EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);
        registry.getEventListenerGroup(EventType.PERSIST).appendListener(RootAwarePersistEventListener.INSTANCE);
        registry.getEventListenerGroup(EventType.SAVE).appendListener(RootAwareUpdateEventListener.INSTANCE);
        registry.getEventListenerGroup(EventType.SAVE_UPDATE).appendListener(RootAwareUpdateEventListener.INSTANCE);
        registry.getEventListenerGroup(EventType.POST_UPDATE).appendListener(RootAwareUpdateEventListener.INSTANCE);
        registry.getEventListenerGroup(EventType.DELETE).appendListener(RootAwareDeleteEventListener.INSTANCE);
    }
}
