package org.befun.core.hibernate;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.befun.core.entity.RootAware;
import org.befun.core.service.SpringContextHolder;
import org.befun.core.utils.EntityUtility;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.event.spi.DeleteEvent;
import org.hibernate.event.spi.DeleteEventListener;
import org.hibernate.event.spi.EventSource;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import javax.transaction.Transactional;
import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.Set;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseResourceEventListener {

    /**
     * update root timestamp when relation changed
     * see detail @RootAware, @RootAwarePersistEventListener, @RootAwareUpdateEventListener
     * @param rootAware
     * @param session
     */
    public void updateRootTimestamp(RootAware rootAware, EventSource session) {
        Object root = rootAware.getRoot();
        if (root == null) {
            return;
        }
        try {
            if (root instanceof HibernateProxy) {
                // un-proxy the lazy relation
                root = Hibernate.unproxy(root);
            }
            PropertyUtils.setProperty(root, "modifyTime", new Date());
            session.save(root);
        } catch (IllegalAccessException ex) {
            ex.printStackTrace();
        } catch (InvocationTargetException ex) {
            ex.printStackTrace();
        } catch (NoSuchMethodException ex) {
            ex.printStackTrace();
        }
    }
}
