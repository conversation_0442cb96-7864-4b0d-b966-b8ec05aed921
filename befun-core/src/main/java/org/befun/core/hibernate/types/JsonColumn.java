package org.befun.core.hibernate.types;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.hibernate.HibernateException;
import org.hibernate.annotations.common.reflection.java.JavaXMember;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.DynamicParameterizedType;
import org.hibernate.usertype.UserType;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.function.Function;

@Slf4j
public class JsonColumn implements UserType, DynamicParameterizedType {
    public static final String TYPE = "org.befun.core.hibernate.types.JsonColumn";

    private final int sqlType = Types.VARCHAR;
    private Class<?> klass;
    private Function<String, Object> getValue;

    @Override
    public void setParameterValues(Properties parameters) {
        JavaXMember member = (JavaXMember) parameters.get(XPROPERTY);
        Field field = (Field) member.getMember();
        klass = field.getType();
        if (Map.class.isAssignableFrom(klass)) {
            Type[] args = ((ParameterizedType) field.getGenericType()).getActualTypeArguments();
            getValue = v -> JsonHelper.toMap(v, (Class<?>) args[0], (Class<?>) args[1]);
        } else if (List.class.isAssignableFrom(klass)) {
            Type[] args = ((ParameterizedType) field.getGenericType()).getActualTypeArguments();
            getValue = v -> JsonHelper.toList(v, (Class<?>) args[0]);
        } else {
            getValue = v -> JsonHelper.toObject(v, klass);
        }
    }

    @Override
    public int[] sqlTypes() {
        return new int[]{sqlType};
    }

    @Override
    public Class<?> returnedClass() {
        return this.klass;
    }

    @Override
    public boolean equals(Object x, Object y) throws HibernateException {
        return Objects.equals(x, y);
    }

    @Override
    public int hashCode(Object x) throws HibernateException {
        return Objects.hashCode(x);
    }

    /*
       This method will be called when hibernate initializes your entity's
       field from the appropriate database table row
    */
    @Override
    public Object nullSafeGet(ResultSet rs,
                              String[] names,
                              SharedSessionContractImplementor session,
                              Object owner) throws HibernateException, SQLException {
        String value = rs.getString(names[0]);
        if (!Strings.isNullOrEmpty(value)) {
            return getValue.apply(value);
        }
        return null;
    }

    /*
       This method will be called when hibernate persists your entity's field
       to the appropriate database table row
    */
    @Override
    public void nullSafeSet(PreparedStatement st,
                            Object value,
                            int index,
                            SharedSessionContractImplementor session) throws HibernateException, SQLException {
        // you can use this.columnLength here
        if (value == null) {
            st.setNull(index, sqlType);
        } else {
            st.setString(index, JsonHelper.toJson(value));
        }
    }

    @Override
    public Object deepCopy(Object value) throws HibernateException {
        return value;
    }

    @Override
    public boolean isMutable() {
        return false;
    }

    @Override
    public Serializable disassemble(Object value) throws HibernateException {
        return Objects.toString(value);
    }

    @Override
    public Object assemble(Serializable cached, Object owner) throws HibernateException {
        return cached;
    }

    @Override
    public Object replace(Object original, Object target, Object owner) throws HibernateException {
        return original;
    }
}
