package org.befun.core.hibernate;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.entity.RootAware;
import org.befun.core.utils.EntityUtility;
import org.hibernate.HibernateException;
import org.hibernate.event.spi.DeleteEvent;
import org.hibernate.event.spi.DeleteEventListener;
import org.hibernate.event.spi.SaveOrUpdateEvent;
import org.hibernate.event.spi.SaveOrUpdateEventListener;

import java.util.Set;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class RootAwareDeleteEventListener extends BaseResourceEventListener implements DeleteEventListener {

    public static final RootAwareDeleteEventListener INSTANCE = new RootAwareDeleteEventListener();

    @Override
    public void onDelete(DeleteEvent deleteEvent) throws HibernateException {
        final Object entity = deleteEvent.getObject();
        if (entity instanceof RootAware) {
            RootAware rootAware = (RootAware) entity;
            updateRootTimestamp(rootAware, deleteEvent.getSession());
        }
    }

    @Override
    public void onDelete(DeleteEvent deleteEvent, Set set) throws HibernateException {
        final Object entity = deleteEvent.getObject();
        if (entity instanceof RootAware) {
            RootAware rootAware = (RootAware) entity;
            updateRootTimestamp(rootAware, deleteEvent.getSession());
        }
    }
}
