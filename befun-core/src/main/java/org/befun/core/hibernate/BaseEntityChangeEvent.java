package org.befun.core.hibernate;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.constant.EntityEventType;
import org.befun.core.entity.BaseEntity;
import org.hibernate.event.spi.PreUpdateEvent;
import org.springframework.context.ApplicationEvent;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BaseEntityChangeEvent<T extends BaseEntity> extends ApplicationEvent {
    private T entity;
    private String name;
    private EntityEventType eventType;
    private Map<String, Object[]> changes = new HashMap<>();

    public BaseEntityChangeEvent(Object source) {
        super(source);
    }

    public BaseEntityChangeEvent(Object source, T entity, EntityEventType eventType) {
        super(source);

        this.name = entity.getClass().getSimpleName();
        this.entity = entity;
        this.eventType = eventType;
    }
}
