package org.befun.core.hibernate;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.befun.core.constant.EntityEventType;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EntityChangeAware;
import org.befun.core.entity.RootAware;
import org.befun.core.service.SpringContextHolder;
import org.befun.core.utils.EntityUtility;
import org.hibernate.HibernateException;
import org.hibernate.event.spi.PersistEvent;
import org.hibernate.event.spi.PersistEventListener;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.context.support.WebApplicationContextUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RootAwarePersistEventListener extends BaseResourceEventListener implements PersistEventListener {

    public static final RootAwarePersistEventListener INSTANCE = new RootAwarePersistEventListener();

    @Override
    public void onPersist(PersistEvent event) throws HibernateException {
        final Object entity = event.getObject();
        if (entity instanceof RootAware) {
            RootAware rootAware = (RootAware) entity;
            updateRootTimestamp(rootAware, event.getSession());
        }
        if (entity instanceof EntityChangeAware) {
            EntityChangeAware changeAware = (EntityChangeAware) entity;
            BaseEntityChangeEvent changeEvent = changeAware.buildChangeEvent(EntityEventType.CREATE);
            ApplicationEventPublisher eventPublisher = SpringContextHolder.getApplicationContext();
            eventPublisher.publishEvent(changeEvent);
        }
    }

    @Override
    public void onPersist(PersistEvent event, Map createdAlready)
            throws HibernateException {
        onPersist(event);
    }
}
