package org.befun.core.hibernate.types;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.hibernate.types.annotation.NestedColumn;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.DynamicParameterizedType;
import org.hibernate.usertype.UserType;

import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Objects;
import java.util.Properties;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class NestedEntityList implements UserType, DynamicParameterizedType {
    private int sqlType;
    private Class<?> klass;
    private ObjectMapper objectMapper;

    public NestedEntityList() {
        this.sqlType = Types.VARCHAR;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void setParameterValues(Properties parameters)
    {
        ParameterType reader = (ParameterType) parameters.get(PARAMETER_TYPE);
        for (Annotation annotation : reader.getAnnotationsMethod()){
            if (annotation instanceof NestedColumn) {
                this.klass = ((NestedColumn) annotation).value();
                return;
            }
        }
        if (this.klass == null) {
            throw new RuntimeException("invalid nested entity, should config @NestedColumn");
        }
    }

    @Override
    public int[] sqlTypes()
    {
        return new int[] {sqlType};
    }

    @Override
    public Class<?> returnedClass()
    {
        return List.class;
    }

    @Override
    public boolean equals(Object x, Object y) throws HibernateException
    {
        return Objects.equals(x, y);
    }

    @Override
    public int hashCode(Object x) throws HibernateException
    {
        return Objects.hashCode(x);
    }

    /*
       This method will be called when hibernate initializes your entity's
       field from the appropriate database table row
    */
    @Override
    public Object nullSafeGet(ResultSet rs,
                              String[] names,
                              SharedSessionContractImplementor session,
                              Object owner) throws HibernateException, SQLException
    {
        String value = rs.getString(names[0]);
        if (!Strings.isNullOrEmpty(value)) {
            try {
                return this.objectMapper.readerForListOf(this.klass).readValue(value);
            } catch (JsonProcessingException e) {
                log.warn("failed to convert nested entity from string %s error message: %s", value, e.getMessage());
                e.printStackTrace();
            }
        }
        return null;
    }

    /*
       This method will be called when hibernate persists your entity's field
       to the appropriate database table row
    */
    @Override
    public void nullSafeSet(PreparedStatement st,
                            Object value,
                            int index,
                            SharedSessionContractImplementor session) throws HibernateException, SQLException
    {
        // you can use this.columnLength here
        if (value == null) {
            st.setNull(index, sqlType);
        }
        else {
            String jsonStr = null;
            try {
                jsonStr = this.objectMapper.writeValueAsString(value);
            } catch (JsonProcessingException e) {
                log.warn("failed to convert nested entity to string");
                e.printStackTrace();
            }
            st.setString(index, jsonStr);
        }
    }

    @Override
    public Object deepCopy(Object value) throws HibernateException
    {
        return value;
    }

    @Override
    public boolean isMutable()
    {
        return false;
    }

    @Override
    public Serializable disassemble(Object value) throws HibernateException
    {
        return Objects.toString(value);
    }

    @Override
    public Object assemble(Serializable cached, Object owner) throws HibernateException
    {
        return cached;
    }

    @Override
    public Object replace(Object original, Object target, Object owner) throws HibernateException
    {
        return original;
    }
}
