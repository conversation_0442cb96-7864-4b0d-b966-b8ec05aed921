package org.befun.core.hibernate;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.constant.EntityEventType;
import org.befun.core.entity.EntityChangeAware;
import org.befun.core.entity.RootAware;
import org.befun.core.service.SpringContextHolder;
import org.hibernate.HibernateException;
import org.hibernate.event.spi.*;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.context.ApplicationEventPublisher;

import javax.persistence.Tuple;
import java.util.HashMap;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class RootAwareUpdateEventListener extends BaseResourceEventListener implements SaveOrUpdateEventListener, PostUpdateEventListener {

    public static final RootAwareUpdateEventListener INSTANCE = new RootAwareUpdateEventListener();

    @Override
    public void onSaveOrUpdate(SaveOrUpdateEvent e) throws HibernateException {
        final Object entity = e.getObject();
        if (entity instanceof RootAware) {
            RootAware rootAware = (RootAware) entity;
            updateRootTimestamp(rootAware, e.getSession());
        }
    }

    @Override
    public void onPostUpdate(PostUpdateEvent e) {
        final Object entity = e.getEntity();
        if (entity instanceof RootAware) {
            RootAware rootAware = (RootAware) entity;
            updateRootTimestamp(rootAware, e.getSession());
        }
        if (entity instanceof EntityChangeAware) {
            EntityPersister persister = e.getPersister();
            String[] propertyNames = persister.getPropertyNames();
            Map<String, Object[]> changes = new HashMap<>();
            for (int dirtyPropertyIndex : e.getDirtyProperties()) {
                changes.put(propertyNames[dirtyPropertyIndex], new Object[]{
                        e.getOldState()[dirtyPropertyIndex],
                        e.getState()[dirtyPropertyIndex]
                });
            }
            EntityChangeAware changeAware = (EntityChangeAware) entity;
            BaseEntityChangeEvent changeEvent = changeAware.buildChangeEvent(EntityEventType.UPDATE);
            changeEvent.setChanges(changes);
            ApplicationEventPublisher eventPublisher = SpringContextHolder.getApplicationContext();
            eventPublisher.publishEvent(changeEvent);
        }
    }

    @Override
    public boolean requiresPostCommitHanding(EntityPersister entityPersister) {
        return false;
    }
}
