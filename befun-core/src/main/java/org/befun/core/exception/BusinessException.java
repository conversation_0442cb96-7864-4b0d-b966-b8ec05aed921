package org.befun.core.exception;

import lombok.Getter;
import org.befun.core.constant.ErrorCode;
import org.springframework.http.HttpStatus;

/**
 * 此异常为业务异常，http状态码为200，code不等于200
 */
@Getter
public class BusinessException extends BaseException {

    private Object data;
    private int internalCode = 0;

    public BusinessException(int code, String message) {
        super(HttpStatus.OK, code, message);
    }

    public BusinessException(ErrorCode code, String message) {
        this(code.getValue(), message);
    }

    public BusinessException(String message) {
        this(ErrorCode.BAD_BUSINESS, message);
    }

    public BusinessException(int code, String message, Object data) {
        this(code, message);
        this.data = data;
    }

    public BusinessException(String message, int internalCode, Object data) {
        this(ErrorCode.BAD_BUSINESS, message);
        this.internalCode = internalCode;
        this.data = data;
    }

    public BusinessException(int code, String message, int internalCode, Object data) {
        this(code, message);
        this.internalCode = internalCode;
        this.data = data;
    }

    public BusinessException(String message, Object data) {
        this(ErrorCode.BAD_BUSINESS, message);
        this.data = data;
    }

    public BusinessException() {
        this(ErrorCode.BAD_BUSINESS, "");
    }

    public BusinessException codeOk() {
        return code(200);
    }

    public BusinessException code(int code) {
        setCode(code);
        return this;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
