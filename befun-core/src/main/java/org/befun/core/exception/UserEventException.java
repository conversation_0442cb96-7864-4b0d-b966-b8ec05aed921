package org.befun.core.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class UserEventException extends RuntimeException {

    private final HttpStatus httpStatus;
    private final int code;
    private final String message;

    private final String event;

    public UserEventException(HttpStatus httpStatus, int code, String message,String event) {
        super(message);
        this.httpStatus = httpStatus;
        this.code = code;
        this.message = message;
        this.event = event;
    }
}
