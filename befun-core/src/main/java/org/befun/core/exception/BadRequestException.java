package org.befun.core.exception;

import lombok.NoArgsConstructor;
import org.befun.core.constant.ErrorCode;

@NoArgsConstructor
public class BadRequestException extends BaseException {
    public BadRequestException(boolean printStack, String message) {
        super(ErrorCode.BAD_PARAMETER, message, printStack);
    }

    public BadRequestException(String message) {
        super(ErrorCode.BAD_PARAMETER, message);
    }

    public BadRequestException(int internalCode, String message) {
        super(ErrorCode.BAD_PARAMETER, message, internalCode);
    }

    public BadRequestException(String message, String detail) {
        super(ErrorCode.BAD_PARAMETER, message, detail);
    }
}
