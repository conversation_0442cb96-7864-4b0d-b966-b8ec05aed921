package org.befun.core.exception;

import org.befun.core.constant.ErrorCode;

public class EntityNotFoundException extends BaseException {

    public EntityNotFoundException(Class<?> clazz) {
        super(ErrorCode.ENTITY_NOT_FOUND, String.format("Entity(%s) Not Exist", clazz.getSimpleName()));
    }

    public EntityNotFoundException() {
        super(ErrorCode.ENTITY_NOT_FOUND, "Entity Not Exist");
    }
}
