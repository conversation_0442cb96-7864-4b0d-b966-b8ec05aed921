package org.befun.core.exception;

import lombok.Getter;
import lombok.NoArgsConstructor;
import org.befun.core.constant.ErrorCode;
import org.springframework.http.HttpStatus;

@Getter
@NoArgsConstructor
public class BaseException extends RuntimeException {
    private HttpStatus status = HttpStatus.BAD_REQUEST;
    private int code = ErrorCode.UNKNOWN.getValue();
    private int internalCode = 0;
    private String message = "";
    private String detail = "";
    private boolean printStack = true;

    public BaseException(ErrorCode code, String message, int internalCode, String detail, boolean printStack) {
        this.code = code.getValue();
        this.message = message;
        this.detail = detail;
        this.internalCode = internalCode;
        this.printStack = printStack;
    }

    public BaseException(ErrorCode code, String message, int internalCode) {
        this.code = code.getValue();
        this.message = message;
        this.internalCode = internalCode;
    }

    public BaseException(ErrorCode code, String message, int internalCode, String detail) {
        this.code = code.getValue();
        this.message = message;
        this.detail = detail;
        this.internalCode = internalCode;
    }

    public BaseException(int code, String message, int internalCode) {
        this.code = code;
        this.message = message;
        this.internalCode = internalCode;
    }

    public BaseException(int code, String message, int internalCode, String detail) {
        this.code = code;
        this.message = message;
        this.detail = detail;
        this.internalCode = internalCode;
    }

    public BaseException(ErrorCode code, String message, String detail) {
        this.code = code.getValue();
        this.message = message;
        this.detail = detail;
    }

    public BaseException(ErrorCode code, String message) {
        this.code = code.getValue();
        this.message = message;
    }

    public BaseException(ErrorCode code, String message, boolean printStack) {
        this.code = code.getValue();
        this.message = message;
        this.printStack = printStack;
    }

    public BaseException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public BaseException(HttpStatus status, int code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setPrintStack(boolean printStack) {
        this.printStack = printStack;
    }
}


