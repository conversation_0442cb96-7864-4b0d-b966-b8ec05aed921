package org.befun.core.utils;

import lombok.Data;
import org.befun.core.entity.BaseEntity;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class EntityUtilityTest {

    @Data
    public class EntityTest extends BaseEntity {
        private Integer fieldInteger;
        private String fieldString;
        private Double fieldDouble;
        private List fieldList;
        private Map fieldMap;
    }

    private static Map<String, Object> data = new HashMap<>();
    private EntityTest entity = new EntityTest();

    @BeforeAll
    public static void setUp() {
        data.put("fieldInteger", "123");
        data.put("fieldString", 123);
        data.put("fieldDouble", 1);
        data.put("fieldList", List.of(1, 2));
        data.put("fieldMap", Map.of("key", "value"));
    }

    @Test
    @DisplayName("merge_entity_data")
    public void mergeEntityData() {
        EntityUtility.mergeEntityWithData(entity, data);
        Assertions.assertEquals(data.get("fieldInteger"), entity.getFieldInteger().toString());
        Assertions.assertEquals(data.get("fieldString").toString(), entity.getFieldString());
        Assertions.assertEquals(Double.valueOf(data.get("fieldDouble").toString()), entity.getFieldDouble());
        Assertions.assertEquals(data.get("fieldList"), entity.getFieldList());
        Assertions.assertEquals(data.get("fieldMap"), entity.getFieldMap());

    }
}
