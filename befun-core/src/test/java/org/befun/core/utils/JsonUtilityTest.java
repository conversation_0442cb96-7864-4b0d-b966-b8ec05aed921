package org.befun.core.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class JsonUtilityTest {
    class Student {
        private String name;
        private List<Book> books = new ArrayList<>();

        public Student(String name, List<Book> books) {
            this.name = name;
            this.books = books;
        }
    }

    class Book {
        private String name;

        public Book(String name) {
            this.name = name;
        }
    }

    @SneakyThrows
    @Test
    public void mappingNestedObject() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<Book> books = new ArrayList<>();
        books.add(new Book("b1"));
        Student student = new Student("s1", books);
        String result = objectMapper.writeValueAsString(student);
        System.out.println(result);
    }

    @Test
    public void test() {
        new JsonHelper(new ObjectMapper());
        String s = "[{'a':'b','c':1},{'a':'d','c':2}]";
        List<Map<String, Object>> i = JsonHelper.toListMap(s);
        System.out.printf(JsonHelper.toJson(i.get(0)));
    }
}
