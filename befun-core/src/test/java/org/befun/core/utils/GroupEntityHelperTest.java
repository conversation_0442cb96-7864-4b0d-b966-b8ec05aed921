package org.befun.core.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AllArgsConstructor;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.query.PageResult;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

class GroupEntityHelperTest {


    @Test
    public void test() throws JsonProcessingException {
        List<TestGroup> allGroup = IntStream.range(0, 19).mapToObj(TestGroup::new).collect(Collectors.toList());
        List<TestDataDto> allData = IntStream.range(100, 102).mapToObj(TestDataDto::new).collect(Collectors.toList());

        Consumer<Page<TestDataDto>> print = i -> {
            String list = i.getContent().stream().map(j -> String.format("%3d", j.id)).collect(Collectors.joining(","));
            System.out.printf("[%s], page=%d, size=%d, total=%d%n", list, i.getNumber(), i.getSize(), i.getTotalElements());
        };

        int page = 1;
        while (page < 10) {
            Page<TestDataDto> dtoPage = GroupEntityHelper.findAllWithGroup(page, 20, allGroup,
                    (pageStartOffset, pageNumber, pageSize) -> {
                        int dataSize = allData.size();
                        int pageStart = (pageNumber - 1) * pageSize + pageStartOffset;
                        List<TestDataDto> subList;
                        if (pageStart >= dataSize) {
                            subList = new ArrayList<>();
                        } else {
                            int pageEnd = Math.min(dataSize, pageStart + pageSize);
                            subList = allData.subList(pageStart, pageEnd);
                        }
                        return new PageResult<>(subList, PageRequest.of(pageNumber, pageSize), dataSize);
                    }, g -> g.stream().map(j -> new TestDataDto(j.id)).collect(Collectors.toList()));
            print.accept(dtoPage);
            page++;
        }
    }

    @AllArgsConstructor
    public static class TestGroup extends BaseEntity {
        private int id;
    }

    @AllArgsConstructor
    public static class TestData extends BaseEntity {
        private int id;
    }

    @AllArgsConstructor
    public static class TestDataDto extends BaseEntityDTO<TestData> {
        private int id;

        @Override
        public Long getId() {
            return (long) id;
        }
    }
}