package org.befun.core.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.befun.core.template.TemplateEngine;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class TemplateUtilTest {

    private static Map<String, Object> simpleParams = new HashMap<>();
    private static Map<String, Object> nestedParams = new HashMap<>();

    @BeforeAll
    public static void setUp() {
        Map<String, Object> location = new HashMap<>();
        location.put("count", 100);
        location.put("name", "hotel");

        simpleParams.put("animal", "dog");
        simpleParams.put("target", "desktop");
        simpleParams.put("地 区", "中国");

        nestedParams.put("animal", "dog");
        nestedParams.put("target", "desktop");
        nestedParams.put("location", location);
    }

    @Test
    @DisplayName("text_template")
    public void testTextTemplate() {
        String text = "The ${animal} jumped over the ${target}";

        String result = TemplateEngine.renderTextTemplate(text, simpleParams);
        assertEquals("The dog jumped over the desktop", result);
    }

    @Test
    @DisplayName("text_template_with_nested_field")
    public void testTextTemplateNested() {
        String text = "The ${animal} jumped over the ${location.name}";

        String result = TemplateEngine.renderTextTemplate(text, nestedParams);
        assertEquals("The dog jumped over the hotel", result);
    }

    @Test
    @DisplayName("text_template_simple")
    public void testTextTemplateSimple() {
        String text = "The ${animal} jumped over the ${target}";
        String result = TemplateEngine.renderTextTemplateSimple(text, simpleParams);
        assertEquals("The dog jumped over the desktop", result);
    }

    @Test
    @DisplayName("text_template_space")
    public void testTextTemplateSpace() {
        String text = "The ${地 区}";

        String result = TemplateEngine.renderTextTemplate(text, simpleParams);
        assertEquals("The 中国", result);
    }

    @Test
    @DisplayName("json_template")
    public void testJsonTemplate() {
        Map<String, Object> data = new HashMap<>();
        data.put("keywords1", "${animal}");
        data.put("keywords2", "${target}");

        Map<String, Object> result = TemplateEngine.renderJsonTemplate(data, simpleParams);
        assertEquals("dog", result.get("keywords1"));
        assertEquals("desktop", result.get("keywords2"));
    }

    @Test
    @DisplayName("json_template_with_nested_field")
    public void testJsonTemplateNestedField() {
        Map<String, Object> data = new HashMap<>();
        data.put("keywords1", "${animal}");
        data.put("keywords2", "${location.name}");

        Map<String, Object> result = TemplateEngine.renderJsonTemplate(data, nestedParams);
        assertEquals("dog", result.get("keywords1"));
        assertEquals("hotel", result.get("keywords2"));
    }
}
