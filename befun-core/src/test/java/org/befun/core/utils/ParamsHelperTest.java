package org.befun.core.utils;

import ch.qos.logback.classic.util.LogbackMDCAdapter;
import lombok.Data;
import org.befun.core.constant.SearchOperator;
import org.befun.core.dto.query.ResourceQueryRequestDto;
import org.befun.core.dto.query.SearchCriteria;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@AutoConfigureMockMvc
@SpringBootTest
public class ParamsHelperTest {

    private static HashMap<String, Object> simpleParams = new HashMap<>();

    @BeforeAll
    public static void setUp() {
        ArrayList<Object> criteriaList = new ArrayList<>();
        SearchCriteria nameSearchCriteria = new SearchCriteria("name", "Jerry");
        criteriaList.add(nameSearchCriteria);

        simpleParams.put("_limit", 10);
        simpleParams.put("_sort", "name_asc");
        simpleParams.put("criteriaList", criteriaList);

    }

//    @Test
//    @DisplayName("parse_simple_query")
//    public void parseSimpleQuery() {
//        ResourceQueryRequestDto dto = ParamsHelper.parseQueryParams(simpleParams, ResourceQueryRequestDto.class);
//        assertEquals(10, dto.getLimit());
//
//        assertNotNull(dto.getSort().getOrderFor("name"), "missing name param");
//        assertEquals(Sort.Direction.ASC, dto.getSort().getOrderFor("name").getDirection());
//
//        Optional<SearchCriteria> name = dto.getCriteriaList().stream().findFirst();
//        assertTrue(name.isPresent());
//
//
//        dto.getCriteriaList().forEach(
//                c-> {
//                    ((ArrayList) c.getValue()).stream().forEach(
//                            x->{
//                                SearchCriteria y = (SearchCriteria) x;
//                                assertEquals("name", y.getKey());
//                                assertEquals("Jerry",y.getValue());
//                                assertEquals(SearchOperator.EQUAL, y.getOperator());
//                            }
//                    );
//                }
//        );
//    }
}
