package org.befun.core.collection;

import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.SequenceAware;
import org.befun.core.entity.TreeAware;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ForestTest {

    class SampleNode extends BaseEntityDTO implements TreeAware, SequenceAware {
        private Long id;
        private Long parentId;
        private Integer sequence;

        public SampleNode(Long id, Long parentId, Integer sequence) {
            this.id = id;
            this.parentId = parentId;
            this.sequence = sequence;
        }

        @Override
        public Long getId() {
            return id;
        }

        @Override
        public Long getParentId() {
            return parentId;
        }

        @Override
        public Integer getSequence() {
            return this.sequence;
        }
    }


    @Test
    public void buildForest() {
        SampleNode n1 = new SampleNode(1L, null, 1);
        SampleNode n2 = new SampleNode(2L, null, 2);
        SampleNode n3 = new SampleNode(3L, 1L, 3);
        SampleNode n4 = new SampleNode(4L, 1L, 4);

        List<SampleNode> items = Arrays.asList(
                n2,
                n1,
                n4,
                n3
        );
        Forest<SampleNode> forest = new Forest(items);
        Set<TreeNode> roots = forest.getNodes();

        Assertions.assertEquals(2, roots.size(), "root have two elements");
    }

}
