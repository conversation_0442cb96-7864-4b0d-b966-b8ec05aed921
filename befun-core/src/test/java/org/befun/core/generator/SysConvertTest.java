package org.befun.core.generator;

import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;

import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

public class SysConvertTest {

    @Test
    public void test() {
        test(1);
//        IntStream.range(0, 1000).forEach(this::test);
//        IntStream.range(0, 1000).forEach(i -> {
//            test(RandomUtils.nextLong());
//        });
    }

    public void test(long i) {
//        System.out.println(i);
//        SysConvert convert = new SysConvert();
//        String x = convert.toX(i);
//        System.out.println(x);
//        long j = convert.toDecimal(x);
//        assertEquals(i, j);
//        assertEquals(convert.toX(i).replace("O", ""), convert.toX(j).replace("O", ""));
//

        System.out.println(SysConvert.toDecimal("aaa.xxx"));
        System.out.println(SysConvert.toDecimal("vXyEe6qaAGMpcSLzQs1nd7w3bPhNOFu5xBgKVDRijUmZTC4Ior8tlYk2W9JfH0"));
    }
}