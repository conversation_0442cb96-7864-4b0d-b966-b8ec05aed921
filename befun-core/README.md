# befun-core

## Entity

### BaseEntity
所有befun entity都必须直接或者间接继承自BaseEntity，实现最基础的三个字段
- id 默认使用雪花id
- createTime （注意updatable是false）
- modifyTime

```
@Id
@GenericGenerator(name = "snowflake", strategy = "org.befun.core.generator.SnowflakeGenerator" )
@GeneratedValue(generator = "snowflake")
@JsonView(ResourceViews.Basic.class)
@Column(name = "id", columnDefinition="bigint(20) COMMENT '主键'")
protected Long id;

@Temporal(TemporalType.TIMESTAMP)
@CreationTimestamp
@JsonView(ResourceViews.Basic.class)
@Column(name = "create_time", updatable = false)
public Date createTime;

@Temporal(TemporalType.TIMESTAMP)
@UpdateTimestamp
@JsonView(ResourceViews.Basic.class)
@Column(name = "modify_time")
public Date modifyTime;
```
### EnterpriseEntity
实现基于多租户，围绕企业组织的基础Entity
```
@Column(name = "org_id")
protected Long orgId;
```

### EnterpriseAware
定义基础Enterprise多租户模型接口

### RootAware
比如添加一条comment，如何更新book的最后修改时间？

- Comment中实现了RootAware接口
- Book->Comment是一对多关系
- Comment->Book是多对一关系

借助于
- RootAwareDeleteEventListener
- RootAwareFlushEventListener
- RootAwarePersistEventListener
- RootAwareUpdateEventListener
实现对Entity的监听，以及Root的检查，最终达到修改Comment能够联动修改Book的最后更新时间

```
@Entity
public class Book extends BaseEntity {
    private String title;

    @OneToMany(mappedBy = "book", fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JsonManagedReference
    private List<Comment> comments = new ArrayList<>();
}

@Entity
public class Comment extends BaseEntity implements RootAware<Book> {
    private String content;

    @ManyToOne()
    @JoinColumn(name = "book_id")
    @JsonBackReference
    private Book book;

    @Override
    public Book getRoot() {
        return book;
    }
}

// UnitTest
Date b1CreatedAt = b1.getCreateTime();
Date b1ModifiedAt = b1.getModifyTime();

b1c2.setContent("comment 22");
commentRepository.save(b1c2);

Book nb1 = bookRepository.findById(b1.getId()).get();
Assert.assertEquals(b1CreatedAt.getTime(), nb1.getCreateTime().getTime());
Assert.assertTrue(nb1.getModifyTime().after(b1ModifiedAt));
```

## 权限
Befun系统依据主流权限机制才用如下两种权限机制
- 功能权限 (TBD)
- 数据权限

## 功能权限 (TBD)
匹配模块菜单级别的数据权限控制。根据Spring Security实现基于RBAC的权限体制。

## 数据权限 (ScopePermission)
数据权限用来限定用户访问数据范围，比如用户是否可以看到别人分享的问卷，以及其他部门的客户数据。
数据权限是基于JPA的Filter实现
参考 EntityScopeStrategyType定义

- SHARE("shareFilter"),                               // 整个企业和全局共享
```
select * from book
where org_id = :orgId or org_id is null
```
- ORGANIZATION("organizationFilter"),                 // 整个企业共享，默认策略, 超管默认强制这个策略
```
select * from book
where org_id = :orgId
```
- OWNER("ownerFilter"),                               // 只有创建者才能访问 对应：ownerFilter
```
select * from book
where user_id = :userId
```
- OWNER_CORPORATION("ownerCorpFilter"),               // 只有创建者或者邀请协作才能访问
```
select * from book
where user_id = :userId or id in (
  select resource_id from resource_permission
  where user_id = :userId and type = 'BOOK'
)
```
- DEPARTMENT("departmentFilter");                     // 当前部门以及下属部门可以访问
```
select * from book
where department_id = :departmentId or department_id in (:subdepartmentIds)
```
Book Entity Example
```
@DtoClass
@EntityScopeStrategy(EntityScopeStrategyType.OWNER_CORPORATION)
public class Book extends EnterpriseOwnerEntity {
    @Column(name = "title")
    @DtoProperty(description = "title", example = "标题", queryable = true, jsonView = ResourceViews.Basic.class)
    private String title;
}
```
 

## 接口授权
- Authorization: <token>
- X-API-Key: <api key>

### Controller增加鉴权
毕方通过PreAuthorize描述鉴权, 同时支持更多的表达式功能
[参考详情Spring Expression Access Control](!https://docs.spring.io/spring-security/site/docs/4.2.x/reference/html/el-access.html)

暂时支持
- isAuthenticated 已完整登陆用户，包括用户名密码或者OpenAPI
- hasAuthority('EXECUTE_OPENAPI') 已通过OpenAPI登陆

未来会支持
- hasRole
- hasPermission

```
@ResourceInstanceAction(
    action = "open",
    path = "open",
    method = RequestMethod.GET,
    description = "open"
)
@PreAuthorize("hasAuthority('EXECUTE_OPENAPI')")
public ResourceResponseDto<Book> openApiAccess(Book book) {
    Book result = bookService.echo(book);
    return new ResourceResponseDto(result);
}

@ResourceInstanceAction(
    action = "open",
    path = "open",
    method = RequestMethod.GET,
    description = "open"
)
@PreAuthorize("isAuthenticated()")
public ResourceResponseDto<Book> openApiAccess(Book book) {
    Book result = bookService.echo(book);
    return new ResourceResponseDto(result);
}
```

## Entity EntityChangeAware
```
# step 1 define entity change event
public class FooChangeEvent extends BaseEntityChangeEvent<Foo> {
    public FooChangeEvent(Object source) {
        super(source);
    }

    public FooChangeEvent(Object source, Foo entity, EntityEventType eventType) {
        super(source, entity, eventType);
    }
}

# step 2 implement EntityChangeAware
public class Foo extends BaseEntity implements EntityChangeAware<FooChangeEvent> {

    @Column(name = "message")
    @JsonView(ResourceViews.Basic.class)
    private String message;

    @Override
    public FooChangeEvent buildChangeEvent(EntityEventType eventType) {
        FooChangeEvent event = new FooChangeEvent(this, this, eventType);
        return event;
    }
}

# step 3 listen entity change events
@Service
public class FooService {
    @EventListener(classes = { BarChangeEvent.class, FooChangeEvent.class })
    public void handleMultipleEvents(BaseEntityChangeEvent changeEvent) {
        log.info("received event {}", changeEvent.getTimestamp());
    }
}
```

## Version 0.5
- 自动生成Entity Dto，独立Base Service
- 重构Annotation Processor， ResourceAnnotationProcessor -> AnnotationProcessor
- Controller
    - 内嵌 Service，不再直接引用Entity，Repository
    - 使用DTO，不再使用Map，同时轻量化序列化过程
    - findAll 查询参数该用Formatter，统一通过DTO序列化
- Entity
    - 增加EmbeddedOne， 支持User->Profile，默认一层
    - 增加Nested Entity，避免表过度设计

TODO
- [YES] Resource Scope Permission Filter
- [TBD] Resource Permission
- Deep Embedded


    

