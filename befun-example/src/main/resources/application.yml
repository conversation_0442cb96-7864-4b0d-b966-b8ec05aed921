server:
  port: ${PORT:8081}
  servlet:
    context-path: /api/example
  error:
    whitelabel:
      enabled: false

spring:
  mvc:
    throw-exception-if-no-handler-found: true
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ${MYSQL_URL:***********************************}
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:20210819yym}
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  jpa:
    generate-ddl: false
    show-sql: true
    hibernate:
      ddl-auto: update
#    properties:
#      hibernate:
#        dialect: org.hibernate.dialect.MySQL5Dialect
  data:
    rest:
      default-media-type: application/json
  cache:
    type: redis
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:123456}
  kafka:
    bootstrap-servers: ${KAFKA_BROKER:**********:9092}

#  jackson:
#    date-format: com.fasterxml.jackson.databind.util.ISO8601DateFormat

logging:
  level:
    root: ${LOG_LEVEL:info}
    org:
      befun:
        task: info
      hibernate:
        SQL: info
#        type: trace

befun1:
  server:
    enable-open-api-filter: true
  extension_disable:
  extension:
    wechat-open:
      enable: true
      component-app-id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
      component-secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
      component-token: ${WECHAT_OPEN_TOKEN:surveyplus}
      component-aes-key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    mail:
      host: smtp.partner.outlook.cn
      port: 587
      username: <EMAIL>
      from: <EMAIL>
      password: Survey+0627
      templates:
        - name: default
          subject: 标题${name}
          enable-html: true
          content: 尊敬的用户，<h1>${name}</h1>已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！
    sms:
      vendor: aliyun
      providers:
        - name: feige
          app-id: hysj_test
          app-secret: a9dd33f910e41f5d74dbd7147
          signature: 349733
          templates:
            - name: default
              id: 116175
              signature: 349733
              content: 尊敬的用户，已经为您开通体验家XMPlus账户，快来www.xmplus.cn登陆使用，您可以轻松实现以用户为中心的旅程刻画、体验监测和行动反馈，改善您的客户体验！
        - name: aliyun
          app-id: LTAI4G5RfyxPtajMojKJPvmM
          app-secret: ******************************
          aliyun-region: cn-shenzhen
          templates:
            - name: default
              id: SMS_154950909
              signature: 阿里云短信测试
              content: 您正在使用阿里云短信测试服务，体验验证码是：${code}，如非本人操作，请忽略本短信！


befun:
  task:
    type: rabbitmq
    delay:
      enabled: true
      queue: delay
      cron: "1/10 * * * * *"
    redis-stream:
      prefix: befun.task.example
      separator: .
      group: example
      name: example
      manual-ack: true
      pull-interval-seconds: 5
      trim:
        enabled: false
        cron: "1/10 * * * * *"
    kafka:
      prefix: befun.task.example
      separator: .
      group: example
      manual-ack: false
    rabbitmq:
      prefix: befun.task.example
      separator: .
      exchange: example
      manual-ack: false

hanyi:
  common:
    ip-resolver:
      default-platform: local
      local:
        algorithm: memory
    file-storage:
      default-platform: default
      aliyun-oss:
        - platform: default
          enable-storage: true
          access-key: ${ALICLOUD_ACCESS_KEY:LTAI4G5RfyxPtajMojKJPvmM}
          secret-key: ${ALICLOUD_SECRET_KEY:******************************}
          end-point: ${ALICLOUD_OSS_ENDPOINT:https://oss-cn-shenzhen.aliyuncs.com}
          bucket-name: ${ALICLOUD_OSS_BUCKET:dev-assets-sp}
          domain: ${ALICLOUD_OSS_DOMAIN:http://dev-assets.surveyplus.cn/}
          base-path: ${ALICLOUD_OSS_BASEPATH:lite/}
