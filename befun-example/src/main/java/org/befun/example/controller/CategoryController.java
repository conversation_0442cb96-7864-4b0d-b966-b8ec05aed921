package org.befun.example.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.example.entity.Category;
import org.befun.example.repository.CategoryRepository;
import org.befun.example.service.CategoryService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "分组")
@RestController
@ResourceController(
        entityClass = Category.class,
        repositoryClass = CategoryRepository.class,
        serviceClass = CategoryService.class,
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        permission = "isAuthenticated()"
)
@RequestMapping("categories")
@PreAuthorize("isAuthenticated()")
public class CategoryController {

}
