package org.befun.example.controller;

import io.swagger.v3.oas.annotations.Hidden;
import org.befun.example.dto.BarTaskDetailDto;
import org.befun.example.task.BookBarTaskExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Hidden
@RequestMapping("task")
@RestController
public class TaskController {

    @Autowired
    private BookBarTaskExecutor bookBarTaskExecutor;

    @GetMapping("add")
    public String test1(@RequestParam(name = "name") String name) {
        bookBarTaskExecutor.performAsync(BarTaskDetailDto.builder().name(name).build());
        return "success";
    }

}
