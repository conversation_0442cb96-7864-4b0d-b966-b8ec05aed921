package org.befun.example.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.example.entity.Room;
import org.befun.example.repository.RoomRepository;
import org.befun.example.service.RoomService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "房间")
@RestController
@ResourceController(
        entityClass = Room.class,
        repositoryClass = RoomRepository.class,
        serviceClass = RoomService.class,
        permission = "isAuthenticated()"
)
@RequestMapping("/rooms")
@PreAuthorize("isAuthenticated()")
public class RoomController extends BaseController<RoomService> {

}
