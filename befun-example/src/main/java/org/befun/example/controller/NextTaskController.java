//package org.befun.example.controller;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import io.swagger.v3.oas.annotations.Hidden;
//import org.befun.example.task.NextTaskExecutor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Optional;
//
//@Hidden
//@RequestMapping("next-task/{taskId}")
//@RestController
//public class NextTaskController {
//
//    @Autowired
//    private NextTaskExecutor nextTaskExecutor;
//    @Autowired
//    private ObjectMapper objectMapper;
//
//
//    @GetMapping("run")
//    public String test1(@PathVariable(name = "taskId") String taskId) {
//        nextTaskExecutor.reset(taskId);
//        nextTaskExecutor.updateTotal(taskId,11);
//        nextTaskExecutor.performNextId(taskId, nextTaskExecutor.testDto(0));
//        return "success";
//    }
//
//    @GetMapping("dead/run")
//    public String deadRun(@PathVariable(name = "taskId")String taskId) {
//        nextTaskExecutor.consumerDeadRecords(taskId);
//        return "success";
//    }
//
//    @GetMapping("dead/prev")
//    public String deadPrev(@PathVariable(name = "taskId")String taskId) {
//        return Optional.ofNullable(nextTaskExecutor.getDeadRecords(taskId, 0, -1))
//                .map(i -> {
//                    try {
//                        return objectMapper.writeValueAsString(i);
//
//                    } catch (JsonProcessingException e) {
//                        e.printStackTrace();
//                    }
//                    return null;
//                }).orElse(null);
//    }
//
//    @GetMapping("progress")
//    public String progress(@PathVariable(name = "taskId")String taskId) {
//        try {
//            return objectMapper.writeValueAsString(nextTaskExecutor.getProgress(taskId));
//        } catch (JsonProcessingException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    @GetMapping("cancel")
//    public String cancel(@PathVariable(name = "taskId")String taskId) {
//        nextTaskExecutor.cancel(taskId);
//        return "success";
//    }
//
//
//}
