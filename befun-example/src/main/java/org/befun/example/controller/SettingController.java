package org.befun.example.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.example.entity.Setting;
import org.befun.example.repository.SettingRepository;
import org.befun.example.service.SettingService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "设置")
@RestController
@ResourceController(
        entityClass = Setting.class,
        repositoryClass = SettingRepository.class,
        serviceClass = SettingService.class,
        collectionType = ResourceCollectionType.SINGLE_TYPE
)
@RequestMapping("/settings")
public class SettingController extends BaseController<SettingService> {
}
