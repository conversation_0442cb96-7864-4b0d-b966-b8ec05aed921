package org.befun.example.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Tag(name = "阿里云短信测试接口")
@RestController
@RequestMapping("/sms/aliyun")
public class SmsController {

    @Autowired
    private SmsService smsService;

    @PostMapping("send")
    public boolean send(String mobile, String code) {
        return smsService.sendMessageByTemplate("default", mobile, Map.of("code", code));
    }
}
