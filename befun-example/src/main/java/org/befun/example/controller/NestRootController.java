package org.befun.example.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceDeepEmbeddedMany;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.example.dto.BookCreateDto;
import org.befun.example.dto.BookQueryDto;
import org.befun.example.entity.*;
import org.befun.example.repository.BookRepository;
import org.befun.example.repository.NestDeepRepository;
import org.befun.example.repository.NestEmbeddedRepository;
import org.befun.example.repository.NestRootRepository;
import org.befun.example.service.BookService;
import org.befun.example.service.NestRootService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "三级嵌套-第一层")
@RestController
@RequestMapping("nest-roots")
@ResourceController(
        entityClass = NestRoot.class,
        repositoryClass = NestRootRepository.class,
        serviceClass = NestRootService.class
)
@ResourceEmbeddedMany(
        fieldNameInRoot = "embeddeds",
        path = "nest-embeddeds",
        entityClass = NestEmbedded.class,
        repositoryClass = NestEmbeddedRepository.class,
        dtoClass = NestEmbeddedDto.class,
        docTag = "三级嵌套-第二层",
        deepEmbeddedMany = {
                @ResourceDeepEmbeddedMany(
                        fieldNameInEmbedded = "deeps",
                        path = "nest-deeps",
                        entityClass = NestDeep.class,
                        repositoryClass = NestDeepRepository.class,
                        dtoClass = NestDeepDto.class,
                        docTag = "三级嵌套-第三层"
                )
        }
)
public class NestRootController {
}
