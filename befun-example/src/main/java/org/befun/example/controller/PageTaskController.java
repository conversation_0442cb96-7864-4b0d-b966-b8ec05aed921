//package org.befun.example.controller;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import io.swagger.v3.oas.annotations.Hidden;
//import org.befun.example.dto.PageTaskCoordinatorDto;
//import org.befun.example.task.PageTaskExecutor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Optional;
//
//@Hidden
//@RequestMapping("page-task/{taskId}")
//@RestController
//public class PageTaskController {
//
//    @Autowired
//    private PageTaskExecutor pageTaskExecutor;
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @GetMapping("run")
//    public String test1(@PathVariable(name = "taskId")String taskId) {
//        pageTaskExecutor.performCoordinator(new PageTaskCoordinatorDto(taskId));
//        return "success";
//    }
//
//    @GetMapping("dead/run")
//    public String deadRun(@PathVariable(name = "taskId")String taskId) {
//        pageTaskExecutor.consumerDeadRecords(taskId);
//        return "success";
//    }
//
//    @GetMapping("dead/prev")
//    public String deadPrev(@PathVariable(name = "taskId")String taskId) {
//        return Optional.ofNullable(pageTaskExecutor.getDeadRecords(taskId, 0, -1))
//                .map(i -> {
//                    try {
//                        return objectMapper.writeValueAsString(i);
//
//                    } catch (JsonProcessingException e) {
//                        e.printStackTrace();
//                    }
//                    return null;
//                }).orElse(null);
//    }
//
//    @GetMapping("progress")
//    public String progress(@PathVariable(name = "taskId")String taskId) {
//        try {
//            return objectMapper.writeValueAsString(pageTaskExecutor.getProgress(taskId));
//        } catch (JsonProcessingException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    @GetMapping("cancel")
//    public String cancel(@PathVariable(name = "taskId")String taskId) {
//        pageTaskExecutor.cancel(taskId);
//        return "success";
//    }
//
//
//}
