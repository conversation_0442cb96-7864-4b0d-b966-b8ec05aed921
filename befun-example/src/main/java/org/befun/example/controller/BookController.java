package org.befun.example.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.*;
import org.befun.example.dto.BookEchoRequestDto;
import org.befun.example.dto.BookEchoResponseDto;
import org.befun.example.dto.BookRemarkRequestDto;
import org.befun.example.entity.*;
import org.befun.example.repository.ArticleRepository;
import org.befun.example.repository.BookRepository;
import org.befun.example.repository.CommentRepository;
import org.befun.example.service.BookService;
import org.befun.example.service.BookStatService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;


@Tag(name = "书籍")
@RestController
@ResourceController(
        entityClass = Book.class,
        repositoryClass = BookRepository.class,
        serviceClass = BookService.class,
        permission = "isAuthenticated()",
        docCrud = "书籍"
)
@PreAuthorize("isAuthenticated()")
@ResourceEmbeddedOne(
        entityClass = BookStat.class,
        repositoryClass = BookStatService.class,
        dtoClass = BookStatDto.class,
        fieldNameInRoot = "bookStat",
        path = "bookStat",
        docTag = "书籍-统计",
        docCrud = "统计"
)
@ResourceEmbeddedMany(
        entityClass = Article.class,
        repositoryClass = ArticleRepository.class,
        dtoClass = ArticleDto.class,
        fieldNameInRoot = "articles",
        path = "articles",
        docTag = "书籍-文章",
        docCrud = "文章"
)
@ResourceEmbeddedMany(
        entityClass = Comment.class,
        repositoryClass = CommentRepository.class,
        dtoClass = CommentDto.class,
        fieldNameInRoot = "comments",
        path = "comments",
        docTag = "书籍-评论",
        docCrud = "评论"
)
@RequestMapping("/books")
public class BookController extends BaseController<BookService> {

    @ResourceInstanceAction(
            action = "check",
            path = "check",
            method = RequestMethod.GET
    )
    public ResourceResponseDto<BookEchoResponseDto> check(@Valid @NotNull @RequestBody BookDto book) {
        return new ResourceResponseDto(new BookEchoResponseDto("echo " + book.getTitle()));
    }

    @ResourceInstanceAction(
            action = "remark",
            path = "remark",
            method = RequestMethod.POST
    )
    public ResourceResponseDto<BookDto> remark(BookDto book, @RequestBody @NotNull BookRemarkRequestDto requestDto) {
//        book.setNote(requestDto.getNote());
        service.save(book);
        return new ResourceResponseDto(book);
    }

    @ResourceCollectionAction(
            action = "echo",
            path = "echo",
            method = RequestMethod.GET
    )
    public ResourceResponseDto<BookEchoResponseDto> echo(@RequestBody @NotNull BookEchoRequestDto dto) {
        return new ResourceResponseDto(new BookEchoResponseDto("echo " + dto.getMessage()));
    }
}
