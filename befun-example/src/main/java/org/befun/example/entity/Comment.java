package org.befun.example.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "comment")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class Comment extends BaseEntity {

    @Column(name = "content")
    @DtoProperty(description = "content", example = "内容", jsonView = ResourceViews.Basic.class)
    private String content;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "book_id")
    private Book book;
}