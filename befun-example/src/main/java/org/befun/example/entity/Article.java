package org.befun.example.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "article")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class Article extends BaseEntity {
    @Column(name = "title")
    @DtoProperty(description = "title", example = "标题", queryable = true, jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "description")
    @DtoProperty(description = "description", example = "描述", jsonView = ResourceViews.Basic.class)
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "book_id")
    private Book book;
}