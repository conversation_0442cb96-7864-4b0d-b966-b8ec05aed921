package org.befun.example.entity;


import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.List;

@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "nest_embedded")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class NestEmbedded extends BaseEntity {

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String name;

    @ManyToOne
    private NestRoot root;

    @OneToMany(mappedBy = "embedded",fetch = FetchType.EAGER)
    @DtoProperty(type = NestEmbeddedDto.class,jsonView = ResourceViews.Basic.class)
    private List<NestDeep> deeps;
}
