package org.befun.example.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.annotation.SoftDelete;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "setting")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
@SoftDelete
public class Setting extends BaseEntity {

    @DtoProperty
    private String name;
    @DtoProperty
    private Boolean deleted;
}
