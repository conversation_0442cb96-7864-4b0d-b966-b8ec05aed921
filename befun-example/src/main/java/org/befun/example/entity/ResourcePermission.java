//package org.befun.example.entity;
//
//import lombok.*;
//import org.befun.core.entity.EnterpriseEntity;
//
//import javax.persistence.*;
//
//@Builder
//@Entity
//@Getter
//@Setter
//@Table(name = "resource_permission")
//@NoArgsConstructor
//@AllArgsConstructor
//public class ResourcePermission extends EnterpriseEntity {
//
//    @Column(name = "user_id")
//    private Long userId;
//
//    @Column(name = "role_id")
//    private Long roleId;
//
//    @Column(name = "department_id")
//    private Long departmentId;
//
//    @Column(name = "resource_id")
//    private Long resourceId;
//
//    @Column(name = "resource_type")
//    private String resourceType;
//
//    @Column(name = "relation_type")
//    private String relationType;
//
//}