package org.befun.example.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.converter.LongCommaListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.view.ResourceViews;
import org.befun.example.constant.BookType;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "book")
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "UPDATE book SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@DtoClass()
@EntityScopeStrategy(EntityScopeStrategyType.OWNER_CORPORATION)
public class Book extends EnterpriseOwnerEntity {
    @Column(name = "title")
    @DtoProperty(description = "标题", queryable = true, jsonView = ResourceViews.Basic.class)
    private String title;

    @Column(name = "description")
    @DtoProperty(description = "描述", jsonView = ResourceViews.Basic.class)
    private String description;

    @Enumerated
    @DtoProperty(description = "类型", example = "")
    private BookType bookType = BookType.NOVEL;

    @Column(name = "tags", columnDefinition = "varchar(200)")
    @Convert(converter = LongCommaListConverter.class)
    @DtoProperty(description = "tags", example = "tags")
    private List<Long> tags = new ArrayList<>();

    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

    @OneToMany(mappedBy = "book", fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @DtoProperty(description = "文章", type = ArticleDto.class, jsonView = ResourceViews.Basic.class)
    private List<Article> articles = new ArrayList<>();

    @OneToMany(mappedBy = "book", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @DtoProperty(description = "评论", type = CommentDto.class, jsonView = ResourceViews.Basic.class)
    private List<Comment> comments = new ArrayList<>();

    @Getter
    @Setter
    public static class BookMeta implements Serializable {
        @JsonView(ResourceViews.Basic.class)
        private String name;
        @JsonView(ResourceViews.Basic.class)
        private String content;
    }

    @Type(type = JsonColumn.TYPE)
    @DtoProperty(description = "meta")
    private BookMeta meta = new BookMeta();

    @Type(type = JsonColumn.TYPE)
    @DtoProperty(description = "metas")
    private List<BookMeta> metas;

    @Type(type = JsonColumn.TYPE)
    @DtoProperty(description = "labels")
    private List<String> labels;

    @Type(type = JsonColumn.TYPE)
    @DtoProperty(description = "config")
    private Map<String, BookMeta> config;

    @OneToOne
    @JoinColumn(name = "category_id", referencedColumnName = "id")
    @DtoProperty(description = "meta", example = "meta", type = CategoryDto.class)
    private Category category = null;

    @Column(columnDefinition = "varchar(100)")
    @DtoProperty(description = "note", example = "note")
    private String note = "";

    @OneToOne(mappedBy = "book")
    @DtoProperty(description = "书籍统计", type = BookStatDto.class)
    private BookStat bookStat;
}