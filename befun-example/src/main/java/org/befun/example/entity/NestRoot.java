package org.befun.example.entity;


import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.List;

@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "nest_root")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class NestRoot extends BaseEntity {

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String name;

    @OneToMany(mappedBy = "root",fetch = FetchType.EAGER)
    @DtoProperty(type = NestEmbeddedDto.class, jsonView = ResourceViews.Basic.class)
    private List<NestEmbedded> embeddeds;

}
