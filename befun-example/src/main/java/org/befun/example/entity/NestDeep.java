package org.befun.example.entity;


import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "nest_deep")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class NestDeep extends BaseEntity {

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String name;

    @ManyToOne
    private NestEmbedded embedded;
}
