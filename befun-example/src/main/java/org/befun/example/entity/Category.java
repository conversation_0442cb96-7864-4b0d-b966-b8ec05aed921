package org.befun.example.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "category")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class Category extends BaseEntity {
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "name")
    @DtoProperty
    private String name;
}