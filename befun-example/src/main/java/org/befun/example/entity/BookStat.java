package org.befun.example.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(name = "book_stat")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass()
public class BookStat extends BaseEntity {

    @OneToOne
    @JoinColumn(name = "book_id")
    private Book book;

    @Column(name = "count_word")
    @DtoProperty(description = "字数", queryable = true, jsonView = ResourceViews.Basic.class)
    private Integer countWord;

    @Column(name = "count_chapter")
    @DtoProperty(description = "章节数", jsonView = ResourceViews.Basic.class)
    private String countChapter;

    @Column(name = "count_view")
    @DtoProperty(description = "浏览数", jsonView = ResourceViews.Basic.class)
    private String countView;

}