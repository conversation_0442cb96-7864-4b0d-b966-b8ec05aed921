package org.befun.example.entity;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseTreeEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "folder")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class Folder extends EnterpriseTreeEntity {
    @Column(name = "name")
    @DtoProperty(description = "name", example = "名称", queryable = true, jsonView = ResourceViews.Basic.class)
    private String name;
}