package org.befun.example.entity;

import lombok.*;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.befun.example.dto.ext.RoomExtDto;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Builder(toBuilder = true)
@Table(name = "room")
@AllArgsConstructor
@NoArgsConstructor
@Where(clause = "del=0")
@SoftDelete(property = "del", propertyType = Integer.class, deleteValue = "1")
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER_CORPORATION, resource = "ROOM2")
@DtoClass(superClass = RoomExtDto.class)
public class Room extends EnterpriseEntity {

    @Column(name = "user_id")
    private Long userId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String name;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private Integer floor;

    private Integer del = 0;
}
