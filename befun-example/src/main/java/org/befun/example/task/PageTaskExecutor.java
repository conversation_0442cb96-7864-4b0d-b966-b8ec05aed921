//package org.befun.example.task;
//
//import lombok.extern.slf4j.Slf4j;
//import org.befun.core.exception.BadRequestException;
//import org.befun.example.dto.NextTaskDetailDto;
//import org.befun.example.dto.PageTaskCoordinatorDto;
//import org.befun.example.dto.PageTaskDetailDto;
//import org.befun.task.NextIdTaskExecutor;
//import org.befun.task.PageableTaskExecutor;
//import org.befun.task.annotation.Task;
//import org.befun.task.annotation.TaskRetryable;
//import org.befun.task.dto.TaskContextDto;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.TimeUnit;
//
//@Slf4j
//@TaskRetryable(maxAttempts = 3, intervals = {5}, intervalUnit = TimeUnit.SECONDS)
//@Task("test-page")
//@Component
//public class PageTaskExecutor extends PageableTaskExecutor<PageTaskDetailDto> {
//
//    @Override
//    public <C> void runCoordinator(C coordinatorData) {
//        PageTaskCoordinatorDto dto = (PageTaskCoordinatorDto) coordinatorData;
//        reset(dto.getTaskId());
//        updateTotal(dto.getTaskId(), 1001);
//        performPage(dto.getTaskId(), 1001, (p, s) -> testDto(p > 9 ? 1 : 100));
//    }
//
//    @Override
//    public void run(PageTaskDetailDto detailDto, TaskContextDto context) {
//        if (detailDto.getPage() == 10) {
//            throw new RuntimeException();
//        }
//        try {
//            Thread.sleep(2000L);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        log.info("run taskId = {}, page = {}, size = {}, count = {}", detailDto.getTaskId(), detailDto.getPage(), detailDto.getSize(), detailDto.getCount());
//    }
//
//    @Override
//    public void runDead(PageTaskDetailDto detailDto, TaskContextDto context) {
//        log.info("dead taskId = {}, page = {}, size = {}, count = {}", detailDto.getTaskId(), detailDto.getPage(), detailDto.getSize(), detailDto.getCount());
//    }
//
//    public PageTaskDetailDto testDto(int count) {
//        PageTaskDetailDto dto = new PageTaskDetailDto();
//        dto.setTest("test");
//        dto.setCount(count);
//        return dto;
//    }
//
//
//}
