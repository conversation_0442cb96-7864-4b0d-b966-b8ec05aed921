//package org.befun.example.task;
//
//import lombok.extern.slf4j.Slf4j;
//import org.befun.core.exception.BadRequestException;
//import org.befun.example.dto.NextTaskDetailDto;
//import org.befun.task.NextIdTaskExecutor;
//import org.befun.task.annotation.Task;
//import org.befun.task.annotation.TaskRetryable;
//import org.befun.task.dto.TaskContextDto;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.TimeUnit;
//
//@Slf4j
//@TaskRetryable(intervals = {3, 5}, intervalUnit = TimeUnit.SECONDS)
//@Task("test-next")
//@Component
//public class NextTaskExecutor extends NextIdTaskExecutor<NextTaskDetailDto> {
//
//    @Override
//    public NextTaskDetailDto runAndReturnNextData(NextTaskDetailDto detailDto, TaskContextDto context) {
//        if (detailDto.getStep() >= 10) {
//            throw new BadRequestException("test");
//        } else {
//            try {
//                if (detailDto.getStep() / 2 == 0) {
//                    Thread.sleep(2000L);
//                }
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            log.info("success taskId= {}, step = {}", detailDto.getTaskId(), detailDto.getStep());
//            return testDto(detailDto.getStep() + 1);
//        }
//    }
//
//    @Override
//    public void runDead(NextTaskDetailDto detailDto, TaskContextDto context) {
//        log.info("dead taskId= {}, step = {}", detailDto.getTaskId(), detailDto.getStep());
//    }
//
//    public NextTaskDetailDto testDto(int step) {
//        NextTaskDetailDto dto = new NextTaskDetailDto();
//        dto.setTest("test");
//        dto.setStep(step);
//        return dto;
//    }
//}
