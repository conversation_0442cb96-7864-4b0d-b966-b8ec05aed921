package org.befun.example.task;

import lombok.extern.slf4j.Slf4j;
import org.befun.example.dto.BarTaskDetailDto;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.annotation.TaskRetryable;
import org.befun.task.dto.TaskContextDto;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

@Slf4j
@Task("bar")
@TaskRetryable
@Component
public class BookBarTaskExecutor extends BaseTaskExecutor<BarTaskDetailDto> {

    public void run(BarTaskDetailDto detailDto, TaskContextDto context) {
        log.info("bar task started {}", detailDto.getName());
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.info("bar task finished {}", detailDto.getName());
    }

//    @KafkaListener(topics = "befun.task.example.bar", groupId = "example")
//    public void test(ConsumerRecord<String, String> data, Acknowledgment acknowledgment) {
//        log.info("kafka message: {}", data.value());
//        acknowledgment.acknowledge();
//    }

//    @RabbitListener(queues = "befun.task.example.bar")
//    public void test(String message) {
//        log.info("rabbit message: {}", message);
//    }

}
