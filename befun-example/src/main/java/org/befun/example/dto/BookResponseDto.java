package org.befun.example.dto;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.example.entity.Book;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class BookResponseDto extends BaseEntityDTO<Book> {

    private Long id;

    private long countArticle;

    private String authorName;

    private String title;
}