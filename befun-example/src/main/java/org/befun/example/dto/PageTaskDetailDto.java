//package org.befun.example.dto;
//
//import lombok.Getter;
//import lombok.Setter;
//import org.befun.task.dto.NextIdTaskDetailDto;
//import org.befun.task.dto.PageableTaskDetailDto;
//
//@Getter
//@Setter
//public class PageTaskDetailDto extends PageableTaskDetailDto {
//    private String test;
//    private int count;
//
//    @Override
//    public int countComplete() {
//        return count;
//    }
//}
