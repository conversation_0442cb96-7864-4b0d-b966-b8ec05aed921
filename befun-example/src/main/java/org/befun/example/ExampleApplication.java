package org.befun.example;

import cn.hanyi.common.file.storage.FileStorageProperties;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.befun.extension.XPackAutoConfiguration;
import org.befun.task.TaskAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(
        scanBasePackages = {"org.befun.core", "org.befun.example"},
        exclude = {SecurityAutoConfiguration.class, ErrorMvcAutoConfiguration.class})
@EnableAsync
@EnableCaching
@EnableScheduling
@EnableConfigurationProperties(FileStorageProperties.class)
public class ExampleApplication {

    @Configuration
    @EntityScan({
            "org.befun.example.entity",
            "org.befun.core.entity",
            TaskAutoConfiguration.PACKAGE_ENTITY,
            XPackAutoConfiguration.PACKAGE_ENTITY
    }
    )
    @EnableJpaRepositories(
            basePackages = {
                    "org.befun.example.repository",
                    "org.befun.core.repo",
                    TaskAutoConfiguration.PACKAGE_REPOSITORY,
                    XPackAutoConfiguration.PACKAGE_REPOSITORY
            }, repositoryBaseClass = BaseRepositoryImpl.class)
    public class BaseJPAConfig {
    }

    public static void main(String[] args) {
        SpringApplication.run(ExampleApplication.class, args);
    }
}