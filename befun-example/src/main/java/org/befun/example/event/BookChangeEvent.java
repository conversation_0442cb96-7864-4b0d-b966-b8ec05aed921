package org.befun.example.event;

import org.befun.core.constant.EntityEventType;
import org.befun.core.hibernate.BaseEntityChangeEvent;
import org.befun.example.entity.Book;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class BookChangeEvent extends BaseEntityChangeEvent<Book> {
    public BookChangeEvent(Object source) {
        super(source);
    }
    public BookChangeEvent(Object source, Book entity, EntityEventType eventType) {
        super(source, entity, eventType);
    }
}
