package org.befun.example.repository;

import org.befun.core.repository.ResourceRepository;
import org.befun.example.entity.Article;
import org.befun.example.entity.Book;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Repository
public interface ArticleRepository extends ResourceRepository<Article, Long> {
    List<Article> findAllByBook(Book book);

}
