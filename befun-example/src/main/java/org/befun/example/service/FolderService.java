package org.befun.example.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.TreeService;
import org.befun.example.entity.Folder;
import org.befun.example.entity.FolderDto;
import org.befun.example.repository.FolderRepository;
import org.springframework.stereotype.Service;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FolderService extends TreeService<Folder, FolderDto, FolderRepository> {
}
