package org.befun.example.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.entity.BaseEntity;
import org.befun.core.service.BaseService;
import org.befun.example.entity.Book;
import org.befun.example.entity.BookDto;
import org.befun.example.entity.BookStat;
import org.befun.example.entity.BookStatDto;
import org.befun.example.repository.ArticleRepository;
import org.befun.example.repository.BookRepository;
import org.befun.example.repository.BookStatRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BookStatService extends BaseService<BookStat, BookStatDto, BookStatRepository> {


}
