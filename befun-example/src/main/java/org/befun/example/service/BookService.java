package org.befun.example.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.core.entity.BaseEntity;
import org.befun.core.service.BaseService;
import org.befun.example.entity.Book;
import org.befun.example.entity.BookDto;
import org.befun.example.repository.ArticleRepository;
import org.befun.example.repository.BookRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BookService extends BaseService<Book, BookDto, BookRepository> {

    @Autowired
    private ArticleRepository articleRepository;

    @Override
    public void afterMapToDto(List<Book> entityList, List<BookDto> dtoList) {
        List<Long> ids = entityList.stream().map(BaseEntity::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            Map<Long/*bookId*/, Long/*count*/> countMap = new HashMap<>();
            Map<Long/*bookId*/, String/*authorName*/> authorNameMap = new HashMap<>();
            dtoList.forEach(i -> {
//                i.setCountArticle(countMap.getOrDefault(i.getId(), 0L));
//                i.setAuthorName(authorNameMap.get(i.getId()));
            });
        }
    }

    /**
     * echo book
     *
     * @param book
     */
    public Book echo(Book book) {
        log.info("access echo book {}", book.getId());
        return book;
    }

    /**
     * ping book
     */
    public long count() {
        log.info("access count book {}");
        return repository.count();
    }

}
