package org.befun.example;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Knife4jConfig {

    @Bean
    public GroupedOpenApi doc() {
        String[] packagedToMatch = {"org.befun.example"};
        return GroupedOpenApi.builder()
                .group("example")
                .pathsToMatch("/**")
                .packagesToScan(packagedToMatch).build();
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("example api")
                        .version("1.7.0")
                        .description("Knife4j集成springdoc-openapi示例")
                        .termsOfService("http://127.0.0.1:8081/api/example/swagger-ui/index.html"));
    }
}
