package org.befun.example.utilis;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import com.fasterxml.jackson.databind.node.TextNode;
import org.springframework.data.domain.Sort;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SortDeserializer extends JsonDeserializer<Sort> {
    @Override
    public Sort deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        TextNode node = jp.getCodec().readTree(jp);
        String val = node.toString();
        if (!val.isEmpty()){
            val = val.replace("\"", "");
            List<String> ops = Arrays.asList(val.split(","));
            Sort.Direction direction = Sort.Direction.valueOf(ops.size() > 1 ? ops.get(1).toUpperCase() : "ASC");
            return Sort.by(direction, ops.get(0));
        }
        return null;
    }
}