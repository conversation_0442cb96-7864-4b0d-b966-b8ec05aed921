package org.befun.example.controller;

import org.befun.example.BaseTest;
import org.befun.example.TestRedisConfiguration;
import org.befun.example.entity.*;
import org.befun.example.repository.NestDeepRepository;
import org.befun.example.repository.NestEmbeddedRepository;
import org.befun.example.repository.NestRootRepository;
import org.befun.example.repository.SettingRepository;
import org.befun.example.service.SettingService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SettingControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected SettingService settingService;

    @Test
    public void testSetting() throws Exception {

        // create
        mvc.perform(post("/settings")
                        .content("{\"name\":\"1\",\"deleted\":false}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber());
        SettingDto setting = settingService.findAll();
        Assertions.assertNotNull(setting);

        mvc.perform(delete("/settings/"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        setting = settingService.findAll();
        Assertions.assertNotNull(setting);
        Assertions.assertEquals(setting.getDeleted(), true);
    }
}
