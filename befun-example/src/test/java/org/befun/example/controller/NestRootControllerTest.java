package org.befun.example.controller;

import org.befun.example.BaseTest;
import org.befun.example.TestRedisConfiguration;
import org.befun.example.entity.NestDeep;
import org.befun.example.entity.NestEmbedded;
import org.befun.example.entity.NestRoot;
import org.befun.example.repository.NestDeepRepository;
import org.befun.example.repository.NestEmbeddedRepository;
import org.befun.example.repository.NestRootRepository;
import org.befun.example.service.NestRootService;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class NestRootControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected NestRootRepository nestRootRepository;

    @Autowired
    protected NestEmbeddedRepository nestEmbeddedRepository;

    @Autowired
    protected NestDeepRepository nestDeepRepository;

    @Test
    public void testCrud() throws Exception {

        // create
        mvc.perform(post("/nest-roots")
                        .content("{\"name\":\"1\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber());
        NestRoot root = getRoot();

        // update
        mvc.perform(put("/nest-roots/" + root.getId())
                        .content("{\"name\":\"11\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber())
                .andExpect(jsonPath("$.data.name").value("11"));

        // create
        mvc.perform(post("/nest-roots/" + root.getId() + "/nest-embeddeds")
                        .content("{\"name\":\"2\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber());

        NestEmbedded embedded = getEmbedded();

        // update
        mvc.perform(put("/nest-roots/" + root.getId() + "/nest-embeddeds/" + embedded.getId())
                        .content("{\"name\":\"22\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber())
                .andExpect(jsonPath("$.data.name").value("22"));

        // create
        mvc.perform(post("/nest-roots/" + root.getId() + "/nest-embeddeds/" + embedded.getId() + "/nest-deeps")
                        .content("{\"name\":\"3\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber());

        NestDeep deep = getDeep();

        // update
        mvc.perform(put("/nest-roots/" + root.getId() + "/nest-embeddeds/" + embedded.getId() + "/nest-deeps/" + deep.getId())
                        .content("{\"name\":\"33\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber())
                .andExpect(jsonPath("$.data.name").value("33"));

        mvc.perform(delete("/nest-roots/" + root.getId() + "/nest-embeddeds/" + embedded.getId() + "/nest-deeps/" + deep.getId()))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
        noDeep();

        mvc.perform(delete("/nest-roots/" + root.getId() + "/nest-embeddeds/" + embedded.getId()))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
        noEmbedded();

        mvc.perform(delete("/nest-roots/" + root.getId()))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
        noRoot();

    }

    private NestRoot getRoot() {
        List<NestRoot> roots = nestRootRepository.findAll();
        Assertions.assertEquals(1, roots.size());
        return roots.get(0);
    }

    private NestEmbedded getEmbedded() {
        List<NestEmbedded> embeddeds = getRoot().getEmbeddeds();
        Assertions.assertEquals(1, embeddeds.size());
        return embeddeds.get(0);
    }

    private NestDeep getDeep() {
        List<NestDeep> deeps = getEmbedded().getDeeps();
        Assertions.assertEquals(1, deeps.size());
        return deeps.get(0);
    }

    private void noDeep() {
        List<NestDeep> deeps = getEmbedded().getDeeps();
        Assertions.assertEquals(0, deeps == null ? 0 : deeps.size());
    }

    private void noEmbedded() {
        List<NestEmbedded> embeddeds = getRoot().getEmbeddeds();
        Assertions.assertEquals(0, embeddeds == null ? 0 : embeddeds.size());
    }

    private void noRoot() {
        List<NestRoot> roots = nestRootRepository.findAll();
        Assertions.assertEquals(0, roots.size());
    }

}
