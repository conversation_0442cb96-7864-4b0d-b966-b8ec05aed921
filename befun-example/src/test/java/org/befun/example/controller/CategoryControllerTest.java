package org.befun.example.controller;

import com.jayway.jsonpath.JsonPath;
import org.befun.core.constant.ErrorCode;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.utils.JsonHelper;
import org.befun.example.BaseTest;
import org.befun.example.TestRedisConfiguration;
import org.befun.example.entity.*;
import org.junit.Assert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.util.JsonPathExpectationsHelper;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.result.JsonPathResultMatchers;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CategoryControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Test
    public void crud() throws Exception {
        String path = "/categories";

        // create
        String r = mvc.perform(post(path)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content("{\"name\":\"1\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber())
                .andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
        Long id1 = JsonPath.parse(r).read("$.data.id", Long.class);

        // create
        r = mvc.perform(post(path)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content("{\"name\":\"2\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber())
                .andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
        Long id2 = JsonPath.parse(r).read("$.data.id", Long.class);

        // update
        mvc.perform(put(path + "/" + id1)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content("{\"name\":\"11\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").isNumber());

        // findAll
        mvc.perform(get(path)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta").doesNotExist())
                .andExpect(jsonPath("$.items.length()").value(4));

        // findOne
        mvc.perform(get(path + "/" + id1)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value("11"));

        // batch update
        mvc.perform(post(path+"/batch")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content("{" +
                                "  \"changes\": [" +
                                "    {" +
                                "      \"id\": " + id1 + "," +
                                "      \"data\": {" +
                                "        \"name\": \"12\"" +
                                "      }" +
                                "    }," +
                                "    {" +
                                "      \"id\": " + id2 + "," +
                                "      \"data\": {" +
                                "        \"name\": \"22\"" +
                                "      }" +
                                "    }" +
                                "  ]" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.length()").value(2));
    }

}
