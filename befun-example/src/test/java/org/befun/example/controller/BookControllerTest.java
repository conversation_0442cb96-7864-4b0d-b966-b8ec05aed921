package org.befun.example.controller;

import org.befun.core.constant.ErrorCode;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.example.BaseTest;
import org.befun.example.TestRedisConfiguration;
import org.befun.example.entity.*;
import org.junit.Assert;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BookControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Test
    public void countBooksWithToken() throws Exception {
        mvc.perform(get("/books")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(2))
                .andExpect(jsonPath("$.items.length()").value(2));
    }


    @Test
    public void queryBooksWithQ() throws Exception {
        mvc.perform(get("/books?_q=b1")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(1))
                .andExpect(jsonPath("$.items.length()").value(1));
    }

    @Test
    public void queryBooksWithTitle() throws Exception {
        mvc.perform(get("/books?title=b1")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(1))
                .andExpect(jsonPath("$.items.length()").value(1));
    }

    @Test
    public void queryBooksWithType() throws Exception {
        mvc.perform(get("/books?bookType_neq=NOVEL")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(1))
                .andExpect(jsonPath("$.items.length()").value(1));
    }

    @Test
    public void createBook() throws Exception {
        BookDto change = new BookDto();
        String title = "new title";
        change.setTitle(title);
        change.setMeta(new Book.BookMeta());
        change.setLabels(List.of(""));

        mvc.perform(post("/books/")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(title));

        Assert.assertEquals(5, bookRepository.count());
    }

    @Test
    public void createBookWithCategory() throws Exception {
        BookDto change = new BookDto();
        String title = "new title";
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setId(c1.getId());
        change.setCategory(categoryDto);
        change.setTitle(title);
        change.setTags(Arrays.asList(10L, 20L, 30L));

        mvc.perform(post("/books/")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(title))
                .andExpect(jsonPath("$.data.category.id").value(c1.getId()))
                .andExpect(jsonPath("$.data.tags.length()").value(3));

        Assert.assertEquals(5, bookRepository.count());
        Assert.assertEquals(2, categoryRepository.count());
    }

    @Test
    public void createBookWithInvalidCategory() throws Exception {
        BookDto change = new BookDto();
        String title = "new title";
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setId(9999L);
        change.setCategory(categoryDto);
        change.setTitle(title);
        mvc.perform(post("/books/")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
    }

    @Test
    public void updateBookTitle() throws Exception {
        BookDto change = new BookDto();
        String title = "new title";
        change.setTitle(title);

        mvc.perform(put("/books/" + b1.getId())
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(title));

        Book newBook = bookRepository.findById(b1.getId()).get();
        Assert.assertEquals(title, newBook.getTitle());
    }

    @Test
    public void updateBookTagToEmptyList() throws Exception {
        BookDto change = new BookDto();
        change.setTags(Arrays.asList());

        ResultActions response = mvc.perform(put("/books/" + b1.getId())
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(change))
                .contentType(MediaType.APPLICATION_JSON));

        response.andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.tags.length()").value(0));

        Book newBook = bookRepository.findById(b1.getId()).get();
        Assert.assertEquals(0, newBook.getTags().size());
    }

    @Test
    public void updateIdWillBeIgnore() throws Exception {
        BookDto change = new BookDto();
        Long id = 100L;
        change.setId(id);
        // update one 的时候，如果参数中有id属性，则会忽略它，其他属性不影响
        mvc.perform(put("/books/" + b1.getId())
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));

    }

    @Test
    public void createArticle() throws Exception {
        String newTitle = "new title";
        ArticleDto change = new ArticleDto();
        change.setTitle(newTitle);

        mvc.perform(post("/books/" + b1.getId() + "/articles/")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(newTitle));

        Book nb1 = bookRepository.findById(b1.getId()).get();
        Assert.assertEquals(3, nb1.getArticles().size());
    }

    @Test
    public void updateArticle() throws Exception {
        String newTitle = "new title";
        ArticleDto change = new ArticleDto();
        change.setTitle(newTitle);

        mvc.perform(put("/books/" + b1.getId() + "/articles/" + b1a2.getId())
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(change))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(newTitle));

        Book nb1 = bookRepository.findById(b1.getId()).get();
        Assert.assertEquals(2, nb1.getArticles().size());
        Article nb1a2 = articleRepository.findById(b1a2.getId()).get();
        Assert.assertEquals(newTitle, nb1a2.getTitle());
    }

    @Test
    public void batchUpdateArticle() throws Exception {
        ArticleDto change1 = new ArticleDto();
        change1.setId(b1a1.getId());
        change1.setTitle("new a1");
        ArticleDto change2 = new ArticleDto();
        change2.setId(b1a2.getId());
        change2.setTitle("new a2");
        ResourceBatchUpdateRequestDto<ArticleDto> changeDto = new ResourceBatchUpdateRequestDto<>(
                Arrays.asList(change1, change2)
        );

        mvc.perform(post("/books/" + b1.getId() + "/articles/batch")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(changeDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.length()").value(2));

        Book nb1 = bookRepository.findById(b1.getId()).get();
        Assert.assertEquals(2, nb1.getArticles().size());
        Article nb1a1 = articleRepository.findById(b1a1.getId()).get();
        Assert.assertEquals("new a1", nb1a1.getTitle());
        Article nb1a2 = articleRepository.findById(b1a2.getId()).get();
        Assert.assertEquals("new a2", nb1a2.getTitle());
    }


    @Test
    public void deleteArticle() throws Exception {
        mvc.perform(delete("/books/" + b1.getId() + "/articles/" + b1a2.getId())
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Book nb1 = bookRepository.findById(b1.getId()).get();
        Assert.assertEquals(1, nb1.getArticles().size());
    }

    @Test
    @Disabled
    public void deleteWrongArticleLeadError() throws Exception {
        mvc.perform(delete("/books/" + b1.getId() + "/articles/" + b2a1.getId())
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(ErrorCode.ENTITY_NOT_FOUND.getValue()));
    }

//    @Test
//    @Disabled
//    public void softDeleteBook() throws Exception {
//        mvc.perform(delete("/books/" + b1.getId())
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
//
//        Session session = this.entityManager.unwrap(Session.class);
//        Object result = session.createSQLQuery("select count(*) from book").getSingleResult();
//        Assert.assertEquals(2, Integer.valueOf(result.toString()).intValue());
//        List<Book> books = bookRepository.findAll();
//        Assert.assertEquals(1, books.size());
//    }
}
