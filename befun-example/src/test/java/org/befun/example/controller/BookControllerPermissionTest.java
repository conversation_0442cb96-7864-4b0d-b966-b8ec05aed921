package org.befun.example.controller;

import org.befun.example.BaseTest;
import org.befun.example.TestRedisConfiguration;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BookControllerPermissionTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Test
    public void queryBooksWithoutTokenShouldBeRejected() throws Exception {
        mvc.perform(get("/books")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
    }

    @Test
    public void queryCorpedBooks() throws Exception {
        mvc.perform(get("/books")
                .header(HttpHeaders.AUTHORIZATION, mock_token_3)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(1))
                .andExpect(jsonPath("$.items.length()").value(1));
    }

    @Test
    public void queryAllBooksWithAdmin() throws Exception {
        mvc.perform(get("/books")
                .header(HttpHeaders.AUTHORIZATION, mock_token_admin_1)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(2))
                .andExpect(jsonPath("$.items.length()").value(2));
    }

}
