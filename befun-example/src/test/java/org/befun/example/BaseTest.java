package org.befun.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.befun.core.dto.UserDto;
import org.befun.core.entity.ResourcePermission;
import org.befun.core.repo.ResourcePermissionRepository;
import org.befun.example.constant.BookType;
import org.befun.example.entity.Article;
import org.befun.example.entity.Book;
import org.befun.example.entity.Category;
import org.befun.example.entity.Folder;
import org.befun.example.repository.ArticleRepository;
import org.befun.example.repository.BookRepository;
import org.befun.example.repository.CategoryRepository;
import org.befun.example.repository.FolderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class BaseTest {
    @Autowired
    protected EntityManager entityManager;

    @Autowired
    private RedisTemplate<String, String> template;

    @Autowired
    protected DataSource dataSource;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected BookRepository bookRepository;

    @Autowired
    protected ArticleRepository articleRepository;

    @Autowired
    protected CategoryRepository categoryRepository;

    @Autowired
    protected FolderRepository folderRepository;

    @Autowired
    protected ResourcePermissionRepository permissionRepository;

    protected Book b1;
    protected Book b2;
    protected Book b3;
    protected Book b4;

    protected Article b1a1;
    protected Article b1a2;
    protected Article b2a1;

    protected Category c1;
    protected Category c2;

    protected Folder f1;
    protected Folder f2;
    protected Folder f3;
    protected Folder f4;

    protected Long tag1 = 1L;
    protected Long tag2 = 2L;
    protected Long tag3 = 3L;

    protected ResourcePermission p1;

    protected Long mock_org_1_id = 1L;
    protected Long mock_org_2_id = 2L;
    protected String mock_token_1 = "fake_token_1";
    protected String mock_token_2 = "fake_token_2";
    protected String mock_token_3 = "fake_token_3";
    protected String mock_token_admin_1 = "fake_token_4";
    protected UserDto mock_user_1;
    protected UserDto mock_admin_1;
    protected UserDto mock_user_2;
    protected UserDto mock_user_3; // same org with org user 1

    /**
     * u1 -> b1, b2
     * u2 -> b3, b4
     * <p>
     * b1 ->
     * - articles: b1a1, b1a2
     * - type: novel
     * <p>
     * b2 ->
     * - articles: b2a1, b2a2
     * - type: poem
     */
    @BeforeEach
    @Transactional
    public void setup() {

        f1 = Folder.builder().name("f1").build();
        f2 = Folder.builder().name("f2").build();
        f3 = Folder.builder().name("f3").build();
        f4 = Folder.builder().name("f4").build();
        f1 = folderRepository.save(f1);
        f2 = folderRepository.save(f2);
        f3.setParentId(f1.getId());
        f4.setParentId(f1.getId());
        folderRepository.save(f3);
        folderRepository.save(f4);

        c1 = Category.builder().name("c1").build();
        c2 = Category.builder().name("c2").build();
        categoryRepository.save(c1);
        categoryRepository.save(c2);

        mock_user_1 = new UserDto()
                .builder()
                .orgId(mock_org_1_id)
                .id(1L)
                .isAdmin(false)
                .username("u1")
                .build();
        mock_user_2 = new UserDto()
                .builder()
                .orgId(mock_org_2_id)
                .id(2L)
                .username("u2")
                .isAdmin(false)
                .build();
        mock_user_3 = new UserDto()
                .builder()
                .orgId(mock_org_1_id)
                .id(3L)
                .username("u3")
                .isAdmin(false)
                .build();

        mock_admin_1 = new UserDto()
                .builder()
                .orgId(mock_org_1_id)
                .id(4L)
                .username("a1")
                .isAdmin(true)
                .build();

        b1 = new Book().toBuilder()
                .title("b1")
                .category(c1)
                .tags(Arrays.asList(tag1, tag2))
                .bookType(BookType.NOVEL)
                .description("b1 description")
                .build();
        b1.setUserId(mock_user_1.getId());
        b1.setOrgId(mock_user_1.getOrgId());
        b1a1 = new Article().toBuilder()
                .book(b1)
                .title("article 1")
                .build();
        b1a2 = new Article().toBuilder()
                .book(b1)
                .title("article 2")
                .build();

        b1.getArticles().add(b1a1);
        b1.getArticles().add(b1a2);

        b2 = new Book().toBuilder()
                .title("b2")
                .category(c2)
                .tags(Arrays.asList(tag3))
                .bookType(BookType.POEM)
                .description("b2 description")
                .build();
        b2.setUserId(mock_user_1.getId());
        b2.setOrgId(mock_user_1.getOrgId());
        b2a1 = Article.builder()
                .book(b2)
                .title("title 2")
                .build();
        b2.getArticles().add(b2a1);

        b3 = new Book().toBuilder()
                .title("b3")
                .category(c1)
                .bookType(BookType.NOVEL)
                .description("b3 description")
                .build();
        b3.setUserId(mock_user_2.getId());
        b3.setOrgId(mock_user_2.getOrgId());

        b4 = new Book().toBuilder()
                .title("b4")
                .category(c1)
                .bookType(BookType.NOVEL)
                .description("b4 description")
                .build();
        b4.setUserId(mock_user_2.getId());
        b4.setOrgId(mock_user_2.getOrgId());

        bookRepository.save(b1);
        bookRepository.save(b2);
        bookRepository.save(b3);
        bookRepository.save(b4);

        p1 = ResourcePermission.builder()
                .resourceType("BOOK")
                .userId(mock_user_3.getId())
                .resourceId(b1.getId())
                .build();
        p1.setOrgId(mock_org_1_id);
        permissionRepository.save(p1);

        saveToken(mock_token_1, mock_user_1);
        saveToken(mock_token_2, mock_user_2);
        saveToken(mock_token_3, mock_user_3);
        saveToken(mock_token_admin_1, mock_admin_1);
    }

    private void saveToken(String token, UserDto user) {
        String roleIds = null;
        if (user.getRoleIds() != null) {
            roleIds = String.join(",", user.getRoleIds().stream().map(x -> x.toString()).collect(Collectors.toList()));
        }
        Map<String, Object> value = new HashMap<>();
        value.put("orgId", user.getOrgId().toString());
        value.put("userId", user.getId().toString());
        value.put("username", user.getUsername());
        value.put("roleId", roleIds);
        value.put("is_admin", user.getIsAdmin() ? "1" : "0");
        template.opsForHash().putAll("session:" + token, value);
    }
}
